package environment

import (
	"context"
	"encoding/base64"
	"os"
	"testing"
	"time"
)

func TestBinaryStrategy_CanHandle(t *testing.T) {
	strategy := NewBinaryStrategy()

	tests := []struct {
		depType  string
		expected bool
	}{
		{"binary", true},
		{"Binary", false}, // 区分大小写
		{"file", false},
		{"directory", false},
		{"db", false},
		{"url", false},
		{"", false},
	}

	for _, test := range tests {
		t.Run(test.depType, func(t *testing.T) {
			result := strategy.CanHandle(test.depType)
			if result != test.expected {
				t.Errorf("CanHandle(%q) = %v, expected %v", test.depType, result, test.expected)
			}
		})
	}
}

func TestBinaryStrategy_GetName(t *testing.T) {
	strategy := NewBinaryStrategy()
	expected := "BinaryStrategy"

	result := strategy.GetName()
	if result != expected {
		t.<PERSON><PERSON>("GetName() = %q, expected %q", result, expected)
	}
}

func TestBinaryStrategy_Initialize_EmptyContent(t *testing.T) {
	strategy := NewBinaryStrategy()
	dep := EnvironmentDependency{
		Type:    "binary",
		Path:    "./test_binary.bin",
		Content: "",
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for empty Base64 content, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}
}

func TestBinaryStrategy_Initialize_InvalidBase64(t *testing.T) {
	strategy := NewBinaryStrategy()
	dep := EnvironmentDependency{
		Type:    "binary",
		Path:    "./test_binary.bin",
		Content: "invalid-base64-content!@#$",
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for invalid Base64 content, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}
}

func TestBinaryStrategy_Initialize_Success(t *testing.T) {
	strategy := NewBinaryStrategy()

	// 创建测试二进制数据
	testData := []byte{0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A} // PNG文件头
	base64Data := base64.StdEncoding.EncodeToString(testData)

	testFile := "./test_binary_" + time.Now().Format("20060102150405") + ".png"
	defer os.Remove(testFile)

	dep := EnvironmentDependency{
		Type:    "binary",
		Path:    testFile,
		Content: base64Data,
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true, got false. Error: %s", result.ErrorMessage)
	}

	// 验证文件是否创建
	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("Expected file to be created, but it doesn't exist")
	}

	// 验证文件内容
	fileContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read created file: %v", err)
	}

	if len(fileContent) != len(testData) {
		t.Errorf("File size mismatch. Expected %d bytes, got %d bytes", len(testData), len(fileContent))
	}

	for i, b := range testData {
		if i < len(fileContent) && fileContent[i] != b {
			t.Errorf("File content mismatch at byte %d. Expected 0x%02X, got 0x%02X", i, b, fileContent[i])
		}
	}
}

func TestBinaryStrategy_Initialize_ExistingFile(t *testing.T) {
	strategy := NewBinaryStrategy()

	testFile := "./existing_binary_" + time.Now().Format("20060102150405") + ".bin"
	defer os.Remove(testFile)

	// 创建已存在的文件
	existingContent := []byte("existing file content")
	if err := os.WriteFile(testFile, existingContent, 0644); err != nil {
		t.Fatalf("Failed to create existing file: %v", err)
	}

	testData := []byte{0x01, 0x02, 0x03, 0x04}
	base64Data := base64.StdEncoding.EncodeToString(testData)

	dep := EnvironmentDependency{
		Type:    "binary",
		Path:    testFile,
		Content: base64Data,
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error for existing file, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true for existing file, got false. Error: %s", result.ErrorMessage)
	}

	// 验证文件内容没有被修改（因为文件已存在）
	fileContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read existing file: %v", err)
	}

	if string(fileContent) != string(existingContent) {
		t.Error("Existing file content should not be modified")
	}
}

func TestBinaryStrategy_Initialize_CreateDirectory(t *testing.T) {
	strategy := NewBinaryStrategy()

	testDir := "./test_bin_dir_" + time.Now().Format("20060102150405")
	testFile := testDir + "/test.bin"
	defer os.RemoveAll(testDir)

	testData := []byte{0xFF, 0xFE, 0xFD, 0xFC}
	base64Data := base64.StdEncoding.EncodeToString(testData)

	dep := EnvironmentDependency{
		Type:    "binary",
		Path:    testFile,
		Content: base64Data,
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true, got false. Error: %s", result.ErrorMessage)
	}

	// 验证目录和文件都被创建
	if _, err := os.Stat(testDir); os.IsNotExist(err) {
		t.Error("Expected directory to be created, but it doesn't exist")
	}

	if _, err := os.Stat(testFile); os.IsNotExist(err) {
		t.Error("Expected file to be created, but it doesn't exist")
	}

	// 验证文件内容
	fileContent, err := os.ReadFile(testFile)
	if err != nil {
		t.Fatalf("Failed to read created file: %v", err)
	}

	for i, b := range testData {
		if i < len(fileContent) && fileContent[i] != b {
			t.Errorf("File content mismatch at byte %d. Expected 0x%02X, got 0x%02X", i, b, fileContent[i])
		}
	}
}
