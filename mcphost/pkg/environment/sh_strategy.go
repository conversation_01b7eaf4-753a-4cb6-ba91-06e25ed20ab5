package environment

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// ShellConfig 表示shell脚本配置
type ShellConfig struct {
	Content string `json:"content"` // 脚本内容
	Cmd     string `json:"cmd"`     // 可选的自定义执行命令
}

// ShellStrategy shell脚本执行策略
type ShellStrategy struct{}

// NewShellStrategy 创建shell策略实例
func NewShellStrategy() *ShellStrategy {
	return &ShellStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *ShellStrategy) CanHandle(depType string) bool {
	return depType == "sh"
}

// GetName 获取策略名称
func (s *ShellStrategy) GetName() string {
	return "ShellStrategy"
}

// Initialize 执行shell脚本
func (s *ShellStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	if dep.Content == "" {
		result.ErrorMessage = "脚本内容不能为空"
		return result, fmt.Errorf("脚本内容不能为空")
	}

	// 解析脚本配置
	config, err := s.parseShellConfig(dep.Content)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析脚本配置失败: %v", err)
		return result, fmt.Errorf("解析脚本配置失败: %w", err)
	}

	if config.Content == "" {
		result.ErrorMessage = "脚本内容不能为空"
		return result, fmt.Errorf("脚本内容不能为空")
	}

	// 转换为相对路径（移除开头的斜杠）
	scriptPath := dep.Path
	if strings.HasPrefix(scriptPath, "/") {
		scriptPath = "." + scriptPath
	}

	// 验证脚本路径安全性
	if err := s.validateScriptPath(scriptPath); err != nil {
		result.ErrorMessage = fmt.Sprintf("脚本路径不安全: %v", err)
		return result, fmt.Errorf("脚本路径不安全: %w", err)
	}

	// 创建脚本目录（如果需要）
	dir := filepath.Dir(scriptPath)
	if dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			result.ErrorMessage = fmt.Sprintf("创建脚本目录失败: %v", err)
			return result, fmt.Errorf("创建脚本目录失败 %s: %w", dir, err)
		}
	}

	// 创建临时脚本文件
	if err := os.WriteFile(scriptPath, []byte(config.Content), 0755); err != nil {
		result.ErrorMessage = fmt.Sprintf("创建脚本文件失败: %v", err)
		return result, fmt.Errorf("创建脚本文件失败 %s: %w", scriptPath, err)
	}

	// 确保脚本文件在函数结束时被删除
	defer func() {
		if removeErr := os.Remove(scriptPath); removeErr != nil {
			// 记录清理失败，但不影响主要结果
			if result.Details != "" {
				result.Details += "\n"
			}
			result.Details += fmt.Sprintf("警告: 清理脚本文件失败: %v", removeErr)
		}
	}()

	// 执行脚本
	startTime := time.Now()
	var cmd *exec.Cmd
	var cmdStr string

	if config.Cmd != "" {
		// 使用自定义命令
		cmdStr = config.Cmd
		cmd = exec.CommandContext(ctx, "sh", "-c", config.Cmd)
	} else {
		// 使用默认命令: bash {path}
		cmdStr = fmt.Sprintf("sh %s", scriptPath)
		cmd = exec.CommandContext(ctx, "sh",  scriptPath)
	}

	// 执行命令并捕获输出
	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime)
	
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("脚本执行失败: %v", err)
		result.Details = fmt.Sprintf("执行命令: %s\n执行时间: %s\n输出: %s",
			cmdStr, duration, string(output))
		return result, fmt.Errorf("脚本执行失败: %w\n命令: %s\n输出: %s", err, cmdStr, string(output))
	}

	// 执行成功
	result.Success = true
	result.Details = fmt.Sprintf("脚本执行成功\n执行命令: %s\n执行时间: %s\n输出: %s",
		cmdStr, duration, string(output))

	return result, nil
}

// parseShellConfig 解析shell配置，支持JSON格式和纯文本格式
func (s *ShellStrategy) parseShellConfig(content string) (*ShellConfig, error) {
	// 尝试解析为JSON
	var config ShellConfig
	if err := json.Unmarshal([]byte(content), &config); err == nil {
		// JSON解析成功
		return &config, nil
	}

	// JSON解析失败，当作纯文本脚本内容处理
	return &ShellConfig{
		Content: content,
		Cmd:     "", // 没有自定义命令
	}, nil
}

// validateScriptPath 验证脚本路径的安全性
func (s *ShellStrategy) validateScriptPath(path string) error {
	// 检查路径是否包含危险字符
	if strings.Contains(path, "..") {
		return fmt.Errorf("路径不能包含 '..'")
	}

	// 检查是否为绝对路径（应该已经被转换为相对路径）
	if filepath.IsAbs(path) {
		return fmt.Errorf("不允许使用绝对路径")
	}

	// 检查路径是否为空或只包含空白字符
	if strings.TrimSpace(path) == "" {
		return fmt.Errorf("路径不能为空")
	}

	return nil
}
