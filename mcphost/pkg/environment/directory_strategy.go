package environment

import (
	"context"
	"fmt"
	"os"
	"strings"
)

// DirectoryStrategy 目录创建策略
type DirectoryStrategy struct{}

// NewDirectoryStrategy 创建目录策略实例
func NewDirectoryStrategy() *DirectoryStrategy {
	return &DirectoryStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *DirectoryStrategy) CanHandle(depType string) bool {
	return depType == "directory" || depType == "dir"
}

// GetName 获取策略名称
func (s *DirectoryStrategy) GetName() string {
	return "DirectoryStrategy"
}

// Initialize 执行目录创建
func (s *DirectoryStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	// 转换为相对路径（移除开头的斜杠）
	dirPath := dep.Path
	if strings.HasPrefix(dirPath, "/") {
		dirPath = "." + dirPath
	}

	// 检查目录是否已存在
	if info, err := os.Stat(dirPath); err == nil {
		if info.IsDir() {
			result.Success = true
			result.Details = fmt.Sprintf("目录已存在: %s", dirPath)
			return result, nil
		} else {
			return result, fmt.Errorf("路径存在但不是目录: %s", dirPath)
		}
	}

	// 创建目录
	if err := os.MkdirAll(dirPath, 0755); err != nil {
		result.ErrorMessage = fmt.Sprintf("创建目录失败: %v", err)
		return result, fmt.Errorf("创建目录失败 %s: %w", dirPath, err)
	}

	// 验证目录是否创建成功
	if info, err := os.Stat(dirPath); err != nil {
		result.ErrorMessage = "目录创建后验证失败"
		return result, fmt.Errorf("目录创建后验证失败: %w", err)
	} else if !info.IsDir() {
		result.ErrorMessage = "创建的路径不是目录"
		return result, fmt.Errorf("创建的路径不是目录: %s", dirPath)
	}

	result.Success = true
	result.Details = fmt.Sprintf("成功创建目录: %s, 权限: 0755", dirPath)

	return result, nil
}
