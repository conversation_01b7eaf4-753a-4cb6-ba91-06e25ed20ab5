package environment

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"
)

// ExampleDefaultManager 展示如何使用默认管理器
func ExampleDefaultManager() {
	// 创建环境管理器
	manager := DefaultManager()

	// 定义环境依赖
	deps := []EnvironmentDependency{
		{
			Path: "./example_data",
			Type: "directory",
		},
		{
			Path:    "./example_data/config.txt",
			Type:    "file",
			Content: "# 配置文件\ndebug=true\nport=8080\n",
		},
		{
			Path: "./example_data/app.db",
			Type: "db",
			Content: `
				-- 用户表
				CREATE TABLE users (
					id INTEGER PRIMARY KEY AUTOINCREMENT,
					username VARCHAR(50) UNIQUE NOT NULL,
					email VARCHAR(100),
					created_at DATETIME DEFAULT CURRENT_TIMESTAMP
				);
				
				-- 插入示例数据
				INSERT INTO users (username, email) VALUES 
					('admin', '<EMAIL>'),
					('user1', '<EMAIL>');
				
				-- 设置表
				CREATE TABLE settings (
					key VARCHAR(50) PRIMARY KEY,
					value TEXT
				);
				
				INSERT INTO settings (key, value) VALUES 
					('app_name', 'Example App'),
					('version', '1.0.0');
			`,
		},
		{
			Path:    "./example_data/logo.png",
			Type:    "binary",
			Content: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9YOMo3gAAAABJRU5ErkJggg==",
		},
	}

	// 执行初始化
	ctx := context.Background()
	results, err := manager.Initialize(ctx, deps)
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	// 显示结果
	fmt.Printf("=== 环境初始化结果 ===\n")
	for i, result := range results {
		status := "✅"
		if !result.Success {
			status = "❌"
		}

		fmt.Printf("%s [%d] %s (%s)\n", status, i+1, result.Path, result.Type)
	}

	// 清理示例文件
	defer func() {
		os.RemoveAll("./example_data")
	}()

	// Output:
	// === 环境初始化结果 ===
	// ✅ [1] ./example_data (directory)
	// ✅ [2] ./example_data/config.txt (file)
	// ✅ [3] ./example_data/app.db (db)
	// ✅ [4] ./example_data/logo.png (binary)
}

// JSONConfigStrategy 自定义策略示例：创建JSON配置文件
type JSONConfigStrategy struct{}

func (s *JSONConfigStrategy) CanHandle(depType string) bool {
	return depType == "json_config"
}

func (s *JSONConfigStrategy) GetName() string {
	return "JSONConfigStrategy"
}

func (s *JSONConfigStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	// 简单的JSON格式验证（这里简化处理）
	if !isValidJSON(dep.Content) {
		return result, fmt.Errorf("无效的JSON内容")
	}

	// 创建文件
	if err := os.WriteFile(dep.Path, []byte(dep.Content), 0644); err != nil {
		return result, fmt.Errorf("创建JSON文件失败: %w", err)
	}

	result.Success = true
	result.Details = fmt.Sprintf("JSON配置文件创建成功: %s", dep.Path)
	return result, nil
}

// 简单的JSON验证（实际应该使用json.Valid）
func isValidJSON(content string) bool {
	return len(content) > 0 &&
		(content[0] == '{' || content[0] == '[')
}

// ExampleJSONConfigStrategy 展示如何创建和使用自定义策略
func ExampleJSONConfigStrategy() {
	// 创建管理器并注册自定义策略
	manager := NewManager()
	manager.RegisterStrategy(&JSONConfigStrategy{})

	// 使用自定义策略
	dep := EnvironmentDependency{
		Path:    "./custom_config.json",
		Type:    "json_config",
		Content: `{"name": "自定义配置", "enabled": true}`,
	}

	result, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep})
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	if len(result) > 0 && result[0].Success {
		fmt.Println("✅ 自定义策略执行成功")
		fmt.Printf("详情: %s\n", result[0].Details)
	} else {
		fmt.Println("❌ 自定义策略执行失败")
	}

	// 清理
	defer os.Remove("./custom_config.json")
}

// ExampleShellStrategy 展示如何使用Shell策略
func ExampleShellStrategy() {
	// 创建管理器
	manager := NewManager()
	manager.RegisterStrategy(NewShellStrategy())

	// 示例1: 使用纯文本脚本内容
	fmt.Println("=== 示例1: 纯文本脚本 ===")
	dep1 := EnvironmentDependency{
		Path:    "./hello.sh",
		Type:    "sh",
		Content: "#!/bin/bash\necho 'Hello from shell script!'",
	}

	result1, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep1})
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	if len(result1) > 0 && result1[0].Success {
		fmt.Println("✅ 纯文本脚本执行成功")
	}

	// 示例2: 使用JSON配置和自定义命令
	fmt.Println("\n=== 示例2: JSON配置与自定义命令 ===")
	config := map[string]interface{}{
		"content": "#!/bin/bash\necho \"参数: $1 $2\"",
		"cmd":     "bash ./greet.sh World 2024",
	}

	configJSON, _ := json.Marshal(config)

	dep2 := EnvironmentDependency{
		Path:    "./greet.sh",
		Type:    "sh",
		Content: string(configJSON),
	}

	result2, err := manager.Initialize(context.Background(), []EnvironmentDependency{dep2})
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	if len(result2) > 0 && result2[0].Success {
		fmt.Println("✅ JSON配置脚本执行成功")
	}

	// Output:
	// === 示例1: 纯文本脚本 ===
	// ✅ 纯文本脚本执行成功
	//
	// === 示例2: JSON配置与自定义命令 ===
	// ✅ JSON配置脚本执行成功
}

// DemoBenchJSONProcessing 展示如何处理bench.json格式的环境依赖
func DemoBenchJSONProcessing() {
	// 模拟从bench.json读取的环境依赖数据
	benchEnvDeps := []struct {
		Path    string `json:"path"`
		Type    string `json:"type"`
		Content string `json:"content"`
	}{
		{
			Path: "orders.db",
			Type: "db",
			Content: `
				CREATE TABLE customers (
					customer_id INTEGER PRIMARY KEY,
					customer_name VARCHAR(100),
					email VARCHAR(100) UNIQUE
				);
				
				INSERT INTO customers (customer_name, email) VALUES 
					('张三', '<EMAIL>'),
					('李四', '<EMAIL>');
			`,
		},
	}

	// 转换为environment包的格式
	var envDeps []EnvironmentDependency
	for _, dep := range benchEnvDeps {
		envDeps = append(envDeps, EnvironmentDependency{
			Path:    dep.Path,
			Type:    dep.Type,
			Content: dep.Content,
		})
	}

	// 使用默认管理器初始化
	manager := DefaultManager()
	results, err := manager.Initialize(context.Background(), envDeps)
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	// 显示结果
	for _, result := range results {
		if result.Success {
			fmt.Printf("✅ %s 初始化成功\n", result.Path)
		} else {
			fmt.Printf("❌ %s 初始化失败: %s\n", result.Path, result.ErrorMessage)
		}
	}

	// 清理
	defer os.Remove("orders.db")

	// Output:
	// ✅ orders.db 初始化成功
}

// DemoPerformanceMonitoring 展示性能监控功能
func DemoPerformanceMonitoring() {
	manager := DefaultManager()

	// 创建一个相对复杂的环境设置
	deps := []EnvironmentDependency{
		{Path: "./perf_test", Type: "directory"},
		{Path: "./perf_test/large_file.txt", Type: "file", Content: generateLargeContent()},
		{Path: "./perf_test/complex.db", Type: "db", Content: generateComplexSQL()},
	}

	start := time.Now()
	results, err := manager.Initialize(context.Background(), deps)
	totalDuration := time.Since(start)

	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}

	fmt.Printf("=== 性能报告 ===\n")
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("任务数量: %d\n", len(results))

	for i, result := range results {
		fmt.Printf("[%d] %s: %v\n", i+1, result.Type, result.Duration)
	}

	// 清理
	defer os.RemoveAll("./perf_test")
}

func generateLargeContent() string {
	content := "这是一个大文件的内容\n"
	result := ""
	for i := 0; i < 1000; i++ {
		result += fmt.Sprintf("%s第%d行\n", content, i)
	}
	return result
}

func generateComplexSQL() string {
	return `
		-- 复杂的数据库结构
		CREATE TABLE IF NOT EXISTS products (
			id INTEGER PRIMARY KEY,
			name VARCHAR(200),
			price DECIMAL(10,2),
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS orders (
			id INTEGER PRIMARY KEY,
			product_id INTEGER,
			quantity INTEGER,
			FOREIGN KEY (product_id) REFERENCES products(id)
		);
		
		-- 插入测试数据
		INSERT INTO products (name, price) VALUES 
			('产品A', 99.99),
			('产品B', 199.99),
			('产品C', 299.99);
			
		INSERT INTO orders (product_id, quantity) VALUES 
			(1, 2),
			(2, 1),
			(3, 3);
	`
}
