package environment

import (
	"context"
	"encoding/json"
	"os"
	"strings"
	"testing"
	"time"
)

func TestShellStrategy_CanHandle(t *testing.T) {
	strategy := NewShellStrategy()

	tests := []struct {
		depType  string
		expected bool
	}{
		{"sh", true},
		{"SH", false}, // 区分大小写
		{"shell", false},
		{"bash", false},
		{"file", false},
		{"directory", false},
		{"", false},
	}

	for _, test := range tests {
		t.Run(test.depType, func(t *testing.T) {
			result := strategy.CanHandle(test.depType)
			if result != test.expected {
				t.Errorf("CanHandle(%q) = %v, expected %v", test.depType, result, test.expected)
			}
		})
	}
}

func TestShellStrategy_GetName(t *testing.T) {
	strategy := NewShellStrategy()
	expected := "ShellStrategy"

	result := strategy.GetName()
	if result != expected {
		t.<PERSON><PERSON><PERSON>("GetName() = %q, expected %q", result, expected)
	}
}

func TestShellStrategy_Initialize_EmptyContent(t *testing.T) {
	strategy := NewShellStrategy()
	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    "./test.sh",
		Content: "",
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for empty content, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}
}

func TestShellStrategy_Initialize_PlainTextScript(t *testing.T) {
	strategy := NewShellStrategy()

	scriptContent := "#!/bin/bash\necho 'Hello World'\nexit 0"
	testScript := "./test_plain_" + time.Now().Format("20060102150405") + ".sh"

	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    testScript,
		Content: scriptContent,
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true, got false. Error: %s", result.ErrorMessage)
	}

	if result.Details == "" {
		t.Error("Expected details, got empty string")
	}

	// 验证脚本文件已被清理
	if _, err := os.Stat(testScript); err == nil {
		t.Error("Expected script file to be cleaned up, but it still exists")
	}

	// 验证输出包含预期内容
	if !strings.Contains(result.Details, "Hello World") {
		t.Errorf("Expected output to contain 'Hello World', got: %s", result.Details)
	}
}

func TestShellStrategy_Initialize_JSONConfigWithCustomCmd(t *testing.T) {
	strategy := NewShellStrategy()

	testScript := "./test_custom_" + time.Now().Format("20060102150405") + ".sh"

	config := ShellConfig{
		Content: "#!/bin/bash\necho 'Custom Command Test'\necho \"Args: $@\"",
		Cmd:     "bash " + testScript + " arg1 arg2",
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config: %v", err)
	}

	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    testScript,
		Content: string(configJSON),
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true, got false. Error: %s", result.ErrorMessage)
	}

	// 验证使用了自定义命令
	expectedCmd := "bash " + testScript + " arg1 arg2"
	if !strings.Contains(result.Details, expectedCmd) {
		t.Errorf("Expected details to contain custom command '%s', got: %s", expectedCmd, result.Details)
	}

	// 验证脚本文件已被清理
	if _, err := os.Stat(testScript); err == nil {
		t.Error("Expected script file to be cleaned up, but it still exists")
	}
}

func TestShellStrategy_Initialize_JSONConfigWithoutCmd(t *testing.T) {
	strategy := NewShellStrategy()

	config := ShellConfig{
		Content: "#!/bin/bash\necho 'JSON without custom command'",
	}

	configJSON, err := json.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal config: %v", err)
	}

	testScript := "./test_json_" + time.Now().Format("20060102150405") + ".sh"

	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    testScript,
		Content: string(configJSON),
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if !result.Success {
		t.Errorf("Expected Success to be true, got false. Error: %s", result.ErrorMessage)
	}

	// 验证使用了默认命令
	expectedCmd := "bash " + testScript
	if !strings.Contains(result.Details, expectedCmd) {
		t.Errorf("Expected details to contain default command '%s', got: %s", expectedCmd, result.Details)
	}

	// 验证脚本文件已被清理
	if _, err := os.Stat(testScript); err == nil {
		t.Error("Expected script file to be cleaned up, but it still exists")
	}
}

func TestShellStrategy_Initialize_ScriptFailure(t *testing.T) {
	strategy := NewShellStrategy()

	// 创建一个会失败的脚本
	scriptContent := "#!/bin/bash\necho 'This will fail'\nexit 1"
	testScript := "./test_fail_" + time.Now().Format("20060102150405") + ".sh"

	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    testScript,
		Content: scriptContent,
	}

	ctx := context.Background()
	result, err := strategy.Initialize(ctx, dep)

	if err == nil {
		t.Error("Expected error for failing script, got nil")
	}

	if result == nil {
		t.Fatal("Expected result, got nil")
	}

	if result.Success {
		t.Error("Expected Success to be false for failing script")
	}

	if result.ErrorMessage == "" {
		t.Error("Expected error message, got empty string")
	}

	// 验证脚本文件已被清理（即使执行失败）
	if _, err := os.Stat(testScript); err == nil {
		t.Error("Expected script file to be cleaned up even after failure, but it still exists")
	}
}

func TestShellStrategy_ValidateScriptPath(t *testing.T) {
	strategy := NewShellStrategy()

	tests := []struct {
		path        string
		expectError bool
		description string
	}{
		{"test.sh", false, "simple filename"},
		{"./test.sh", false, "relative path with dot"},
		{"dir/test.sh", false, "subdirectory path"},
		{"../test.sh", true, "parent directory traversal"},
		{"dir/../test.sh", true, "directory traversal in middle"},
		{"/absolute/path.sh", true, "absolute path"},
		{"", true, "empty path"},
		{"   ", true, "whitespace only path"},
	}

	for _, test := range tests {
		t.Run(test.description, func(t *testing.T) {
			err := strategy.validateScriptPath(test.path)
			if test.expectError && err == nil {
				t.Errorf("Expected error for path %q, got nil", test.path)
			}
			if !test.expectError && err != nil {
				t.Errorf("Expected no error for path %q, got %v", test.path, err)
			}
		})
	}
}

func TestShellStrategy_ParseShellConfig(t *testing.T) {
	strategy := NewShellStrategy()

	// 测试JSON格式
	jsonConfig := `{"content": "echo test", "cmd": "bash test.sh"}`
	config, err := strategy.parseShellConfig(jsonConfig)
	if err != nil {
		t.Fatalf("Expected no error parsing JSON config, got %v", err)
	}
	if config.Content != "echo test" {
		t.Errorf("Expected content 'echo test', got %q", config.Content)
	}
	if config.Cmd != "bash test.sh" {
		t.Errorf("Expected cmd 'bash test.sh', got %q", config.Cmd)
	}

	// 测试纯文本格式
	plainText := "#!/bin/bash\necho 'plain text'"
	config, err = strategy.parseShellConfig(plainText)
	if err != nil {
		t.Fatalf("Expected no error parsing plain text config, got %v", err)
	}
	if config.Content != plainText {
		t.Errorf("Expected content %q, got %q", plainText, config.Content)
	}
	if config.Cmd != "" {
		t.Errorf("Expected empty cmd for plain text, got %q", config.Cmd)
	}
}

func TestShellStrategy_Integration(t *testing.T) {
	// 测试shell策略是否正确注册到默认管理器中
	manager := DefaultManager()

	// 检查策略是否可用
	strategies := GetAvailableStrategies()
	shellStrategies, exists := strategies["ShellStrategy"]
	if !exists {
		t.Error("ShellStrategy未在可用策略中找到")
		return
	}

	expectedTypes := []string{"sh"}
	if len(shellStrategies) != len(expectedTypes) {
		t.Errorf("ShellStrategy支持的类型数量不符，期望 %d，实际 %d", len(expectedTypes), len(shellStrategies))
		return
	}

	for i, expectedType := range expectedTypes {
		if shellStrategies[i] != expectedType {
			t.Errorf("ShellStrategy支持的类型不符，期望 %s，实际 %s", expectedType, shellStrategies[i])
		}
	}

	// 测试管理器能否找到shell策略并执行脚本
	testScript := "./test_integration_" + time.Now().Format("20060102150405") + ".sh"

	dep := EnvironmentDependency{
		Type:    "sh",
		Path:    testScript,
		Content: "#!/bin/bash\necho 'Integration test successful'",
	}

	ctx := context.Background()
	results, err := manager.Initialize(ctx, []EnvironmentDependency{dep})

	if err != nil {
		t.Fatalf("管理器初始化失败: %v", err)
	}

	if len(results) != 1 {
		t.Fatalf("期望1个结果，实际: %d", len(results))
	}

	result := results[0]
	if !result.Success {
		t.Errorf("Shell策略执行应该成功，错误: %s", result.ErrorMessage)
	}

	if result.Type != "sh" {
		t.Errorf("期望类型 'sh'，实际: %s", result.Type)
	}

	if !strings.Contains(result.Details, "Integration test successful") {
		t.Errorf("期望输出包含 'Integration test successful'，实际: %s", result.Details)
	}

	// 验证脚本文件已被清理
	if _, err := os.Stat(testScript); err == nil {
		t.Error("期望脚本文件被清理，但仍然存在")
	}
}
