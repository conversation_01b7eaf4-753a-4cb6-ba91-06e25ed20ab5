package environment

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"
	"time"

	_ "modernc.org/sqlite" // SQLite driver - 纯 Go 实现，不需要 CGO
)

// DatabaseStrategy 数据库初始化策略
type DatabaseStrategy struct{}

// NewDatabaseStrategy 创建数据库策略实例
func NewDatabaseStrategy() *DatabaseStrategy {
	return &DatabaseStrategy{}
}

// CanHandle 判断是否能处理指定类型的依赖
func (s *DatabaseStrategy) CanHandle(depType string) bool {
	return depType == "db" || depType == "database"
}

// GetName 获取策略名称
func (s *DatabaseStrategy) GetName() string {
	return "DatabaseStrategy"
}

// Initialize 执行数据库初始化
func (s *DatabaseStrategy) Initialize(ctx context.Context, dep EnvironmentDependency) (*InitializationResult, error) {
	result := &InitializationResult{
		Success: false,
		Path:    dep.Path,
		Type:    dep.Type,
	}

	// 转换为相对路径
	dbPath := dep.Path
	if strings.HasPrefix(dbPath, "/") {
		dbPath = "." + dbPath
	}
	// 如果文件已经存在，删除
	if _, err := os.Stat(dbPath); err == nil {
		if err := os.Remove(dbPath); err != nil {
			result.ErrorMessage = fmt.Sprintf("删除数据库文件失败: %v", err)
			return result, fmt.Errorf("删除数据库文件失败: %w", err)
		}
	}

	// 创建数据库目录（如果需要）
	if strings.Contains(dbPath, "/") {
		dir := dbPath[:strings.LastIndex(dbPath, "/")]
		if err := os.MkdirAll(dir, 0755); err != nil {
			result.ErrorMessage = fmt.Sprintf("创建数据库目录失败: %v", err)
			return result, fmt.Errorf("创建数据库目录失败 %s: %w", dir, err)
		}
	}

	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("连接数据库失败: %v", err)
		return result, fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Ping(); err != nil {
		result.ErrorMessage = fmt.Sprintf("数据库连接测试失败: %v", err)
		return result, fmt.Errorf("数据库连接测试失败: %w", err)
	}

	// 执行SQL语句
	if dep.Content != "" {
		statements := s.parseSQLStatements(dep.Content)
		successCount := 0
		var executionDetails []string

		for i, stmt := range statements {
			if stmt == "" {
				continue
			}

			startTime := time.Now()
			sqlResult, err := db.Exec(stmt)
			duration := time.Since(startTime)

			if err != nil {
				result.ErrorMessage = fmt.Sprintf("执行SQL失败 [语句 %d]: %v", i+1, err)
				return result, fmt.Errorf("执行SQL失败 [语句 %d]: %w\n语句内容: %s", i+1, err, stmt)
			}

			// 记录执行结果
			if rowsAffected, err := sqlResult.RowsAffected(); err == nil {
				executionDetails = append(executionDetails,
					fmt.Sprintf("语句 %d: 成功 (%s) - 影响行数: %d", i+1, duration, rowsAffected))
			} else {
				executionDetails = append(executionDetails,
					fmt.Sprintf("语句 %d: 成功 (%s)", i+1, duration))
			}

			successCount++
		}

		result.Success = true
		result.Details = fmt.Sprintf("成功执行 %d/%d 条SQL语句:\n%s",
			successCount, len(statements), strings.Join(executionDetails, "\n"))
	} else {
		result.Success = true
		result.Details = "数据库连接成功，但没有SQL内容需要执行"
	}

	return result, nil
}

// parseSQLStatements 解析SQL语句，处理注释和复杂的SQL脚本
func (s *DatabaseStrategy) parseSQLStatements(content string) []string {
	var statements []string
	var currentStatement strings.Builder

	lines := strings.Split(content, "\n")
	inBlockComment := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过空行
		if line == "" {
			continue
		}

		// 处理块注释
		if strings.HasPrefix(line, "/*") {
			inBlockComment = true
		}
		if strings.HasSuffix(line, "*/") {
			inBlockComment = false
			continue
		}
		if inBlockComment {
			continue
		}

		// 跳过单行注释
		if strings.HasPrefix(line, "--") || strings.HasPrefix(line, "#") {
			continue
		}

		// 处理行内注释
		if commentPos := strings.Index(line, "--"); commentPos != -1 {
			line = strings.TrimSpace(line[:commentPos])
			if line == "" {
				continue
			}
		}

		// 添加到当前语句
		if currentStatement.Len() > 0 {
			currentStatement.WriteString(" ")
		}
		currentStatement.WriteString(line)

		// 检查是否是完整语句（以分号结尾）
		if strings.HasSuffix(line, ";") {
			stmt := strings.TrimSpace(currentStatement.String())
			// 移除末尾的分号
			stmt = strings.TrimSuffix(stmt, ";")
			stmt = strings.TrimSpace(stmt)

			if stmt != "" {
				statements = append(statements, stmt)
			}
			currentStatement.Reset()
		}
	}

	// 处理最后一个可能没有分号的语句
	if currentStatement.Len() > 0 {
		stmt := strings.TrimSpace(currentStatement.String())
		if stmt != "" {
			statements = append(statements, stmt)
		}
	}

	return statements
}
