package check

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/log"
	"github.com/mark3labs/mcphost/pkg/llm"
)

// LLMChecker LLM检查器
type LLMChecker struct {
	provider llm.Provider
}

// NewLLMChecker 创建LLM检查器
func NewLLMChecker(provider llm.Provider) *LLMChecker {
	return &LLMChecker{
		provider: provider,
	}
}

// GetName 获取检查器名称
func (l *LLMChecker) GetName() string {
	return "llm_judge"
}

// Check 执行LLM检查
func (l *LLMChecker) Check(ctx context.Context, item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	startTime := time.Now()
	defer func() {
		result.TimeCostMs = time.Since(startTime).Milliseconds()
	}()

	switch item.Type {
	case "check_file_content":
		return l.checkFileContentByLLM(ctx, item)
	default:
		// 对于其他类型，使用通用的LLM判断
		return l.checkByLLMJudgment(ctx, item)
	}
}

// checkFileContentByLLM 使用LLM检查文件内容
func (l *LLMChecker) checkFileContentByLLM(ctx context.Context, item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 处理相对路径
	target := item.Target
	if strings.HasPrefix(target, "./") {
		target = target[2:]
	}

	// 读取文件内容
	content, err := os.ReadFile(target)
	if err != nil {
		result.ExcuteResult = "failed"
		result.ErrMsg = "读取文件失败: " + err.Error()
		return result
	}

	// 构建LLM检查提示
	prompt := fmt.Sprintf(`请检查以下文件内容是否符合要求。

要求描述: %s

文件内容:
%s

请判断文件内容是否符合要求，只回答"是"或"否"，并简要说明理由。`, item.Value, string(content))

	// 调用LLM
	message, err := l.provider.CreateMessage(ctx, prompt, nil, nil)
	if err != nil {
		result.ExcuteResult = "failed"
		result.ErrMsg = "LLM检查失败: " + err.Error()
		return result
	}

	response := message.GetContent()
	log.Debug("LLM检查结果", "response", response)

	// 简单判断是否包含"是"
	if strings.Contains(response, "是") || strings.Contains(response, "符合") || strings.Contains(response, "满足") {
		result.ExcuteResult = "success"
	} else {
		result.ExcuteResult = "failed"
		result.ErrMsg = "LLM判断不符合要求: " + response
	}

	return result
}

// checkByLLMJudgment 使用LLM进行通用判断
func (l *LLMChecker) checkByLLMJudgment(ctx context.Context, item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 构建LLM检查提示
	prompt := fmt.Sprintf(`请根据以下描述进行判断:

检查类型: %s
检查目标: %s
检查要求: %s

请判断是否符合要求，只回答"是"或"否"，并简要说明理由。`, item.Type, item.Target, item.Value)

	// 调用LLM
	message, err := l.provider.CreateMessage(ctx, prompt, nil, nil)
	if err != nil {
		result.ExcuteResult = "failed"
		result.ErrMsg = "LLM检查失败: " + err.Error()
		return result
	}

	response := message.GetContent()
	log.Debug("LLM检查结果", "response", response)

	// 简单判断是否包含"是"
	if strings.Contains(response, "是") || strings.Contains(response, "符合") || strings.Contains(response, "满足") {
		result.ExcuteResult = "success"
	} else {
		result.ExcuteResult = "failed"
		result.ErrMsg = "LLM判断不符合要求: " + response
	}

	return result
}
