package check

import (
	"context"
	"os"
	"os/exec"
	"strings"
	"time"
)

// RuleChecker 规则检查器
type RuleChecker struct{}

// NewRuleChecker 创建规则检查器
func NewRuleChecker() *RuleChecker {
	return &RuleChecker{}
}

// GetName 获取检查器名称
func (r *RuleChecker) GetName() string {
	return "rule_judge"
}

// Check 执行规则检查
func (r *RuleChecker) Check(ctx context.Context, item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	startTime := time.Now()
	defer func() {
		result.TimeCostMs = time.Since(startTime).Milliseconds()
	}()

	switch item.Type {
	case "check_file_exist":
		return r.checkFileExist(item)
	case "check_directory_exist":
		return r.checkDirectoryExist(item)
	case "check_file_content":
		// 对于内容检查，这里使用简单的字符串包含检查
		return r.checkFileContent(item)
	case "check_tool_call_list":
		// 工具调用列表检查，这里先简单返回成功
		result.ExcuteResult = "success"
		return result
	default:
		// 尝试作为shell命令执行
		return r.runShellCheck(item)
	}
}

// checkFileExist 检查文件是否存在
func (r *RuleChecker) checkFileExist(item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 处理相对路径
	target := item.Target
	if strings.HasPrefix(target, "./") {
		target = target[2:]
	}

	if _, err := os.Stat(target); err == nil {
		result.ExcuteResult = "success"
	} else {
		result.ExcuteResult = "failed"
		result.ErrMsg = "文件不存在: " + target
	}

	return result
}

// checkDirectoryExist 检查目录是否存在
func (r *RuleChecker) checkDirectoryExist(item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 处理相对路径
	target := item.Target
	if strings.HasPrefix(target, "./") {
		target = target[2:]
	}

	if info, err := os.Stat(target); err == nil && info.IsDir() {
		result.ExcuteResult = "success"
	} else {
		result.ExcuteResult = "failed"
		result.ErrMsg = "目录不存在: " + target
	}

	return result
}

// checkFileContent 检查文件内容
func (r *RuleChecker) checkFileContent(item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 处理相对路径
	target := item.Target
	if strings.HasPrefix(target, "./") {
		target = target[2:]
	}

	content, err := os.ReadFile(target)
	if err != nil {
		result.ExcuteResult = "failed"
		result.ErrMsg = "读取文件失败: " + err.Error()
		return result
	}

	// 简单的字符串包含检查
	contentStr := string(content)
	if strings.Contains(contentStr, item.Value) {
		result.ExcuteResult = "success"
	} else {
		result.ExcuteResult = "failed"
		result.ErrMsg = "文件内容不包含期望的字符串"
	}

	return result
}

// runShellCheck 执行shell命令检查
func (r *RuleChecker) runShellCheck(item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	// 使用target作为shell命令
	cmd := exec.Command("sh", "-c", item.Target)
	output, err := cmd.CombinedOutput()

	if err != nil {
		result.ExcuteResult = "failed"
		result.ErrMsg = "命令执行失败: " + err.Error() + " | 输出: " + string(output)
	} else {
		result.ExcuteResult = "success"
	}

	return result
}
