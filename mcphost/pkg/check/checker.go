package check

import (
	"context"
	"fmt"
	"time"

	"github.com/mark3labs/mcphost/pkg/llm"
)

// CheckResult 检查结果
type CheckResult struct {
	Type         string `json:"type"`
	Target       string `json:"target"`
	Method       string `json:"method"`
	PassRequired bool   `json:"pass_required"`
	Value        string `json:"value"`
	TimeCostMs   int64  `json:"time_cost_ms"`
	ExcuteResult string `json:"excute_result"`
	ErrMsg       string `json:"err_msg"`
}

// CheckItem 检查项配置
type CheckItem struct {
	Type         string `json:"type"`
	Target       string `json:"target"`
	Method       string `json:"method"`
	PassRequired bool   `json:"pass_required"`
	Value        string `json:"value"`
}

// Checker 检查器接口
type Checker interface {
	Check(ctx context.Context, item CheckItem) CheckResult
	GetName() string
}

// CheckerManager 检查器管理器
type CheckerManager struct {
	checkers map[string]Checker
}

// NewCheckerManager 创建检查器管理器
func NewCheckerManager(provider llm.Provider) *CheckerManager {
	manager := &CheckerManager{
		checkers: make(map[string]Checker),
	}

	// 注册默认检查器
	manager.RegisterChecker(NewRuleChecker())
	// manager.RegisterChecker(NewLLMChecker(provider))

	return manager
}

// RegisterChecker 注册检查器
func (m *CheckerManager) RegisterChecker(checker Checker) {
	m.checkers[checker.GetName()] = checker
}

// Check 执行检查
func (m *CheckerManager) Check(ctx context.Context, item CheckItem) CheckResult {
	result := CheckResult{
		Type:         item.Type,
		Target:       item.Target,
		Method:       item.Method,
		PassRequired: item.PassRequired,
		Value:        item.Value,
	}

	startTime := time.Now()
	defer func() {
		result.TimeCostMs = time.Since(startTime).Milliseconds()
	}()

	checker, exists := m.checkers[item.Method]
	if !exists {
		result.ExcuteResult = "failed"
		result.ErrMsg = fmt.Sprintf("不支持的检查方法: %s", item.Method)
		return result
	}

	return checker.Check(ctx, item)
}
