package assistant_api

import (
	"strings"

	"github.com/mark3labs/mcphost/pkg/llm"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Usage 表示token使用情况
type Usage struct {
	PromptTokens     int
	CompletionTokens int
}

// Message 实现llm.Message接口
type Message struct {
	assistantMessage *types.Message
	usage            Usage
	toolCallID       string // 用于工具响应
}

func (m *Message) GetRole() string {
	switch m.assistantMessage.Role {
	case types.RoleUser:
		return "user"
	case types.RoleAssistant:
		return "assistant"
	default:
		return "user"
	}
}

func (m *Message) GetContent() string {
	var contents []string
	for _, content := range m.assistantMessage.Content {
		if content.Type == "text" && content.Text != nil {
			contents = append(contents, content.Text.Value)
		}
	}
	return strings.Join(contents, "\n")
}

func (m *Message) GetReasoningContent() string {
	// assistant api目前不支持reasoning内容
	return ""
}

func (m *Message) GetToolCalls() []llm.ToolCall {
	// assistant api的工具调用通过不同机制处理
	// 这里返回空，因为工具调用在run过程中通过事件处理
	return []llm.ToolCall{}
}

func (m *Message) IsToolResponse() bool {
	return m.toolCallID != ""
}

func (m *Message) GetToolResponseID() string {
	return m.toolCallID
}

func (m *Message) GetUsage() (int, int) {
	return m.usage.PromptTokens, m.usage.CompletionTokens
}
