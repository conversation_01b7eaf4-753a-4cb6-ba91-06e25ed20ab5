package assistant_api

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/log"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcphost/pkg/llm"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/qianfan"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/yiyan"
	"github.com/mark3labs/mcphost/utils"
	"icode.baidu.com/baidu/gdp/logit"
)

type Provider struct {
	client        interfaces.Client
	assistantID   string
	model         string
	systemPrompt  string
	authorization string
	appID         string
	threadID      string
}

// NewProvider 创建provider
func NewProvider(authorization, assistantID, model, backend, appID, systemPrompt string, assistantServerIP string, assistantServerPort int, globalTimeout int) (*Provider, error) {
	if authorization == "" {
		return nil, fmt.Errorf("assistant_api需要配置authorization")
	}

	// 启用详细日志
	req.Verbose = true

	var client interfaces.Client

	switch backend {
	case "yiyan":
		if appID == "" {
			return nil, fmt.Errorf("yiyan backend需要配置app_id")
		}

		// 注册服务器
		err := utils.RegisterServer(utils.ServerToml{
			Name:              "assistant_v1",
			ConnectionTimeOut: 10000000,
			WriteTimeOut:      10000000,
			ReadTimeOut:       20000000,
			Retry:             10,

			IP:   assistantServerIP,
			Port: assistantServerPort,
		})
		if err != nil {
			return nil, fmt.Errorf("注册服务器失败: %w", err)
		}

		client = yiyan.NewClient(appID)
	case "qianfan":
		client = qianfan.NewClient(authorization)
	default:
		return nil, fmt.Errorf("不支持的backend: %s", backend)
	}

	provider := &Provider{
		client:        client,
		authorization: authorization,
		assistantID:   assistantID,
		model:         model,
		systemPrompt:  systemPrompt,
		appID:         appID,
	}

	// 对于yiyan backend，在初始化时创建assistant和thread
	if backend == "yiyan" {
		ctx := logit.NewContext(context.Background())

		// 如果没有提供assistantID，创建新的assistant
		if assistantID == "" {
			tools := []types.Tool{} // 初始为空，后续会根据实际工具动态添加

			assistant, err := provider.createAssistant(ctx, model, systemPrompt, tools)
			if err != nil {
				return nil, fmt.Errorf("创建assistant失败: %w", err)
			}
			provider.assistantID = assistant.ID
			log.Info("创建新assistant", "assistantID", provider.assistantID)
		}

		// 创建thread
		err := provider.initializeThread(ctx)
		if err != nil {
			return nil, fmt.Errorf("初始化thread失败: %w", err)
		}
	}

	return provider, nil
}

// createAssistant 创建助手
func (p *Provider) createAssistant(ctx context.Context, model, systemPrompt string, tools []types.Tool) (*types.Assistant, error) {
	opts := []func(*types.CreateAssistantOptions){
		func(o *types.CreateAssistantOptions) {
			o.ReqOpts = []func(*req.Options){
				func(o *req.Options) {
					o.Source = "yiyan"
					o.Authorization = p.authorization
					if p.appID != "" {
						o.AppID = p.appID
					}
				},
			}
		},
	}

	return p.client.Assistants().Create(
		ctx,
		types.Model(model),
		"mcphost_assistant",
		"MCPHost助手",
		systemPrompt,
		tools,
		opts...,
	)
}

// initializeThread 初始化线程
func (p *Provider) initializeThread(ctx context.Context) error {
	opts := []func(*req.Options){
		func(o *req.Options) {
			o.Source = "yiyan"
			o.Authorization = p.authorization
			if p.appID != "" {
				o.AppID = p.appID
			}
		},
	}

	thread, err := p.client.Threads().Create(ctx, []*types.Message{}, nil, opts...)
	if err != nil {
		return err
	}

	p.threadID = thread.ID
	log.Info("创建新thread", "threadID", p.threadID)
	return nil
}

func (p *Provider) CreateMessage(
	ctx context.Context,
	prompt string,
	messages []llm.Message,
	tools []llm.Tool,
) (llm.Message, error) {
	// not implemented
	return nil, nil
}

// convertToChatMessages 转换消息格式
func (p *Provider) convertToChatMessages(messages []llm.Message, prompt string) []types.ChatMessage {
	var chatMessages []types.ChatMessage

	// 转换历史消息
	for _, msg := range messages {
		role := p.convertRole(msg.GetRole())
		content := msg.GetContent()
		toolCalls := msg.GetToolCalls()
		isToolResponse := msg.IsToolResponse()

		// 如果是工具响应，作为用户消息处理
		if isToolResponse {
			if content != "" {
				chatMessages = append(chatMessages, types.ChatMessage{
					Role:    types.RoleUser,
					Content: content,
				})
			}
			continue
		}

		// 如果有工具调用，创建assistant消息
		if len(toolCalls) > 0 {
			var functionCalls []*types.FunctionCall
			for _, toolCall := range toolCalls {
				functionCalls = append(functionCalls, &types.FunctionCall{
					Name:      toolCall.GetName(),
					Arguments: p.mapToJSON(toolCall.GetArguments()),
				})
			}

			chatMessages = append(chatMessages, types.ChatMessage{
				Role:         types.RoleAssistant,
				Content:      content, // assistant消息可以同时包含内容和工具调用
				FunctionCall: functionCalls,
			})
			continue
		}

		// 普通消息
		if content != "" {
			chatMessages = append(chatMessages, types.ChatMessage{
				Role:    role,
				Content: content,
			})
		}
	}

	// 添加当前prompt - 确保不与最后一条消息角色重复
	if prompt != "" {
		// 检查最后一条消息是否也是user角色
		if len(chatMessages) > 0 && chatMessages[len(chatMessages)-1].Role == types.RoleUser {
			// 如果最后一条也是user消息，合并内容
			lastMsg := &chatMessages[len(chatMessages)-1]
			lastMsg.Content = lastMsg.Content + "\n\n" + prompt
		} else {
			// 否则添加新的user消息
			chatMessages = append(chatMessages, types.ChatMessage{
				Role:    types.RoleUser,
				Content: prompt,
			})
		}
	}

	return chatMessages
}

// convertRole 转换角色
func (p *Provider) convertRole(role string) types.Role {
	switch role {
	case "user":
		return types.RoleUser
	case "assistant":
		return types.RoleAssistant
	case "system":
		return types.RoleUser // assistant api中system通常作为instructions
	default:
		return types.RoleUser
	}
}

// convertTools 转换工具格式
func (p *Provider) convertTools(tools []llm.Tool) []types.Tool {
	var assistantTools []types.Tool

	for _, tool := range tools {
		// assistant api 对tool_call的兼容性有点儿问题，加载firecrawl的tool时，会报错，所以这里需要特殊处理（文灿提供的兼容后的tool_list）
		if strings.Contains(tool.Name, "firecrawl") {
			assistantTools = append(assistantTools, types.Tool{
				Type: types.FunctionTool,
				Function: map[string]interface{}{
					"name":        tool.Name,
					"description": tool.Description,
					"parameters":  utils.ProcessFirecrawlTool(tool),
				},
			})
			continue
		}
		// 构建完整的parameters对象
		parameters := map[string]interface{}{
			"type":       tool.InputSchema.Type,
			"properties": tool.InputSchema.Properties,
		}

		// 添加required字段（如果存在）
		if len(tool.InputSchema.Required) > 0 {
			parameters["required"] = tool.InputSchema.Required
		}

		assistantTools = append(assistantTools, types.Tool{
			Type: types.FunctionTool,
			Function: map[string]interface{}{
				"name":        tool.Name,
				"description": tool.Description,
				"parameters":  parameters,
			},
		})
	}

	return assistantTools
}

// mapToJSON 转换map到JSON字符串
func (p *Provider) mapToJSON(m map[string]interface{}) string {
	data, err := json.Marshal(m)
	if err != nil {
		return "{}"
	}
	return string(data)
}

func (p *Provider) CreateToolResponse(toolCallID string, content interface{}) (llm.Message, error) {
	// assistant api的工具响应通过SubmitToolOutputs处理，这里返回一个占位消息
	contentStr := fmt.Sprintf("%v", content)
	return &Message{
		assistantMessage: &types.Message{
			Role: types.RoleUser,
			Content: []types.MessageContent{
				{
					Type: "text",
					Text: &types.Text{Value: contentStr},
				},
			},
		},
	}, nil
}

func (p *Provider) SupportsTools() bool {
	return true
}

func (p *Provider) Name() string {
	return "assistant_api"
}

// CreateMessageWithToolHandling 专门为assistant_api设计的完整对话处理方法
// 包括工具调用记录、token统计等功能
func (p *Provider) CreateMessageWithToolHandling(
	ctx context.Context,
	prompt string,
	messages []llm.Message,
	tools []llm.Tool,
	mcpClients map[string]client.MCPClient,
	benchResult interface{},
	maxRounds int,
) (llm.Message, error) {
	ctx = logit.NewContext(ctx)

	// 创建请求选项
	opts := []func(*req.Options){
		func(o *req.Options) {
			o.Source = "yiyan"
			o.Authorization = p.authorization
			if p.appID != "" {
				o.AppID = p.appID
			}
		},
	}

	// 如果还没有threadID，先创建thread
	if p.threadID == "" {
		err := p.initializeThread(ctx)
		if err != nil {
			return nil, fmt.Errorf("初始化thread失败: %w", err)
		}
	}

	// 转换历史消息和当前prompt为ChatMessage格式
	chatMessages := p.convertToChatMessages(messages, prompt)

	// 转换工具到assistant api格式
	assistantTools := p.convertTools(tools)

	// 记录调试信息
	log.Debug("assistant_api request details",
		"threadID", p.threadID,
		"assistantID", p.assistantID,
		"model", p.model,
		"systemPrompt", p.systemPrompt,
		"chatMessages_count", len(chatMessages),
		"tools_count", len(assistantTools))

	// 创建运行选项
	startOpts := []func(*types.StartRunOptions){
		func(sro *types.StartRunOptions) {
			sro.MetaData = map[string]any{
				"option.run.mode":             "eval",
				"option.output.thoughts.data": "true",
				"config.run.max.steps":        maxRounds,
			}
			sro.ThreadID = p.threadID
			sro.Thread = types.RequestThread{
				Messages: chatMessages,
			}
			sro.Source = "yiyan"
			sro.ReqOpts = opts
			sro.Model = types.Model(p.model)
			sro.Instructions = p.systemPrompt
			sro.Tools = assistantTools
			sro.Stream = true // 使用流式调用
		},
	}

	// 使用流式创建并运行
	eventChan, closeRun, err := p.client.Threads().Runs().CreateAndStream(ctx, p.assistantID, startOpts...)
	if err != nil {
		return nil, fmt.Errorf("创建流式run失败: %w", err)
	}
	defer closeRun()

	// 处理流式事件
	var lastMessage *types.Message
	var currentThreadID, currentRunID string

	// 设置超时
	timeout := time.NewTimer(30 * time.Minute)
	defer timeout.Stop()

	promptTokens := 0
	completionTokens := 0
	var step int64
	for {
		select {
		case <-timeout.C:
			return nil, fmt.Errorf("流式响应超时")
		case event, ok := <-eventChan:
			if !ok {
				// channel 关闭，检查是否有最终消息
				if lastMessage != nil {
					return &Message{
						assistantMessage: lastMessage,
						usage: Usage{
							PromptTokens:     promptTokens,
							CompletionTokens: completionTokens,
						},
					}, nil
				}
				return nil, fmt.Errorf("流式响应意外结束")
			}

			if event.Error != nil {
				return nil, fmt.Errorf("流式响应错误: %w", event.Error)
			}

			switch event.Type {
			case types.RunEventMessage:
				// 处理消息事件
				if event.MessageEvent != nil {
					for _, content := range event.MessageEvent.Content {
						if content.Type == "text" && content.Text != nil {
							log.Info("模型回复", "contentLength", len(content.Text.Value))
						}

					}
					lastMessage = &types.Message{
						Content: event.MessageEvent.Content,
						Role:    types.RoleAssistant,
					}
				}
			case types.RunEventStatus:
				if event.StatusEvent == nil {
					continue
				}

				switch event.StatusEvent.Type {
				case types.RunStatusEventObjectTypeRunStep:
					// 获取ThreadID和RunID用于工具调用
					if event.StatusEvent.RunStep != nil {
						currentThreadID = event.StatusEvent.RunStep.ThreadID
						currentRunID = event.StatusEvent.RunStep.RunID
						log.Info("获取运行信息", "threadID", currentThreadID, "runID", currentRunID)
					}

				case types.RunStatusEventObjectTypeToolCalls:
					step++
					// 处理工具调用并记录
					err := p.handleToolCallsWithRecording(ctx, event.StatusEvent.ToolCalls, currentThreadID, currentRunID,
						opts, mcpClients, benchResult, step)
					if err != nil {
						log.Error("处理工具调用失败", "error", err)
						return nil, fmt.Errorf("处理工具调用失败: %w", err)
					}
				}

				// 检查运行状态
				switch event.StatusEvent.Status {
				case "completed":
					log.Info("运行完成")
				case "failed":
					return nil, fmt.Errorf("run执行失败")
				case "cancelled":
					return nil, fmt.Errorf("run被取消")
				}

				// 检查是否是运行结束事件
				if event.StatusEvent.EventType == types.RunStatusEventTypeRunEnd {
					promptTokens = event.StatusEvent.Run.Usage.Core.PromptTokens
					completionTokens = event.StatusEvent.Run.Usage.Core.CompletionTokens
					if lastMessage != nil {
						return &Message{
							assistantMessage: lastMessage,
							usage: Usage{
								PromptTokens:     promptTokens,
								CompletionTokens: completionTokens,
							},
						}, nil
					}
				}
			}
		}
	}
}

// handleToolCallsWithRecording 处理工具调用并记录结果
func (p *Provider) handleToolCallsWithRecording(
	ctx context.Context,
	toolCalls []*types.RunToolCalls,
	threadID, runID string,
	opts []func(*req.Options),
	mcpClients map[string]client.MCPClient,
	benchResult interface{},
	step int64,
) error {

	var toolOutputs []types.ToolOutput

	for _, toolCall := range toolCalls {
		if toolCall.Function.Name == "" {
			continue
		}

		callStartTime := time.Now()

		// 实际执行工具调用
		toolContent := p.executeToolCall(toolCall, mcpClients)

		// 记录工具调用
		p.recordToolCall(toolCall, toolContent, time.Since(callStartTime), benchResult, step)

		// 准备工具输出
		toolOutputs = append(toolOutputs, types.ToolOutput{
			ToolCallID: toolCall.ID,
			Output:     toolContent,
		})
	}

	// 提交所有工具输出
	if len(toolOutputs) > 0 {
		_, err := p.client.Threads().Runs().SubmitToolOutputs(ctx, threadID, runID, toolOutputs, opts...)
		if err != nil {
			return fmt.Errorf("提交工具输出失败: %w", err)
		}
	}

	return nil
}

// executeToolCall 执行单个工具调用
func (p *Provider) executeToolCall(toolCall *types.RunToolCalls, mcpClients map[string]client.MCPClient) string {
	// 解析工具调用输入
	toolInput, err := utils.ParseToolCallInput(toolCall.Function.Name, toolCall.Function.Arguments, toolCall.ID)
	if err != nil {
		errMsg := fmt.Sprintf("Error parsing tool call: %v", err)
		log.Error("工具调用解析失败", "tool_name", toolCall.Function.Name, "error", err)
		return fmt.Sprintf(`{"error": "%s"}`, errMsg)
	}

	// 使用通用工具调用函数
	result := utils.ExecuteToolCall(context.Background(), toolInput, mcpClients)

	// 格式化结果为JSON（assistant_api需要JSON格式）
	return utils.FormatToolCallResultAsJSON(result)
}

// recordToolCall 记录工具调用结果
func (p *Provider) recordToolCall(toolCall *types.RunToolCalls, content string, duration time.Duration, benchResult interface{}, step int64) {
	log.Debug("记录工具调用",
		"tool_name", toolCall.Function.Name,
		"arguments", toolCall.Function.Arguments,
		"result_length", len(content),
		"duration_ms", duration.Milliseconds())

	// 尝试类型断言为utils.ToolCallRecorder接口并记录
	if recorder, ok := benchResult.(utils.ToolCallRecorder); ok {
		recorder.AddToolCall(
			toolCall.Function.Name,
			toolCall.Function.Arguments,
			content,
			duration.Milliseconds(),
			step,
			toolCall.Function.Output,
			toolCall.Function.Thoughts,
		)
	} else {
		log.Warn("benchResult未实现ToolCallRecorder接口，工具调用记录仅在日志中保存")
	}
}
