package req

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/mark3labs/mcphost/utils"
	"github.com/tidwall/gjson"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/ral"
)

// Verbose 是否打印请求 path, header, body, query 等内容
// 默认通过 Logger 打印, 如果不传递 logger 则默认到标准输出
var (
	Verbose = false
	Logger  = logit.NewLoggerWithOutput(func(ctx context.Context, level logit.Level, callDepth int, message string, fields ...logit.Field) {
		encoder := logit.NewTextEncoder(logit.DefaultTextEncoderOption)
		// 公共信息
		logit.Range(ctx, func(f logit.Field) error {
			f.AddTo(encoder)
			return nil
		})
		for _, field := range fields {
			field.AddTo(encoder)
		}
		logit.String("message", message).AddTo(encoder)
		fmt.Printf("%s ", level.String())
		_, _ = encoder.WriteTo(os.Stdout)
	})
	// ServiceNameQianFan 千帆 assistant 服务名
	ServiceNameQianFan = "assistant"
	// ServiceNameYiYan 一言 assistant 服务名
	ServiceNameYiYan = "assistant_v1"
	// DefaultAppID 默认 appID
	DefaultAppID = "123456"
	// DefaultHeaderSource 默认 source 来源
	DefaultHeaderSource = "lingjing"
)

// Ret 返回原始数据
type Ret []byte

// Unmarshal 将 Ret 类型的 JSON 数据解码到传入的 v 接口中。
// 参数:
//   - v: 将 JSON 数据解码到的目标接口。
//
// 返回值:
//   - 如果解码成功, 返回 nil；否则返回错误信息。
//
// Unmarshal 将 JSON 字节数据反序列化到给定对象。
// 参数:
//   - v: 指向要存储解码数据的变量。
//
// 返回:
//   - error: 如果反序列化过程出现错误，则返回错误信息，否则返回 nil。
func (rr Ret) Unmarshal(v interface{}) error {
	return json.Unmarshal(rr, v)
}

// Map 将 Ret 数据解码为一个 map[string]any。
// 返回:
//   - map[string]any: 解码后的字典结构。
//   - error: 如果解码过程出现错误，则返回错误信息，否则返回 nil。
func (rr Ret) Map() (map[string]any, error) {
	var ret map[string]any
	err := json.Unmarshal(rr, &ret)
	return ret, err
}

// DebugPrint 方法以调试格式打印 Ret 对象的 JSON 数据。
// 此方法不接受参数，也没有返回值。通过 gjson 库解析字节并以美化格式输出。
func (rr Ret) DebugPrint() {
	fmt.Println("DebugPrint: ", gjson.ParseBytes(rr).Get("@pretty"))
}

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Error struct {
		Code    int    `json:"code"`
		Type    string `json:"type"`
		Message string `json:"message"`
		Params  string `json:"params"`
	} `json:"error,omitempty"`
}

// ErrorResponseData 错误响应数据结构体
type ErrorResponseData struct {
	Data struct {
		LastError struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"last_error,omitempty"`
	} `json:"data,omitempty"`
}

// ErrorResponseDataList 错误响应数据列表结构体
type ErrorResponseDataList struct {
	Data []struct {
		LastError struct {
			Code    string `json:"code"`
			Message string `json:"message"`
		} `json:"last_error,omitempty"`
	} `json:"data,omitempty"`
}

// Error 方法解析 ReqRet 类型的实例，检查 JSON 数据中是否存在错误信息。
// 如果存在错误信息，则返回格式化后的错误信息，包括错误代码、错误类型和错误消息。
// 如果不存在错误信息，则返回 nil。
// 返回值: error - 格式化后的错误信息或 nil
func (rr Ret) Error() error {
	er := ErrorResponse{}
	if err := json.Unmarshal(rr, &er); err == nil {
		if er.Error.Code != 0 {
			return fmt.Errorf("code: %d, type: %s, message: %q, params: %s", er.Error.Code, er.Error.Type, er.Error.Message, er.Error.Params)
		}
	}
	erd := ErrorResponseData{}
	if err := json.Unmarshal(rr, &erd); err == nil {
		if erd.Data.LastError.Code != "" {
			return fmt.Errorf("code: %s, message: %q", erd.Data.LastError.Code, erd.Data.LastError.Message)
		}
	}
	eds := ErrorResponseDataList{}
	if err := json.Unmarshal(rr, &eds); err == nil {
		for _, ed := range eds.Data {
			if ed.LastError.Code != "" {
				return fmt.Errorf("code: %s, message: %q", ed.LastError.Code, ed.LastError.Message)
			}
		}
	}

	return nil
}

// Pretty 方法将 ReqRet 类型的字节数据解析为 JSON 并返回格式化后的字符串。
// 如果解析过程中出现问题，将返回空字符串。
// 返回值: string 格式化后的 JSON 字符串。
func (rr Ret) Pretty() string {
	return gjson.ParseBytes(rr).Get("@pretty").String()
}

// Options 提供控制底层请求服务参数的能力
type Options struct {
	// RalReqOpt 支持用户在上游控制最底层发送给 RAL 的请求参数
	// 此函数入参 req.Header 一定不为空
	RalReqOpt func(req *ghttp.RalRequest)
	// RalReqAuthorizeOpt
	RalReqAuthorizeOpt func(req *ghttp.RalRequest)
	// RalResponseOpt 支持用户在上游控制最底层接收 RAL 返回的响应参数
	RalResponseOpt func(resp ral.Response)
	// RAL 相当于控制请求 Client
	RAL func(ctx context.Context, name any, req ral.Request, resp ral.Response, opts ...ral.ROption) error
	// Authorization token 如果有值则会被添加到请求 header 中
	// 此字段在使用千帆服务时会被使用
	Authorization string
	// AppID 用于请求的 appId
	// 此字段在使用一言服务时会被使用
	AppID string
	// Source 请求来源
	Source string
	// ServiceName 服务名
	ServiceName string
	// Verbose 是否打印请求 path, header, body, query 等内容
	// 默认通过传递的 logger 打印, 如果不传递 logger 则默认到标准输出
	Verbose bool
	// LogID 透传到下游的 logID 通过 header 中的 X_BD_LOGID 传递到下游
	LogID string
	// Logger
	Logger logit.Logger
	// trace共建需要透传到下游的信息

	AgentID  string
	Model    string
	LaunchID string
	UID      string
	CUID     string
	BaiduID  string
}

// NewOptions 创建一个默认的 Options
// Verbose 默认为 false
// Logger 默认为 nil
// ServiceName 默认为 "assistant"
func NewOptions() *Options {
	return &Options{
		Verbose:     Verbose,
		Logger:      Logger,
		ServiceName: ServiceNameQianFan,
	}
}

// NewOptionsByOpts 根据提供的配置函数列表初始化 Options。
// reqOptions 传入的配置函数列表，每个函数都接收一个指向 Options 的指针。
func NewOptionsByOpts(reqOptions ...func(*Options)) *Options {
	reqOpts := NewOptions()
	for _, opt := range reqOptions {
		if opt != nil {
			opt(reqOpts)
		}
	}
	return reqOpts
}

// SetYiYanReqOpts 设置用于YiYan服务的Options函数选项。
// 参数:
//   - opts: 可变数量的函数选项，每个函数接受一个指向Options的指针。
//
// 返回值:
//
//	返回一个包含所有初始选项和附加选项的函数切片。
func SetYiYanReqOpts(opts ...func(*Options)) []func(*Options) {
	yiyanOpts := []func(*Options){func(ro *Options) {
		ro.ServiceName = ServiceNameYiYan
		ro.AppID = DefaultAppID
	}}
	return append(yiyanOpts, opts...)
}

func optionSetHeader(ctx context.Context, header http.Header, reqOpts *Options) http.Header {
	if header == nil {
		header = http.Header{}
	}

	if header.Get("Content-Type") == "" {
		header.Set("Content-Type", "application/json")
	}

	if reqOpts.Authorization != "" {
		header.Set("Authorization", reqOpts.Authorization)
	}

	// logid key 是 X-Bd-Logid
	if f := logit.FindLogIDField(ctx); f == nil && reqOpts.LogID != "" {
		header.Set("X-Bd-Logid", reqOpts.LogID)
		header.Set("X_BD_LOGID", reqOpts.LogID)
	}

	// AppID
	if reqOpts.AppID != "" {
		header.Set("AppId", reqOpts.AppID)
	}

	// Source
	if reqOpts.Source != "" {
		header.Set("Source", reqOpts.Source)
	} else {
		header.Set("Source", DefaultHeaderSource)
	}

	// AgentID
	if reqOpts.AgentID != "" {
		header.Set("AgentId", reqOpts.AgentID)
	}
	// Model
	if reqOpts.Model != "" {
		header.Set("Model", reqOpts.Model)
	}
	// LaunchID
	if reqOpts.LaunchID != "" {
		header.Set("Launchid", reqOpts.LaunchID)
	}
	// UID
	if reqOpts.UID != "" {
		header.Set("Uid", reqOpts.UID)
	}
	// CUID
	if reqOpts.CUID != "" {
		header.Set("Cuid", reqOpts.CUID)
	}
	// BaiduID
	if reqOpts.BaiduID != "" {
		header.Set("Baiduid", reqOpts.BaiduID)
	}
	return header
}

// StreamReq 执行流式请求并返回响应。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - method: HTTP 方法，如 "GET", "POST" 等。
//   - path: 请求的 URL 路径。
//   - header: HTTP 请求头。
//   - body: POST 请求的主体，可以是任意类型。
//   - query: 查询参数。
//   - opts: 可选参数，用于配置请求选项。
//
// 返回值:
//   - resp: 包含服务器响应的 ghttp.RalStreamResponse 对象。
//   - err: 如果请求过程中发生错误，则返回错误信息。
func StreamReq(
	ctx context.Context,
	method string,
	path string,
	header http.Header,
	body any,
	query url.Values,
	opts ...func(*Options),
) (resp *ghttp.RalStreamResponse, err error) {
	reqOpts := NewOptions()
	for _, opt := range opts {
		if opt != nil {
			opt(reqOpts)
		}
	}

	header = optionSetHeader(ctx, header, reqOpts)

	req := &ghttp.RalRequest{
		APIName: path,
		Method:  method,
		Path:    path,
		Header:  header,
		Query:   query,
	}

	err = req.WithBody(body, codec.JSONEncoder)
	if err != nil {
		return
	}

	if reqOpts.RalReqOpt != nil {
		reqOpts.RalReqOpt(req)
	}
	if reqOpts.RalReqAuthorizeOpt != nil {
		reqOpts.RalReqAuthorizeOpt(req)
	}

	if reqOpts.Verbose && reqOpts.Logger != nil {
		bb, _ := json.Marshal(body)
		reqOpts.Logger.Notice(ctx, "assistant stream req",
			logit.String("method", method),
			logit.String("path", path),
			logit.AutoField("header", header),
			logit.String("body", string(bb)),
			logit.AutoField("query", query),
		)
	}

	res := &ghttp.RalStreamResponse{}

	if reqOpts.RAL == nil {
		err = ral.RAL(ctx, reqOpts.ServiceName, req, res)
	} else {
		err = reqOpts.RAL(ctx, reqOpts.ServiceName, req, res)
	}

	if err != nil {
		return nil, fmt.Errorf("stream req error: %w", err)
	}

	if reqOpts.RalResponseOpt != nil {
		reqOpts.RalResponseOpt(res)
	}

	// 流式接口可能直接返回类型为 json 的错误,此时应该及时截流并返回错误
	if resp := res.Response(); resp != nil {
		if strings.Contains(resp.Header.Get("Content-Type"), ghttp.ContentTypeJSON) {
			bb, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, fmt.Errorf("read response body error: %w", err)
			}

			// 尝试解码unicode转义序列
			decodedResponse := utils.TryDecodeResponse(bb)
			return nil, fmt.Errorf("stream req error: %s", decodedResponse)
		}
	}

	return res, nil
}

// Req 函数是一个通用的 HTTP 请求方法，通过传入不同的选项来实现多样的 HTTP 请求。
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - method: HTTP 请求方法，如 GET、POST 等。
//   - path: 请求的路径。
//   - header: HTTP 请求头。
//   - body: 请求的主体，可以是任何类型。
//   - query: URL 查询参数。
//   - opts: 可选的请求配置函数，用来修改请求选项。
//
// 返回:
//   - ReqRet: 请求返回结果。
//   - error: 如果请求过程中发生错误，会返回详细的错误信息。
func Req(
	ctx context.Context,
	method string,
	path string,
	header http.Header,
	body any,
	query url.Values,
	opts ...func(*Options),
) (ret Ret, err error) {
	reqOpts := NewOptions()
	for _, opt := range opts {
		if opt != nil {
			opt(reqOpts)
		}
	}

	var reqBody io.Reader
	if b, ok := body.(io.Reader); ok {
		reqBody = b
	} else {
		bodyJSON, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}

		reqBody = bytes.NewReader(bodyJSON)
	}

	header = optionSetHeader(ctx, header, reqOpts)

	req := &ghttp.RalRequest{
		Method: method,
		Path:   path,
		Header: header,
		Body:   reqBody,
		Query:  query,
	}

	if reqOpts.RalReqOpt != nil {
		reqOpts.RalReqOpt(req)
	}
	if reqOpts.RalReqAuthorizeOpt != nil {
		reqOpts.RalReqAuthorizeOpt(req)
	}

	if reqOpts.Verbose && reqOpts.Logger != nil {
		bb, _ := json.Marshal(body)
		reqOpts.Logger.Notice(ctx, "assistant req",
			logit.String("logid", logit.FindLogIDString(ctx)),
			logit.String("method", method),
			logit.String("path", path),
			logit.AutoField("header", header),
			logit.String("body", fmt.Sprintf("%q", bb)),
			logit.AutoField("query", query),
		)
	}

	data := bytes.Buffer{}

	res := &ghttp.RalResponse{
		Data:    &data,
		Decoder: codec.RawDecoder,
	}

	if reqOpts.RAL == nil {
		err = ral.RAL(ctx, reqOpts.ServiceName, req, res)
	} else {
		err = reqOpts.RAL(ctx, reqOpts.ServiceName, req, res)
	}

	if err != nil {
		return nil, fmt.Errorf("req error: %w", err)
	}

	if reqOpts.RalResponseOpt != nil {
		reqOpts.RalResponseOpt(res)
	}

	ret = data.Bytes()

	if res.Response() != nil && res.Response().StatusCode != http.StatusOK {
		// 尝试解码unicode转义序列
		decodedResponse := utils.TryDecodeResponse(ret)
		return nil, fmt.Errorf("req status code is`t ok,status code: %q, resp body: %s", res.Response().Status, decodedResponse)
	}

	if reqOpts.Verbose && reqOpts.Logger != nil {
		reqOpts.Logger.Notice(ctx, "assistant resp",
			logit.String("logid", logit.FindLogIDString(ctx)),
			logit.String("resp", fmt.Sprintf("%q", ret)),
		)
	}

	return
}
