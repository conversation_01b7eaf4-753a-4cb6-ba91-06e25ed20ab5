package qianfan

import (
	"context"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"icode.baidu.com/baidu/gdp/ghttp"
)

type GetAssistantListOptions = types.GetAssistantsOptions

// authorizeOpts 为给定的客户端添加授权选项。
//
// 参数:
//   - client: 用于授权的客户端接口
//   - reqOpts: 可选的请求选项函数切片
//
// 返回:
//   - []func(*r.Options): 包含原有选项和新添加的授权选项的函数切片
//
// 该函数将授权逻辑添加到现有的请求选项中，确保每个请求都经过适当的授权。
func authorizeOpts(client interfaces.Client, reqOpts ...func(*r.Options)) []func(*r.Options) {
	return append(reqOpts, func(ro *r.Options) {
		if ro != nil {
			ro.RalReqAuthorizeOpt = func(req *ghttp.RalRequest) {
				if req != nil {
					req.Header = client.Authorize(req.Header)
				}
			}
		}
	})
}

// Assistants 是千帆 Assistants 的实现。
type Assistants struct {
	// client 是与 API 交互的客户端实例。
	client interfaces.Client
}

// NewAssistants 创建一个新的千帆 Assistants 实例。
func NewAssistants(client interfaces.Client) *Assistants {
	return &Assistants{
		client: client,
	}
}

// Create 初始化一个具有给定参数和选项的新助手。
//
// 参数:
//   - ctx: 用于管理请求生命周期的上下文。
//   - model: 基于该模型的助手。
//   - name: 分配给助手的名称。
//   - description: 助手用途的简要描述。
//   - instructions: 指导助手行为的指令。
//   - tools: 助手可以使用的工具列表。
//   - opts: 其他可选的配置功能。
//
// 返回:
//   - *types.Assistant: 指向创建的助手对象的指针。
//   - error: 如果创建失败的错误对象，成功则为 nil。
func (a *Assistants) Create(
	ctx context.Context,
	model types.Model,
	name, description, instructions string,
	tools []types.Tool,
	opts ...func(*types.CreateAssistantOptions),
) (*types.Assistant, error) {
	// 通过回调的方式将 client 的授权信息添加到请求头中
	opts = append(opts, func(cao *types.CreateAssistantOptions) {
		if cao != nil {
			cao.ReqOpts = authorizeOpts(a.client, cao.ReqOpts...)
		}
	})

	return CreateAssistant(ctx, model, name, description, instructions, tools, opts...)
}

// Retrieve 根据给定的ID使用可选的请求选项获取助手。
//
// 参数：
//   - ctx: 用于控制取消和截止日期的上下文。
//   - id: 要检索的助手的标识符。
//   - opts: 修改请求选项的可选函数。
//
// 返回：
//   - *types.Assistant: 检索到的助手，如果发生错误则为nil。
//   - error: 如果在检索过程中出现问题则为错误对象，否则为nil。
func (a *Assistants) Retrieve(
	ctx context.Context,
	id string,
	opts ...func(*r.Options),
) (*types.Assistant, error) {
	opts = authorizeOpts(a.client, opts...)

	return GetAssistant(ctx, id, opts...)
}

// Update更新修改当前上下文中具有给定assistantID的助手的详细信息。
//
// 参数：
//   - ctx：用于管理请求生命周期的上下文。
//   - assistantID：要修改的助手的唯一标识符。
//   - opts：修改助手的可变函数选项。
//
// 返回：
//   - *types.Assistant：指向更新后的助手对象的指针。
//   - err：如果更新操作失败，则返回错误。
func (a *Assistants) Update(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.UpdateAssistantOptions),
) (assistant *types.Assistant, err error) {
	// 通过回调的方式将 client 的授权信息添加到请求头中
	opts = append(opts, func(mao *types.UpdateAssistantOptions) {
		if mao != nil {
			mao.ReqOpts = authorizeOpts(a.client, mao.ReqOpts...)
		}
	})

	return UpdateAssistant(ctx, assistantID, opts...)
}

// List 根据提供的选项检索 Assistant 列表。
//
// 参数：
//   - ctx：请求的上下文，用于取消和超时。
//   - opts：变参选项函数以自定义GetAssistantsOptions。
//
// 返回：
//   - as：指向types.Assistant对象的指针切片。
//   - hasMore：指示是否有更多助理可获取的布尔值。
//   - err：在检索过程中发生错误时的错误对象。
func (a *Assistants) List(
	ctx context.Context,
	opts ...func(*GetAssistantListOptions),
) (as []*types.Assistant, hasMore bool, err error) {
	// 通过回调的方式将 client 的授权信息添加到请求头中
	opts = append(opts, func(galo *GetAssistantListOptions) {
		if galo != nil {
			galo.ReqOpts = authorizeOpts(a.client, galo.ReqOpts...)
		}
	})

	return GetAssistants(ctx, opts...)
}

// Delete 删除指定的助手。
//
// 参数:
//   - ctx: 用于控制请求生命周期的上下文。
//   - id: 要删除的助手的唯一标识符。
//   - opts: 可选的请求配置函数列表。
//
// 返回值:
//   - 如果删除成功，返回nil，否则返回错误信息。
func (a *Assistants) Delete(ctx context.Context, assistantID string, opts ...func(*r.Options)) error {
	opts = authorizeOpts(a.client, opts...)
	return DeleteAssistant(ctx, assistantID, opts...)
}
