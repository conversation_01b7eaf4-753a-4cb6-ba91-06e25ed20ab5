package qianfan

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Plannings functionCall 极简模式调用
type Plannings struct {
	client interfaces.Client
}

// NewPlannings 创建一个极简模式调用
func NewPlannings(client interfaces.Client) *Plannings {
	return &Plannings{
		client: client,
	}
}

// Call 同步调用
func (p *Plannings) Call(ctx context.Context, opts ...func(*types.PlanningOptions)) (*types.PlanningResp, error) {
	opts = append(opts, func(po *types.PlanningOptions) {
		if po != nil {
			po.ReqOpts = authorizeOpts(p.client, po.ReqOpts...)
		}
	})
	resp, err := Planning(ctx, opts...)
	if err != nil {
		return nil, fmt.Errorf("planning Call error: %w", err)
	}
	return resp, nil
}

// StreamCall 流式调用
func (p *Plannings) StreamCall(ctx context.Context, opts ...func(*types.PlanningOptions)) (<-chan *types.PlanningEvent, error) {
	opts = append(opts, func(po *types.PlanningOptions) {
		if po != nil {
			po.ReqOpts = authorizeOpts(p.client, po.ReqOpts...)
		}
	})
	stream, err := PlanningStream(ctx, opts...)
	if err != nil {
		return stream, fmt.Errorf("planning StreamCall error: %w", err)
	}
	return stream, nil
}
