package qianfan

import (
	"context"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// CreateThread 创建Thread
func CreateThread(
	ctx context.Context,
	messages []*types.Message,
	metadata map[string]any,
	opts ...func(*r.Options),
) (ret *types.Thread, err error) {
	body := map[string]any{
		"messages": messages,
		"metadata": metadata,
	}

	reqRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsPath,
		http.Header{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// GetThread 获取Thread
func GetThread(ctx context.Context, threadID string, opts ...func(*r.Options)) (ret *types.Thread, err error) {
	reqRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsQueryPath,
		http.Header{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"thread_id": threadID},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// UpdateThread 修改Thread
func UpdateThread(
	ctx context.Context,
	threadID string,
	metadata map[string]any,
	opts ...func(*r.Options),
) (ret *types.Thread, err error) {
	reqRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsUpdatePath,
		http.Header{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"metadata": metadata, "thread_id": threadID},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// DeleteThread 删除Thread
func DeleteThread(ctx context.Context, threadID string, opts ...func(*r.Options)) (err error) {
	repRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsDeletePath,
		http.Header{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"thread_id": threadID},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = repRet.Error(); err != nil {
		return
	}
	return
}
