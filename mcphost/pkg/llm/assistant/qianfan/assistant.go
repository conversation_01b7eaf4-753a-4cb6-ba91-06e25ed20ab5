package qianfan

import (
	"context"
	"fmt"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// AssistantAuthorization assistant 服务授权 token
// 在使用 assistant 包提供的方法之前,需要先设置这个
// var AssistantAuthorization string

// UpdateAssistant 修改助手
func UpdateAssistant(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.UpdateAssistantOptions),
) (assistant *types.Assistant, err error) {
	options := &types.UpdateAssistantOptions{}
	for _, opt := range opts {
		opt(options)
	}

	if assistantID == "" {
		return nil, fmt.Errorf("assistantID is empty")
	}

	body := map[string]any{}
	body["assistant_id"] = assistantID
	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsUpdatePath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistant = &types.Assistant{}
	err = resp.Unmarshal(&assistant)
	return
}

// GetAssistant 获取助手信息
func GetAssistant(ctx context.Context, assistantID string, opts ...func(*r.Options)) (assistant *types.Assistant, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsQueryPath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]string{"assistant_id": assistantID},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistant = &types.Assistant{}
	err = resp.Unmarshal(&assistant)
	return
}

// GetAssistants 获取助手列表
func GetAssistants(
	ctx context.Context,
	opts ...func(*types.GetAssistantsOptions),
) (assistants []*types.Assistant, hasMore bool, err error) {
	options := &types.GetAssistantsOptions{}
	for _, opt := range opts {
		opt(options)
	}

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsListPath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.Assistant `json:"data"`
		HasMore bool               `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// DeleteAssistant 删除助手
func DeleteAssistant(ctx context.Context, assistantID string, opts ...func(*r.Options)) (err error) {
	del, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsDeletePath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]string{"assistant_id": assistantID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	ret, err := del.Map()
	if err != nil {
		return
	}

	if ret != nil {
		if deleted, ok := ret["deleted"].(bool); ok && !deleted {
			return fmt.Errorf("delete assistant failed, %v", ret)
		}
	}
	return
}

// CreateAssistant 创建助手
func CreateAssistant(
	ctx context.Context,
	model types.Model,
	name, description, instructions string,
	tools []types.Tool,
	opts ...func(*types.CreateAssistantOptions),
) (assistantInfo *types.Assistant, err error) {
	options := &types.CreateAssistantOptions{}
	for _, opt := range opts {
		opt(options)
	}

	if tools == nil {
		tools = []types.Tool{}
	}

	body := map[string]any{
		"model":        model,
		"name":         name,
		"description":  description,
		"instructions": instructions,
		"tools":        tools,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsPath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistantInfo = &types.Assistant{}
	err = resp.Unmarshal(&assistantInfo)
	return
}

// GetAssistantFiles 获取助手上挂载的文件
func GetAssistantFiles(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.GetAssistantFilesOptions),
) (files []*types.FileMountOnAssistant, hasMore bool, err error) {
	options := &types.GetAssistantFilesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.AssistantID = assistantID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsFilesListPath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.FileMountOnAssistant `json:"data"`
		HasMore bool                          `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// AddFileToAssistant 添加文件到助手
func AddFileToAssistant(
	ctx context.Context,
	assistantID, fileID string,
	opts ...func(*r.Options),
) (file *types.FileMountOnAssistant, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsFilesPath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]string{"file_id": fileID, "assistant_id": assistantID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	file = &types.FileMountOnAssistant{}
	err = resp.Unmarshal(&file)
	return
}

// DeleteFileFromAssistant 从助手中移除文件
func DeleteFileFromAssistant(ctx context.Context, assistantID, fileID string, opts ...func(*r.Options)) (err error) {
	del, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsDeletePath,
		// 默认使用全局变量 AssistantAuthorization, 如果用户通过 opts 传入了新的 Authorization,则在 req 里面会使用用户传入的
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]string{"assistant_id": assistantID, "file_id": fileID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	ret, err := del.Map()
	if err != nil {
		return
	}

	if ret != nil {
		if deleted, ok := ret["deleted"].(bool); ok && !deleted {
			return fmt.Errorf("delete file from assistant failed, %v", ret)
		}
	}

	return
}
