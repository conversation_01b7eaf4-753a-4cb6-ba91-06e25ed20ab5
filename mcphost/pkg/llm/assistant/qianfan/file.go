package qianfan

import (
	"bytes"
	"context"
	"fmt"
	"mime/multipart"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// UploadFile 上传文件
func UploadFile(ctx context.Context, fileName, fileContent string, opts ...func(*types.UploadFileOptions)) (ret types.File, err error) {
	options := &types.UploadFileOptions{}
	for _, opt := range opts {
		opt(options)
	}

	query := map[string][]string{}
	if options.Purpose != "" {
		query["purpose"] = []string{options.Purpose}
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	fileField, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return
	}

	_, err = fileField.Write([]byte(fileContent))
	if err != nil {
		return
	}

	err = writer.Close()
	if err != nil {
		return
	}

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}, "Content-Type": {writer.FormDataContentType()}},
		body,
		query,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	err = resp.Unmarshal(&ret)
	return
}

// GetFiles 获取文件列表
// WARN: 此函数会获取所有文件, 请谨慎使用以免造成性能问题
func GetFiles(ctx context.Context, opts ...func(*types.GetFilesOptions)) (files []*types.File, err error) {
	options := &types.GetFilesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	respRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesListPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{},
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = respRet.Error(); err != nil {
		return
	}

	type ret struct {
		Data []*types.File `json:"data,omitempty"`
	}

	var r ret
	err = respRet.Unmarshal(&r)
	return r.Data, err
}

// GetFileInfo 获取文件信息
func GetFileInfo(ctx context.Context, fileID string, opts ...func(*types.GetFileInfoOptions)) (file *types.File, err error) {
	options := &types.GetFileInfoOptions{}
	for _, opt := range opts {
		opt(options)
	}

	respRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesQueryPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID},
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = respRet.Error(); err != nil {
		return
	}

	f := types.File{}
	err = respRet.Unmarshal(&f)
	return &f, err
}

// GetFileURL 根据文件ID获取文件的下载链接
// 参数:
//
//	fileID - 文件ID
//	urlValidTime - URL有效时间（秒）
//
// 返回值:
//
//	url - 文件下载链接
func GetFileURL(ctx context.Context, fileID string, urlValidTime int, opts ...func(*types.GetFileInfoOptions)) (url string, err error) {
	options := &types.GetFileInfoOptions{}
	for _, opt := range opts {
		opt(options)
	}

	respRet, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesURLPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID, "url_valid_seconds": urlValidTime},
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = respRet.Error(); err != nil {
		return
	}

	type ret struct {
		DownloadURL string `json:"download_url"`
	}
	var r ret
	err = respRet.Unmarshal(&r)
	return r.DownloadURL, err
}

// DeleteFile 删除文件
func DeleteFile(ctx context.Context, fileID string, opts ...func(*types.DeleteFileOptions)) (err error) {
	options := &types.DeleteFileOptions{}
	for _, opt := range opts {
		opt(options)
	}

	del, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesDeletePath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID},
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	return
}

// GetContentOfFile 获取文件内容
func GetContentOfFile(ctx context.Context, fileID string, opts ...func(*r.Options)) ([]byte, error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesContentPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID},
		nil,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	if err = resp.Error(); err != nil {
		return nil, err
	}

	return resp, nil
}

// DownloadFile 下载文件
func DownloadFile(ctx context.Context, fileID string, opts ...func(*r.Options)) ([]byte, error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesDownloadPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID},
		nil,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetInfoOfFile 获取文件信息
func GetInfoOfFile(ctx context.Context, fileID string, opts ...func(*r.Options)) (ret *types.File, err error) {
	ret = &types.File{}
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.StorageFilesQueryPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"file_id": fileID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	err = resp.Unmarshal(ret)
	return
}

// RegisterFile 注册文件
func RegisterFile(ctx context.Context, body []*types.FileRegisterInput, appID string, opts ...func(*r.Options)) (list []*types.FileRegisterOutput, err error) {
	query := map[string][]string{}
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.RegisterFilePath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{
			"file_info_list": body,
		},
		query,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	if err = resp.Error(); err != nil {
		return nil, err
	}

	type ret struct {
		LogID string                      `json:"log_id"`
		Code  int                         `json:"code"`
		Msg   string                      `json:"msg"`
		Data  []*types.FileRegisterOutput `json:"data"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	if err != nil {
		return nil, err
	}
	if r.Code != 0 {
		return nil, fmt.Errorf("search registered file error: resp[%s], logID[%s], code[%d], msg[%s]", string(resp), r.LogID, r.Code, r.Msg)
	}
	return r.Data, nil
}

// SearchRegisteredFile 检索文件
func SearchRegisteredFile(ctx context.Context, fileID string, opts ...func(*r.Options)) (file *types.FileRegisterOutput, err error) {
	query := map[string][]string{}
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.RegisterSearchFilePath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{
			"file_id": fileID,
		},
		query,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	if err = resp.Error(); err != nil {
		return nil, err
	}

	type ret struct {
		LogID string                    `json:"log_id"`
		Code  int                       `json:"code"`
		Msg   string                    `json:"msg"`
		Data  *types.FileRegisterOutput `json:"data"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	if err != nil {
		return nil, err
	}
	if r.Code != 0 {
		return nil, fmt.Errorf("search registered file error: resp[%s], logID[%s], code[%d], msg[%s]", string(resp), r.LogID, r.Code, r.Msg)
	}
	return r.Data, nil
}
