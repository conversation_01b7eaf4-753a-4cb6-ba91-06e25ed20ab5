package qianfan

import (
	"net/http"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
)

// Client 表示与 Qianfan 服务进行交互的客户端。
// 它包含助手、线程、文件模块的指针，以及用于认证的字符串。
//
// 成员:
// - assistants: 指向 Assistants 的指针，用于操作assistant相关功能。
// - plannings: 指向 Plannings 的指针，用于操作functionCall相关功能。
// - threads: 指向 Threads 的指针，用于操作线程相关功能。
// - files: 指向 Files 的指针，用于操作文件相关功能。
// - auth: 字符串，用于身份认证。
type Client struct {
	assistants *Assistants
	plannings  *Plannings
	threads    *Threads
	files      *Files
	auth       string
}

// NewClient 创建一个新的 Client 实例。
// authorization 千帆提供的授权Token,形如"Bearer xxxxx"
func NewClient(authorization string) *Client {
	qc := &Client{
		auth: authorization,
	}
	qc.assistants = NewAssistants(qc)
	qc.threads = NewThreads(qc)
	qc.files = NewFiles(qc)
	return qc
}

// Assistants 返回一个 Assistants 接口实例。
func (qc *Client) Assistants() interfaces.Assistants {
	if qc.assistants == nil {
		qc.assistants = NewAssistants(qc)
	}
	return qc.assistants
}

// Threads 返回一个 Threads 接口实例。
func (qc *Client) Threads() interfaces.Threads {
	if qc.threads == nil {
		qc.threads = NewThreads(qc)
	}
	return qc.threads
}

// Files 返回一个 Files 接口实例。
func (qc *Client) Files() interfaces.Files {
	if qc.files == nil {
		qc.files = NewFiles(qc)
	}
	return qc.files
}

// Plannings 返回一个 Plannings 接口实例。
func (qc *Client) Plannings() interfaces.Plannings {
	if qc.plannings == nil {
		qc.plannings = NewPlannings(qc)
	}
	return qc.plannings
}

// Authorize 为给定的 HTTP 头信息添加授权信息。
func (qc *Client) Authorize(header http.Header) http.Header {
	// Implement the method to add authorization information to the header
	if header == nil {
		return make(http.Header)
	}

	header.Set("Authorization", qc.auth)
	return header
}
