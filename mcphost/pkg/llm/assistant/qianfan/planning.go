package qianfan

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
)

// Planning 发起一次思考请求
func Planning(ctx context.Context, opts ...func(*types.PlanningOptions)) (*types.PlanningResp, error) {
	options := types.PlanningOptions{}
	for _, opt := range opts {
		opt(&options)
	}
	// 强制不要stream
	options.Stream = false

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.AssistantsPlanningFunctionCallPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("planning req failed: %w", err)
	}

	if err = resp.Error(); err != nil {
		return nil, fmt.Errorf("planning respErr: %w", err)
	}

	pResp := &types.PlanningResp{}
	err = resp.Unmarshal(&pResp)
	if err != nil {
		return nil, fmt.Errorf("planning unmarshal failed: %w", err)
	}
	return pResp, nil
}

// PlanningStream 发起一次流式思考请求
func PlanningStream(ctx context.Context, opts ...func(*types.PlanningOptions)) (<-chan *types.PlanningEvent, error) {
	options := types.PlanningOptions{}
	for _, opt := range opts {
		opt(&options)
	}
	// 强制stream
	options.Stream = true

	resp, err := r.StreamReq(
		ctx,
		http.MethodPost,
		types.AssistantsPlanningFunctionCallPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("planningStream req failed: %w", err)
	}
	if resp == nil {
		return nil, fmt.Errorf("planningStream resp is nil")
	}

	stream := make(chan *types.PlanningEvent, 100)

	// 异步处理结果
	go parseStream(ctx, resp, stream, options)

	return stream, nil
}

func parseStream(ctx context.Context, resp *ghttp.RalStreamResponse, stream chan<- *types.PlanningEvent, options types.PlanningOptions) {
	tryWrite := func(re *types.PlanningEvent) {
		select {
		case <-ctx.Done():
		default:
		}
		select {
		case stream <- re:
		default:
		}
	}

	defer func() {
		if r := recover(); r != nil {
			err, ok := r.(error)
			if ok {
				tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream event panic: %w", err)})
			} else {
				tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream event panic: %v", r)})
			}
		}
		if err := resp.Close(); err != nil {
			tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream resp close failed: %w", err)})
		}
		close(stream)
	}()

	// 转换成event 解析sse
	er := ghttp.EventReader{}
	if hResp := resp.Response(); hResp != nil {
		er.From = hResp.Body
	} else {
		tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream resp is nil")})
		return
	}

	// log debug信息
	reqOpts := r.NewOptions()
	if len(options.ReqOpts) > 0 {
		for _, opt := range options.ReqOpts {
			opt(reqOpts)
		}
	}

	for {
		select {
		case <-ctx.Done():
			return
		default:
		}
		ev, err := er.Next()
		if err != nil && !errors.Is(err, io.EOF) {
			tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream read event failed: %w", err)})
			break
		}
		if errors.Is(err, io.EOF) {
			return
		}

		if reqOpts.Verbose && reqOpts.Logger != nil {
			reqOpts.Logger.Notice(ctx,
				fmt.Sprintf("assistant stream response event"),
				logit.String("event", ev.Event),
				logit.AutoField("data", ev.Data),
			)
		}

		re := &types.PlanningEvent{}
		err = re.LoadFromEvent(&ev)
		if err != nil {
			tryWrite(&types.PlanningEvent{Error: fmt.Errorf("planningStream event load failed: %w", err)})
			break
		}

		tryWrite(re)
	}
}
