package qianfan

import (
	"context"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// CreateMessage 创建消息
func CreateMessage(
	ctx context.Context,
	threadID string,
	role types.Role,
	content string,
	opts ...func(*types.CreateMessageOptions),
) (msg *types.Message, err error) {
	options := &types.CreateMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	body := map[string]any{
		"thread_id": threadID,
		"role":      role,
		"content":   content,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessagesPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	msg = &types.Message{}
	err = resp.Unmarshal(&msg)
	return
}

// GetMessages 获取消息
func GetMessages(
	ctx context.Context,
	threadID string,
	opts ...func(*types.GetMessagesOptions),
) (msgs []*types.Message, hasMore bool, err error) {
	options := &types.GetMessagesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ThreadID = threadID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessagesListPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.Message `json:"data"`
		HasMore bool             `json:"has_more"`
	}

	var r ret

	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// GetMessage 从 Thread 中获取某个 Message
func GetMessage(ctx context.Context, threadID, messageID string, opts ...func(*r.Options)) (msg *types.Message, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessagesQueryPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{
			"thread_id":  threadID,
			"message_id": messageID,
		},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	msg = &types.Message{}
	err = resp.Unmarshal(&msg)
	return
}

// UpdateMessage 修改 Message
// msg 中 role,content 不能为空 metadata, file_ids 为可选,其他字段不需要填写
func UpdateMessage(
	ctx context.Context,
	threadID, messageID, content string,
	opts ...func(*types.UpdateMessageOptions),
) (ret *types.Message, err error) {
	options := &types.UpdateMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	body := map[string]any{
		"thread_id":  threadID,
		"message_id": messageID,
		"content":    content,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessagesUpdatePath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	ret = &types.Message{}
	err = resp.Unmarshal(&ret)
	return
}

// GetMessageFiles 获取消息中挂载的文件
func GetMessageFiles(
	ctx context.Context,
	threadID, messageID string,
	opts ...func(*types.GetFilesInMessageOptions),
) (files []*types.FileMountOnMessage, hasMore bool, err error) {
	options := &types.GetFilesInMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ThreadID = threadID
	options.MessageID = messageID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessagesFilesListPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type filesResp struct {
		Data    []*types.FileMountOnMessage `json:"data"`
		HasMore bool                        `json:"has_more"`
	}

	var filesRespData filesResp

	err = resp.Unmarshal(&filesRespData)
	return filesRespData.Data, filesRespData.HasMore, err
}

// DeleteLastMessage 删除最后一条消息
func DeleteLastMessage(
	ctx context.Context,
	threadID, runID string,
	opts ...func(options *types.DeleteLastMessageOptions),
) (deleted bool, err error) {
	options := &types.DeleteLastMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	body := map[string]any{
		"thread_id": threadID,
		"run_id":    runID,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsMessageDeleteLastPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type deleteLastMessageResp struct {
		ID     string `json:"id"`
		Object string `json:"object"`
		LogID  string `json:"logId"`
		// Messages []struct {
		// 	MessageID string `json:"message_id"`
		// 	Role      string `json:"role"`
		// 	Content   string `json:"content"`
		// }
		Deleted bool `json:"deleted"`
	}

	ret := &deleteLastMessageResp{}
	err = resp.Unmarshal(&ret)
	if err != nil {
		return
	}

	return ret.Deleted, nil
}
