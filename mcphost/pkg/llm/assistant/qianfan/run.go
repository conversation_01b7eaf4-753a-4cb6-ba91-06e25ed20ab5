package qianfan

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
)

// StartRunOnThread 在一个 Thread 上开始一次 Run
func StartRunOnThread(ctx context.Context, threadID, assistantID string, opts ...func(*types.StartRunOptions)) (run *types.Run, err error) {
	options := types.StartRunOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ThreadID = threadID
	options.AssistantID = assistantID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// StartRun 直接开始一次 Run 并创建一个新的 Thread
func StartRun(ctx context.Context, assistantID string, opts ...func(*types.StartRunOptions)) (run *types.Run, err error) {
	options := types.StartRunOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.AssistantID = assistantID
	options.Stream = false

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// StartRunStream 同步流式请求 runs 接口, 返回一个 chan 用于接收 RunEvent
// 如果在即有 thread 上运行, 则需要通过 opts 传入 threadID
func StartRunStream(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (run chan *types.RunEvent, closeRun func(), err error) {
	options := types.StartRunOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.AssistantID = assistantID
	options.Stream = true

	run = make(chan *types.RunEvent, 100)
	closeChan := make(chan struct{}, 1)
	runID := ""
	oneClose := sync.Once{}
	closeRun = func() {
		oneClose.Do(func() {
			closeChan <- struct{}{}

			// NOTE:专门开一个 goroutine 来取消 Run, 由于此时业务接口可能已经结束了,且不方便返回错误,因此这里错误直接使用 logit.DefaultLogger 记录
			go func() {
				c := logit.NewContext(context.Background())
				logit.SetLogID(c, logit.FindLogIDField(ctx).Value())

				defer func() {
					if err := recover(); err != nil {
						logit.DefaultLogger.Notice(c, fmt.Sprintf("cancel run panic: %v", err))
					}
				}()

				if runID != "" {
					options.CallbackCancelRunStart(ctx, options.ThreadID, runID)
					cancelRun, err := CancelRun(c, options.ThreadID, runID, true, options.ReqOpts...)
					options.CallbackCancelRunEnd(ctx, cancelRun)
					if err != nil {
						logit.DefaultLogger.Notice(c, fmt.Sprintf("cancel run error: %v", err))
					}
				}
			}()
		})
	}

	resp, err := r.StreamReq(
		ctx,
		http.MethodPost,
		types.ThreadsRunsPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil || resp == nil {
		return
	}

	tryWrite := func(re *types.RunEvent) {
		select {
		case <-ctx.Done():
			return
		default:
		}

		select {
		case run <- re:
		default:
		}
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				tryWrite(&types.RunEvent{Error: fmt.Errorf("%v", r)})
			}

			if err := resp.Close(); err != nil {
				tryWrite(&types.RunEvent{Error: err})
			}
			close(run)
		}()

		er := ghttp.EventReader{}

		if hresp := resp.Response(); hresp != nil {
			er.From = hresp.Body
		} else {
			return
		}

		for {
			select {
			case <-closeChan:
				return
			default:
			}

			ev, err := er.Next()
			if err != nil && !errors.Is(err, io.EOF) {
				tryWrite(&types.RunEvent{Error: err})
				break
			}

			if errors.Is(err, io.EOF) {
				break
			}

			if ev.Event == "ping" {
				continue
			}

			reqOpts := r.NewOptions()
			if options.ReqOpts != nil {
				for _, opt := range options.ReqOpts {
					opt(reqOpts)
				}
			}

			if reqOpts.Verbose {
				if reqOpts.Logger != nil {
					reqOpts.Logger.Notice(ctx, fmt.Sprintf("assistant stream response event: %s data: %s", ev.Event, ev.Data))
				}
			}

			re := &types.RunEvent{}
			err = re.LoadFromEvent(&ev)
			if err != nil {
				tryWrite(&types.RunEvent{Error: err})
				break
			}

			//  纪录一下 runID 方便后续 cancel
			if re.Type == types.RunEventStatus && re.StatusEvent != nil && re.StatusEvent.Type == types.RunStatusEventObjectTypeRunStep &&
				re.StatusEvent.RunStep != nil {
				runID = re.StatusEvent.RunStep.RunID
			}

			tryWrite(re)
		}
	}()

	return
}

// GetRuns 获取指定 Thread 下所有 Runs
func GetRuns(ctx context.Context, threadID string, opts ...func(*types.GetRunsOptions)) (runs []*types.Run, hasMore bool, err error) {
	options := types.GetRunsOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ThreadID = threadID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsListPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		HasMore bool         `json:"has_more,omitempty"`
		Data    []*types.Run `json:"data,omitempty"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// GetRun 获取指定 Thread 下某个 Run
func GetRun(ctx context.Context, threadID, runID string, opts ...func(*r.Options)) (run *types.Run, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsQueryPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"thread_id": threadID, "run_id": runID},
		nil,
		opts...)
	if err != nil {
		return
	}
	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// UpdateRun 修改 Run 的元数据
// NOTE: 此函数千帆暂时未实现,不要使用
//
//nolint:unreachable,govet
func UpdateRun(
	ctx context.Context,
	threadID, runID string,
	metaData map[string]any,
	opts ...func(*r.Options),
) (run *types.Run, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		"/v2/threads/"+threadID+"/runs/"+runID,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		metaData,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// SubmitToolOutputs2Run 提交工具输出到 Run
func SubmitToolOutputs2Run(
	ctx context.Context,
	threadID, runID string,
	toolOutputs []types.ToolOutput,
	opts ...func(*r.Options),
) (run *types.Run, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsSubmitToolOutputsPath,
		map[string][]string{"Content-Type": {"application/json"}, "Authorization": {types.AssistantAuthorization}},
		map[string]any{"tool_outputs": toolOutputs, "thread_id": threadID, "run_id": runID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{Submitted: true}
	err = resp.Unmarshal(&run)
	return
}

// CancelRun 取消 Run
func CancelRun(ctx context.Context, threadID, runID string, sync bool, opts ...func(*r.Options)) (run *types.Run, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsCancelPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"thread_id": threadID, "run_id": runID, "sync": sync},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// GetRunStep 获取指定 Thread 下某个 Run 的某个 Step
func GetRunStep(ctx context.Context, threadID, runID, stepID string, opts ...func(*r.Options)) (runStep *types.RunStep, err error) {
	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsQueryPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		map[string]any{"thread_id": threadID, "run_id": runID, "step_id": stepID},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	runStep = &types.RunStep{}
	err = resp.Unmarshal(&runStep)
	return
}

// GetRunSteps 获取指定 Thread 下某个 Run 的所有 Steps
func GetRunSteps(
	ctx context.Context,
	threadID, runID string,
	opts ...func(*types.GetRunStepsOptions),
) (runSteps []*types.RunStep, hasMore bool, err error) {
	options := types.GetRunStepsOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ThreadID = threadID
	options.RunID = runID

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		types.ThreadsRunsStepsListPath,
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data     []*types.RunStep `json:"data"`
		HaseMore bool             `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HaseMore, err
}
