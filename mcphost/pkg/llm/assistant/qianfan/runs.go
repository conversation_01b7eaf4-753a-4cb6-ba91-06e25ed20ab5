package qianfan

import (
	"context"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// 确保 Runs 实现 Runs 接口
var _ interfaces.Runs = (*Runs)(nil)

// Runs 代表一个一次assistant运行
type Runs struct {
	client   interfaces.Client
	messages *Messages
}

// NewRuns 创建一个新的Runs实例
// 参数:
// - client: 客户端实例
// 返回值:
// - 返回指向Runs结构体的指针
func NewRuns(client interfaces.Client) *Runs {
	return &Runs{client: client}
}

// Messages 返回 Runs 的消息。
// 如果消息尚未初始化，则会创建一个新的 Messages 实例。
// 返回 Runs 的消息。
// 返回值：Messages 对象。
func (runs *Runs) Messages() interfaces.Messages {
	if runs.messages == nil {
		runs.messages = NewMessages(runs.client)
	}
	return runs.messages
}

// Steps 获取指定线程和运行ID的运行步骤。
//
// 参数:
//   - ctx: 上下文环境
//   - threadID: 线程ID
//   - runID: 运行ID
//   - opts: 可选的函数，用于配置获取运行步骤的选项
//
// 返回值:
//   - runSteps: 获取的运行步骤列表
//   - hasMore: 是否有更多的运行步骤
//   - err: 操作过程中遇到的错误(如果有)
func (runs *Runs) Steps(
	ctx context.Context,
	threadID, runID string,
	opts ...func(*types.GetRunStepsOptions),
) (runSteps []*types.RunStep, hasMore bool, err error) {
	opts = append(opts, func(grso *types.GetRunStepsOptions) {
		if grso != nil {
			grso.ReqOpts = authorizeOpts(runs.client, grso.ReqOpts...)
		}
	})

	return GetRunSteps(ctx, threadID, runID, opts...)
}

// Retrieve 从指定的 threadID 和 runID 中检索运行信息，并可选地应用一些选项配置。
//
// 参数:
// - ctx: 上下文环境，用于控制请求的生命周期。
// - threadID: 线程的唯一标识符。
// - runID: 运行的唯一标识符。
// - opts: 可选参数，用于配置请求选项。
//
// 返回值:
// - *types.Run: 检索到的运行信息。
// - error: 如果检索过程中发生错误，将返回相应的错误信息。
func (runs *Runs) Retrieve(
	ctx context.Context,
	threadID, runID string,
	opts ...func(*r.Options),
) (*types.Run, error) {
	opts = authorizeOpts(runs.client, opts...)

	return GetRun(ctx, threadID, runID, opts...)
}

// List 方法返回指定线程的运行信息。
//
// 参数：
//   - ctx 请求的上下文，用于控制请求的取消和截止时间。
//   - threadID  线程的唯一标识符。
//   - opts 可选参数，用于配置获取运行信息的选项。
//
// 返回值：
//   - runs  获取的运行信息列表。
//   - hasMore 布尔值，表示是否有更多的运行信息未被获取。
//   - err 如果发生错误，会返回相应的错误信息。
func (runs *Runs) List(
	ctx context.Context,
	threadID string,
	opts ...func(*types.GetRunsOptions),
) (ret []*types.Run, hasMore bool, err error) {
	opts = append(opts, func(gro *types.GetRunsOptions) {
		if gro != nil {
			gro.ReqOpts = authorizeOpts(runs.client, gro.ReqOpts...)
		}
	})

	return GetRuns(ctx, threadID, opts...)
}

// Cancel 取消指定的运行任务
//
// 参数:
//   - ctx: 上下文环境
//   - threadID: 线程ID
//   - runID: 运行ID
//   - opts: 可选参数，用于配置请求选项
//
// 返回:
//   - run: 取消后的运行任务信息
//   - err: 如果有错误发生，返回错误信息
func (runs *Runs) Cancel(ctx context.Context, threadID, runID string, sync bool, opts ...func(*r.Options)) (ret *types.Run, err error) {
	opts = authorizeOpts(runs.client, opts...)

	return CancelRun(ctx, threadID, runID, sync, opts...)
}

// Create 创建并运行一个新的任务。
//
// 参数:
//   - ctx: 上下文 Context，用于控制任务的生命周期。
//   - assistantID: 助手的唯一标识符。
//   - opts: 可选参数，用于配置 StartRunOptions。
//
// 返回值:
//   - run: 运行实例的指针 (*types.Run)。
//   - err: 如果发生错误，返回错误信息。
func (runs *Runs) Create(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (ret *types.Run, err error) {
	opts = append(opts, func(sro *types.StartRunOptions) {
		if sro != nil {
			sro.ReqOpts = authorizeOpts(runs.client, sro.ReqOpts...)
		}
	})

	return StartRun(ctx, assistantID, opts...)
}

// CreateAndStream 创建并运行一个新的流。
// 它使用提供的 context 和 assistantID 参数，以及可变参数 opts 来配置启动选项。
//
// 参数:
//   - ctx: context.Context 上下文参数，用于控制流的生命周期。
//   - assistantID: string 助手的唯一标识符。
//   - opts: 可选的函数参数，用于配置启动选项。
//
// 返回值:
//   - run: chan *types.RunEvent 返回一个通道，用于接收运行事件。
//   - closeRun: func() 返回一个函数，用于关闭运行的流。
//   - err: error 如果创建和运行流时出现错误，将返回该错误。
func (runs *Runs) CreateAndStream(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (ret <-chan *types.RunEvent, closeRun func(), err error) {
	opts = append(opts, func(sro *types.StartRunOptions) {
		if sro != nil {
			sro.ReqOpts = authorizeOpts(runs.client, sro.ReqOpts...)
		}
	})

	return StartRunStream(ctx, assistantID, opts...)
}

// SubmitToolOutputs 提交工具输出到指定的运行记录
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期
//   - threadID: 线程ID，标识当前的线程
//   - runID: 运行ID，标识当前的运行记录
//   - toolOutputs: 工具输出列表，需要提交的工具输出
//   - opts: 可选参数，用于请求的额外配置
//
// 返回:
//   - *types.Run: 提交后的运行记录对象
//   - error: 如果提交过程中发生错误，返回相应的错误信息
func (runs *Runs) SubmitToolOutputs(
	ctx context.Context,
	threadID, runID string,
	toolOutputs []types.ToolOutput,
	opts ...func(*r.Options),
) (ret *types.Run, err error) {
	opts = authorizeOpts(runs.client, opts...)

	return SubmitToolOutputs2Run(ctx, threadID, runID, toolOutputs, opts...)
}
