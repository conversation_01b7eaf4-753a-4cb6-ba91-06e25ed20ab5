package yiyan

import (
	"bytes"
	"context"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/url"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// GetFiles 获取文件列表的接口。
//
// 这个函数将默认的服务名和AppID添加到请求中，并发送一个HTTP GET请求以获取文件列表。
// 返回值包含文件列表和可能出现的错误。
//
// 参数:
//   - ctx: 请求的上下文信息，用于控制请求的生命周期。
//   - opts: 可选的请求配置函数。
//
// 返回值:
//   - files: 文件列表（如果没有错误的话）。
//   - err: 可能出现的错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
// Query 中 classification_id 为 appID
func GetFiles(ctx context.Context, opts ...func(*types.GetFilesOptions)) (files []*types.File, err error) {
	options := &types.GetFilesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	reqOpts := r.NewOptionsByOpts(options.ReqOpts...)

	query := url.Values{}
	query.Add("purpose", options.Purpose)
	if options.ClassificationID != "" {
		query.Add("classification_id", options.ClassificationID)
	} else {
		query.Add("classification_id", reqOpts.AppID)
	}

	respRet, err := r.Req(
		ctx,
		http.MethodGet,
		"/v1/files",
		nil,
		nil,
		query,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = respRet.Error(); err != nil {
		return
	}

	type ret struct {
		Data []*types.File `json:"data,omitempty"`
	}

	var r ret
	err = respRet.Unmarshal(&r)
	return r.Data, err
}

// UploadFile 上传文件到服务的版本1接口
//
// 参数:
//   - ctx: 操作的上下文，可以用来控制取消等操作。
//   - fileName: 文件的名称。
//   - fileContent: 文件的内容，以字符串形式。
//   - opts: 可选参数，接收一个或多个配置选项的函数，用于自定义上传文件的选项。
//
// 返回值:
//   - File: 上传成功后的文件信息。
//   - error: 操作过程中发生的任何错误。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
// Query 中 classification_id 默认为 appID
func UploadFile(ctx context.Context, fileName, fileContent string, opts ...func(*types.UploadFileOptions)) (ret types.File, err error) {
	options := &types.UploadFileOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	reqOpts := r.NewOptionsByOpts(options.ReqOpts...)

	query := map[string][]string{}
	if options.Purpose != "" {
		query["purpose"] = []string{options.Purpose}
	}

	// 用户给了分类ID，使用用户给的分类ID
	// 否则使用默认的appID
	if options.ClassificationID != "" {
		query["classification_id"] = []string{options.ClassificationID}
	} else {
		query["classification_id"] = []string{reqOpts.AppID}
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	fileField, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return
	}

	_, err = fileField.Write([]byte(fileContent))
	if err != nil {
		return
	}

	err = writer.Close()
	if err != nil {
		return
	}

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		"/v1/files",
		nil,
		body,
		query,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	err = resp.Unmarshal(&ret)
	return
}

// GetFileInfo 获取文件信息的函数。
// 此函数会使用给定的文件ID，从指定的服务中获取文件的详细信息。
//
// 参数:
// - ctx: 上下文环境，用于控制请求的生命周期。
// - fileID: 目标文件的唯一标识符。
// - opts: 可选参数，用于自定义请求选项。
//
// 返回:
// - file: 包含文件详细信息的File对象。
// - err: 如果请求失败或处理过程中出现错误，返回相应的错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetFileInfo(ctx context.Context, fileID string, opts ...func(*types.GetFileInfoOptions)) (file *types.File, err error) {
	options := &types.GetFileInfoOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	reqOpts := r.NewOptionsByOpts(options.ReqOpts...)
	query := url.Values{}
	if options.ClassificationID != "" {
		query.Add("classification_id", options.ClassificationID)
	} else {
		query.Add("classification_id", reqOpts.AppID)
	}

	respRet, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/files/%s", fileID),
		nil,
		nil,
		query,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = respRet.Error(); err != nil {
		return
	}

	f := types.File{}
	err = respRet.Unmarshal(&f)
	return &f, err
}

// DeleteFile 删除指定文件。
//
// 参数:
//   - ctx: 请求的上下文。
//   - fileID: 要删除的文件ID。
//   - opts: 可选参数，用于配置请求选项。
//
// 返回值:
//   - err: 操作过程中遇到的错误。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func DeleteFile(ctx context.Context, fileID string, opts ...func(*types.DeleteFileOptions)) (err error) {
	options := &types.DeleteFileOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	del, err := r.Req(
		ctx,
		http.MethodDelete,
		fmt.Sprintf("/v1/files/%s", fileID),
		nil,
		nil,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	return
}

// DownloadFile 下载指定文件。
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期。
//   - fileID: 要下载的文件ID。
//   - opts: 可选的请求配置函数。
//
// 返回值:
//   - []byte: 文件内容的字节切片。
//   - error: 如果出错，返回错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func DownloadFile(ctx context.Context, fileID string, opts ...func(*r.Options)) ([]byte, error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/files/%s/download", fileID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetContentOfFile 获取指定文件的内容（版本1）。
// 参数:
//   - ctx: 上下文用于控制请求的生命周期。
//   - fileID: 要获取内容的文件的唯一标识符。
//   - opts: 请求选项的可变参数，可以用来设置自定义的请求选项。
//
// 返回值:
//   - []byte: 文件内容的字节切片。
//   - error: 如果请求失败或处理过程中出现错误，将返回一个错误对象。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetContentOfFile(ctx context.Context, fileID string, opts ...func(*r.Options)) ([]byte, error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/files/%s/content", fileID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	if err = resp.Error(); err != nil {
		return nil, err
	}

	return resp, nil
}
