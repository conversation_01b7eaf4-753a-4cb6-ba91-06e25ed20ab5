package yiyan

import (
	"context"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Threads 线程管理
type Threads struct {
	client   interfaces.Client
	runs     *Runs
	messages *Messages
}

// NewThreads 创建一个新的 Threads 实例。
// 参数：
//   - client：客户端实例。
//
// 返回值：
//   - *Threads：返回一个指向 Threads 实例的指针。
func NewThreads(client interfaces.Client) *Threads {
	return &Threads{
		client:   client,
		runs:     NewRuns(client),
		messages: NewMessages(client),
	}
}

// Runs 返回当前 Threads 结构体的 Runs 实例。如果 Runs 实例未初始化，则创建新的 Runs 对象。
//
// 返回值：
//   - Runs：当前 Threads 结构体持有的 Runs 实例。
func (t *Threads) Runs() interfaces.Runs {
	if t.runs == nil {
		t.runs = NewRuns(t.client)
	}
	return t.runs
}

// Messages 返回消息。
// 如果消息尚未初始化，则创建新的 Messages。
// 返回初始化的消息。
func (t *Threads) Messages() interfaces.Messages {
	if t.messages == nil {
		t.messages = NewMessages(t.client)
	}
	return t.messages
}

// Create 初始化一个包含给定消息和元数据的新线程。
//
// 参数:
//   - ctx: 控制请求生命周期的上下文。
//   - messages: 将成为线程一部分的 types.Message 指针切片。
//   - metadata: 包含线程附加元数据的映射。
//   - opts: 用于修改 r.Options 的可选函数。
//
// 返回:
//   - *types.Thread: 指向创建的线程的指针。
//   - error: 线程创建过程中遇到的错误（如果有）。
func (t *Threads) Create(
	ctx context.Context,
	messages []*types.Message,
	metadata map[string]any,
	opts ...func(*r.Options),
) (*types.Thread, error) {
	opts = authorizeOpts(t.client, opts...)

	return CreateThread(ctx, messages, metadata, opts...)
}

// Retrieve 根据其 ID 获取一个线程，并返回一个指向检索到的线程的指针，以及在此过程中遇到的任何错误。
//
// 参数：
//   - ctx      - 用于管理请求截止日期和取消操作的上下文。
//   - threadID - 要检索的线程的唯一标识符。
//   - opts     - 可选函数，用于修改请求选项。
//
// 返回值：
//   - *types.Thread - 一个指向检索到的线程对象的指针。
//   - error       - 如果在检索过程中发生错误，则此错误值不为空。
func (t *Threads) Retrieve(ctx context.Context, threadID string, opts ...func(*r.Options)) (*types.Thread, error) {
	opts = authorizeOpts(t.client, opts...)

	return GetThread(ctx, threadID, opts...)
}

// Update 操作修改现有的线程，并添加新的元数据和选项。
//
// 参数:
//   - ctx: 管理请求生命周期的上下文。
//   - threadID: 要更新的线程的唯一标识符。
//   - metaData: 一个包含元数据键值对的映射，用于更新线程。
//   - opts: 可变参数，用于附加请求选项。
//
// 返回:
//   - *types.Thread: 更新后线程对象的指针。
//   - error: 如果更新操作失败，返回一个错误对象。
//
//nolint:unused-parameter
func (t *Threads) Update(
	ctx context.Context,
	threadID string,
	metaData map[string]any,
	opts ...func(*r.Options),
) (*types.Thread, error) {
	opts = authorizeOpts(t.client, opts...)

	return UpdateThread(ctx, threadID, metaData, opts...)
}

// Delete 删除一个指定ID的线程。
//
// 参数:
//   - ctx: 用于控制请求生命周期的上下文。
//   - threadID: 要删除的线程的唯一标识符。
//   - opts: 可选参数，用于自定义请求选项。
//
// 返回:
//
//	如果删除操作成功，则返回nil。否则返回一个错误信息。
func (t *Threads) Delete(ctx context.Context, threadID string, opts ...func(*r.Options)) error {
	opts = authorizeOpts(t.client, opts...)

	return DeleteThread(ctx, threadID, opts...)
}

// CreateAndRunStream 创建并运行一个新的流
//
// 该函数用于创建和启动一个新的流，以处理指定的助手ID和选项。
// 如果成功，它会返回一个事件通道、一个关闭流的函数以及一个错误信息（如果有）。
//
// 参数:
//   - ctx (context.Context): 上下文信息，用于控制流的生命周期。
//   - assistantID (string): 助手的唯一标识符。
//   - opts (...func(*types.StartRunOptions)): 可选的函数列表，用于配置启动选项。
//
// 返回:
//   - run (chan *types.RunEvent): 事件通道，用于接收流中的事件。
//   - closeRun (func()): 用于关闭流的函数。
//   - err (error): 错误信息（如果有）。
func (t *Threads) CreateAndRunStream(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (run chan *types.RunEvent, closeRun func(), err error) {
	opts = append(opts, func(sro *types.StartRunOptions) {
		if sro != nil {
			sro.ReqOpts = authorizeOpts(t.client, sro.ReqOpts...)
		}
	})

	return StartRunStream(ctx, assistantID, opts...)
}

// CreateAndRun 直接开始一次 Run 并创建一个新的 Thread。
//
// 参数:
//   - ctx: 上下文，用于控制任务的生命周期。
//   - assistantID: 助手ID，用于识别和关联任务。
//   - opts: 可选参数，用于配置任务启动的选项。
//
// 返回:
//   - run: 返回一个任务的实例。
//   - err: 如果发生错误，返回错误信息。
func (t *Threads) CreateAndRun(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (run *types.Run, err error) {
	opts = append(opts, func(sro *types.StartRunOptions) {
		if sro != nil {
			sro.ReqOpts = authorizeOpts(t.client, sro.ReqOpts...)
		}
	})

	return StartRun(ctx, assistantID, opts...)
}
