package yiyan

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"github.com/tidwall/gjson"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"
)

// GetRun 获取指定线程和运行ID的运行信息
//
// 此函数通过HTTP GET请求来获取指定线程ID和运行ID的运行信息。
//
// 参数:
//   - ctx: 上下文对象。
//   - threadID: 线程ID。
//   - runID: 运行ID。
//   - opts: 请求选项的函数切片。
//
// 返回值:
//   - run: 获取的运行信息，类型为*Run。
//   - err: 如果发生错误，返回错误信息。
func GetRun(ctx context.Context, threadID, runID string, opts ...func(*r.Options)) (run *types.Run, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/runs/%s", threadID, runID),
		nil,
		nil,
		nil,
		opts...)
	if err != nil {
		return
	}
	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// GetRuns 获取指定线程的运行信息。
//
// 参数:
//   - ctx: 上下文，包含请求的截止时间和取消信号。
//   - threadID: 线程ID，用于指定要获取运行信息的线程。
//   - opts: 可选参数，用于配置请求参数。
//
// 返回值:
//   - runs: 获取到的运行信息切片。
//   - hasMore: 是否有更多的运行信息。
//   - err: 执行过程中的错误信息。
func GetRuns(ctx context.Context, threadID string, opts ...func(*types.GetRunsOptions)) (runs []*types.Run, hasMore bool, err error) {
	options := types.GetRunsOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/runs", threadID),
		nil,
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		HasMore bool         `json:"has_more,omitempty"`
		Data    []*types.Run `json:"data,omitempty"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// StartRun 创建一个 Thread 同时启动一个新 Run
//
// 参数:
//   - ctx: Context，用于请求的上下文环境
//   - assistantID: string，助手ID
//   - opts: 可选函数，用于配置 StartRunOptions
//
// 返回值:
//   - run: *Run，新创建的会话
//   - err: error，错误信息，如果操作失败则返回错误
func StartRun(ctx context.Context, assistantID string, opts ...func(*types.StartRunOptions)) (run *types.Run, err error) {
	options := types.StartRunOptions{}
	options.AssistantID = assistantID
	for _, opt := range opts {
		opt(&options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		"/v1/threads/runs",
		nil,
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}

// StartRunOnThread 在指定线程上启动一个运行。
//
// 参数:
//   - ctx: 用于控制执行的上下文。
//   - threadID: 要启动运行的线程ID。
//   - assistantID: 助手ID。
//   - opts: 可选参数，用于配置 StartRunOptions。
//
// 返回值:
//   - run: 启动的运行信息。
//   - err: 如果发生错误，返回错误信息。
func StartRunOnThread(ctx context.Context, threadID, assistantID string, opts ...func(*types.StartRunOptions)) (*types.Run, error) {
	options := types.StartRunOptions{AssistantID: assistantID}
	for _, opt := range opts {
		opt(&options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/threads/%s/runs", threadID),
		nil,
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return nil, err
	}

	if err = resp.Error(); err != nil {
		return nil, err
	}

	run := &types.Run{}
	if err = resp.Unmarshal(run); err != nil {
		return nil, err
	}

	return run, nil
}

// GetRunStep 获取特定线程、运行和步骤的运行步骤信息。
//
// 参数：
//   - ctx: 上下文用于控制请求的生命周期。
//   - threadID: 线程的唯一标识符。
//   - runID: 运行的唯一标识符。
//   - stepID: 步骤的唯一标识符。
//   - opts: 可选的请求选项函数列表，用于配置请求。
//
// 返回值：
//   - runStep: 指向包含运行步骤信息的 RunStep 结构体的指针。
//   - err: 如果请求过程中遇到错误，返回相应的错误信息。
func GetRunStep(ctx context.Context, threadID, runID, stepID string, opts ...func(*r.Options)) (runStep *types.RunStep, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/runs/%s/steps/%s", threadID, runID, stepID),
		nil,
		nil,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	runStep = &types.RunStep{}
	err = resp.Unmarshal(&runStep)
	return
}

// GetRunSteps 获取指定线程和运行ID的运行步骤。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - threadID: 线程ID，用于指定请求的目标线程。
//   - runID: 运行ID，用于指定请求的目标运行。
//   - opts: 变长参数，用于配置 GetRunStepsOptions 的函数。
//
// 返回:
//   - runSteps: 获取到的运行步骤切片。
//   - hasMore: 是否有更多的步骤。
//   - err: 如果请求或处理过程中发生错误，则返回该错误。
func GetRunSteps(
	ctx context.Context,
	threadID, runID string,
	opts ...func(*types.GetRunStepsOptions),
) (runSteps []*types.RunStep, hasMore bool, err error) {
	options := types.GetRunStepsOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/runs/%s/steps", threadID, runID),
		map[string][]string{"Authorization": {types.AssistantAuthorization}},
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data     []*types.RunStep `json:"data"`
		HaseMore bool             `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HaseMore, err
}

// StartRunStream 同步流式请求 runs 接口, 返回一个 chan 用于接收 RunEvent
// 如果在即有 thread 上运行, 则需要通过 opts 传入 threadID
func StartRunStream(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.StartRunOptions),
) (run chan *types.RunEvent, closeRun func(), err error) {
	options := types.StartRunOptions{}
	for _, opt := range opts {
		opt(&options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	reqOpts := r.NewOptionsByOpts(options.ReqOpts...)

	options.AssistantID = assistantID
	options.Stream = true

	run = make(chan *types.RunEvent)
	closeChan := make(chan struct{}, 1)
	runID := ""
	oneClose := sync.Once{}
	threadID := options.ThreadID
	options.ThreadID = ""
	closeRun = func() {
		oneClose.Do(func() {
			closeChan <- struct{}{}

			// NOTE:专门开一个 goroutine 来取消 Run, 由于此时业务接口可能已经结束了,且不方便返回错误,因此这里错误直接使用 logit.DefaultLogger 记录
			go func() {
				defer func() {
					if err := recover(); err != nil {
						logit.DefaultLogger.Error(ctx, fmt.Sprintf("cancel run panic: %v", err))
					}
				}()

				if runID != "" {
					_, err := CancelRun(context.Background(), threadID, runID, func(ro *r.Options) {
						ro.AppID = reqOpts.AppID
						ro.Authorization = reqOpts.Authorization
						ro.Source = reqOpts.Source
					})
					if err != nil {
						logit.DefaultLogger.Error(ctx, fmt.Sprintf("cancel run error: %v", err))
					}
				}
			}()
		})
	}

	resp, err := r.StreamReq(
		ctx,
		"POST",
		fmt.Sprintf("/v1/threads/%s/runs", threadID),
		nil,
		options,
		nil,
		options.ReqOpts...,
	)
	if err != nil || resp == nil {
		return
	}

	go func() {
		defer func() {
			if r := recover(); r != nil {
				run <- &types.RunEvent{Error: fmt.Errorf("%v", r)}
			}

			if err := resp.Close(); err != nil {
				run <- &types.RunEvent{Error: err}
			}
			close(run)
		}()

		er := ghttp.EventReader{}

		if hresp := resp.Response(); hresp != nil {
			er.From = hresp.Body
		} else {
			return
		}

		for {
			select {
			case <-closeChan:
				return
			default:
			}

			ev, err := er.Next()
			if err != nil && !errors.Is(err, io.EOF) {
				run <- &types.RunEvent{Error: err}
				break
			}

			if errors.Is(err, io.EOF) {
				break
			}

			if ev.Event == "ping" {
				continue
			}

			reqOpts := r.NewOptions()
			for _, opt := range options.ReqOpts {
				opt(reqOpts)
			}

			if reqOpts.Verbose {
				if reqOpts.Logger != nil {
					// reqOpts.Logger.Debug(ctx, fmt.Sprintf("assistant stream response event: %s data: %s", ev.Event, ev.Data))
				} else {
					fmt.Println("event:", ev.Event)
					ds := string(ev.Data)
					if gjson.Valid(ds) {
						fmt.Println("event data:", gjson.Parse(ds).Get("@pretty"))
					} else {
						fmt.Println("event data:", string(ev.Data))
					}
				}
			}

			re := &types.RunEvent{}
			err = re.LoadFromEvent(&ev)
			// reb, _ := json.Marshal(re)
			// fmt.Println(string(reb))
			// fmt.Println("err:", err)
			if err != nil {
				run <- &types.RunEvent{Error: err}
				break
			}

			//  纪录一下 runID 方便后续 cancel
			if re.Type == types.RunEventStatus && re.StatusEvent != nil && re.StatusEvent.Type == types.RunStatusEventObjectTypeRunStep &&
				re.StatusEvent.RunStep != nil {
				runID = re.StatusEvent.RunStep.RunID
			}

			run <- re
		}
	}()

	return
}

// SubmitToolOutputs2Run 提交工具输出到 Run
func SubmitToolOutputs2Run(
	ctx context.Context,
	threadID, runID string,
	toolOutputs []types.ToolOutput,
	opts ...func(*r.Options),
) (run *types.Run, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		"POST",
		fmt.Sprintf("/v1/threads/%s/runs/%s/submit_tool_outputs", threadID, runID),
		nil,
		map[string]any{"tool_outputs": toolOutputs},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{Submitted: true}
	err = resp.Unmarshal(&run)
	return
}

// CancelRun 取消 Run
func CancelRun(ctx context.Context, threadID, runID string, opts ...func(*r.Options)) (run *types.Run, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		"POST",
		fmt.Sprintf("/v1/threads/%s/runs/%s/cancel", threadID, runID),
		nil,
		nil,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	run = &types.Run{}
	err = resp.Unmarshal(&run)
	return
}
