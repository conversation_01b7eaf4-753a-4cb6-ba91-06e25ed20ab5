package yiyan

import (
	"context"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

var _ interfaces.Files = (*Files)(nil)

// Files 表示一个文件管理的结构体，它包含一个客户端(Client)用于文件操作。
type Files struct {
	client interfaces.Client
}

// NewFiles 创建一个新的 Files 实例
func NewFiles(client interfaces.Client) *Files {
	return &Files{client: client}
}

// Create 将文件上传到Yiyan系统。
// 该函数通过在请求头中添加授权信息来实现文件上传。
//
// 参数:
//   - ctx: 上下文，包含请求的截止日期、取消信号等。
//   - fileName: 待上传文件的名称。
//   - fileContent: 待上传文件的内容。
//   - opts: 可选参数，用于配置上传选项。
//
// 返回值:
//   - types.File: 上传后返回的文件信息。
//   - error: 上传过程中的错误信息，如果有的话。
func (f *Files) Create(
	ctx context.Context,
	fileName, fileContent string,
	opts ...func(*types.UploadFileOptions),
) (ret types.File, err error) {
	opts = append(opts, func(ufo *types.UploadFileOptions) {
		if ufo != nil {
			ufo.ReqOpts = authorizeOpts(f.client, ufo.ReqOpts...)
		}
	})

	return UploadFile(ctx, fileName, fileContent, opts...)
}

// Retrieve 从Files中检索文件信息。
//
// 此方法将指定的文件ID（fileID）和上下文（ctx）传递到types.GetFileInfo方法中，并将可选的请求选项（opts）应用到请求中。
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期。
//   - fileID: 需要检索的文件ID。
//   - opts: 可选的函数，用于修改r.Options。
//
// 返回:
//   - files: 返回获取的文件信息（*types.File类型）。
//   - err: 如果请求过程中出现错误，返回错误信息。
func (f *Files) Retrieve(ctx context.Context, fileID string, opts ...func(*types.GetFileInfoOptions)) (files *types.File, err error) {
	opts = append(opts, func(gfio *types.GetFileInfoOptions) {
		if gfio != nil {
			gfio.ReqOpts = authorizeOpts(f.client, gfio.ReqOpts...)
		}
	})

	return GetFileInfo(ctx, fileID, opts...)
}

// List 方法检索 Yiyan 文件列表
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期
//   - opts: 可选参数，用于自定义 GetFiles 请求选项
//
// 返回值:
//   - files: 文件切片
//   - err: 错误信息
//
// 该方法会附加一个授权头到请求中，并调用 types.GetFiles 获取文件列表。
func (f *Files) List(ctx context.Context, opts ...func(*types.GetFilesOptions)) (files []*types.File, err error) {
	opts = append(opts, func(gfo *types.GetFilesOptions) {
		if gfo != nil {
			gfo.ReqOpts = authorizeOpts(f.client, gfo.ReqOpts...)
		}
	})

	return GetFiles(ctx, opts...)
}

// Delete 删除指定ID的文件。
//
// 参数:
//   - ctx: 上下文对象，用于控制操作的生命周期。
//   - fileID: 要删除的文件ID。
//   - opts: 变参选项函数，用于定制请求选项。
//
// 返回:
//   - err: 如果发生错误，返回相应的错误信息。
func (f *Files) Delete(ctx context.Context, fileID string, opts ...func(*types.DeleteFileOptions)) (err error) {
	opts = append(opts, func(dfo *types.DeleteFileOptions) {
		if dfo != nil {
			dfo.ReqOpts = authorizeOpts(f.client, dfo.ReqOpts...)
		}
	})

	return DeleteFile(ctx, fileID, opts...)
}

// Content 下载指定文件ID的内容。
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期。
//   - fileID: 需要下载的文件ID。
//   - opts: 可选的请求选项，允许调用者自定义请求行为。
//
// 返回值:
//   - content: 下载的文件内容，字节数组形式。
//   - err: 可能发生的错误，如果没有错误则为nil。
func (f *Files) Content(ctx context.Context, fileID string, opts ...func(*r.Options)) (content []byte, err error) {
	opts = authorizeOpts(f.client, opts...)

	return DownloadFile(ctx, fileID, opts...)
}
