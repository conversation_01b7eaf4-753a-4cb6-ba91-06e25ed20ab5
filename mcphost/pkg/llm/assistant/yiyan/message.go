package yiyan

import (
	"context"
	"fmt"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// CreateMessage NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func CreateMessage(
	ctx context.Context,
	threadID string,
	role types.Role,
	content string,
	opts ...func(*types.CreateMessageOptions),
) (msg *types.Message, err error) {
	options := &types.CreateMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	body := map[string]any{
		"role":    role,
		"content": content,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/threads/%s/messages", threadID),
		nil,
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	msg = &types.Message{}
	err = resp.Unmarshal(&msg)
	return
}

// GetMessage 从 Thread 中获取某个 Message
func GetMessage(ctx context.Context, threadID, messageID string, opts ...func(*r.Options)) (msg *types.Message, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/messages/%s", threadID, messageID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	msg = &types.Message{}
	err = resp.Unmarshal(&msg)
	return
}

// UpdateMessage 修改 Message
func UpdateMessage(
	ctx context.Context,
	threadID, messageID, content string,
	opts ...func(*types.UpdateMessageOptions),
) (ret *types.Message, err error) {
	options := &types.UpdateMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	body := map[string]any{
		"content": content,
	}

	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/threads/%s/messages/%s", threadID, messageID),
		nil,
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	ret = &types.Message{}
	err = resp.Unmarshal(&ret)
	return
}

// GetMessages 从指定的线程中获取消息。
//
// 参数:
//   - ctx: 上下文环境，用于控制请求的生命周期。
//   - threadID: 要获取消息的线程ID。
//   - opts: 可选参数，用于配置获取消息的行为。
//
// 返回值:
//   - msgs: 获取的消息列表。
//   - hasMore: 表示是否还有更多消息。
//   - err: 如果操作过程中出现错误，则返回错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetMessages(
	ctx context.Context,
	threadID string,
	opts ...func(*types.GetMessagesOptions),
) (msgs []*types.Message, hasMore bool, err error) {
	options := &types.GetMessagesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/messages", threadID),
		nil,
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.Message `json:"data"`
		HasMore bool             `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// GetMessageFiles 获取指定线程中某条消息的文件列表（版本1）。
//
// 参数：
//   - ctx: 上下文信息，用于控制请求的生命周期。
//   - threadID: 线程ID。
//   - messageID: 消息ID。
//   - opts: 可变参数，用于配置GetFilesInMessageOptions。
//
// 返回值：
//   - files: 文件挂载在消息上的列表。
//   - hasMore: 是否有更多文件。
//   - err: 错误信息，如果有则返回。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetMessageFiles(
	ctx context.Context,
	threadID, messageID string,
	opts ...func(*types.GetFilesInMessageOptions),
) (files []*types.FileMountOnMessage, hasMore bool, err error) {
	options := &types.GetFilesInMessageOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s/messages/%s/files", threadID, messageID),
		nil,
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type filesResp struct {
		Data    []*types.FileMountOnMessage `json:"data"`
		HasMore bool                        `json:"has_more"`
	}

	var filesRespData filesResp

	err = resp.Unmarshal(&filesRespData)
	return filesRespData.Data, filesRespData.HasMore, err
}
