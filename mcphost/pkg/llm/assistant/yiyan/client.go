package yiyan

import (
	"net/http"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
)

// Client 表示与 Yiyan 服务进行交互的客户端。
// 它包含助手、线程、文件模块的指针，以及用于认证的字符串。
//
// 成员:
// - assistants: 指向 Assistants 的指针，用于操作助手相关功能。
// - threads: 指向 Threads 的指针，用于操作线程相关功能。
// - files: 指向 Files 的指针，用于操作文件相关功能。
// - auth: 字符串，用于身份认证。
type Client struct {
	assistants *Assistants
	threads    *Threads
	files      *Files
	appID      string
}

// NewClient 创建一个新的 Client 实例。
// appID 一言提供的 appID
func NewClient(appID string) *Client {
	yc := &Client{
		appID: appID,
	}
	yc.assistants = NewAssistants(yc)
	yc.threads = NewThreads(yc)
	yc.files = NewFiles(yc)
	return yc
}

// Assistants 返回一个 Assistants 接口实例。
func (yc *Client) Assistants() interfaces.Assistants {
	if yc.assistants == nil {
		yc.assistants = NewAssistants(yc)
	}
	return yc.assistants
}

// Threads 返回一个 Threads 接口实例。
func (yc *Client) Threads() interfaces.Threads {
	if yc.threads == nil {
		yc.threads = NewThreads(yc)
	}
	return yc.threads
}

// Files 返回一个 Files 接口实例。
func (yc *Client) Files() interfaces.Files {
	if yc.files == nil {
		yc.files = NewFiles(yc)
	}
	return yc.files
}

// Authorize 为给定的 HTTP 头信息添加授权信息。
func (yc *Client) Authorize(header http.Header) http.Header {
	// Implement the method to add authorization information to the header
	if header == nil {
		return make(http.Header)
	}

	header.Set("AppId", yc.appID)
	return header
}
