package yiyan

import (
	"context"
	"errors"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/interfaces"
	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

var _ interfaces.Messages = (*Messages)(nil)

// Messages 消息相关操作
type Messages struct {
	client interfaces.Client
}

// NewMessages 创建一个新的 Messages 实例
// 参数:
//   - client: 一个实现了 Client 接口的客户端
//
// 返回值:
//   - 返回一个指向 Messages 结构体的指针
func NewMessages(client interfaces.Client) *Messages {
	return &Messages{client: client}
}

// Create 函数创建一个新的消息。
//
// 参数:
//   - ctx: context 上下文，用于控制请求的生命周期
//   - threadID: 线程 ID，用于标识消息所属的线程
//   - role: 消息的角色，如用户或系统
//   - content: 消息的内容
//   - fileIDs(optional): 可选参数,文件ID列表，表示消息关联的文件。
//   - metaData(optional): 可选参数, 消息的元数据
//   - opts: 可选的请求配置函数
//
// 返回值:
//   - *types.Message: 创建的消息对象
//   - error: 可能的错误，如果发生错误则返回
func (m *Messages) Create(ctx context.Context,
	threadID string,
	role types.Role,
	content string,
	opts ...func(*types.CreateMessageOptions),
) (*types.Message, error) {
	opts = append(opts, func(cmo *types.CreateMessageOptions) {
		if cmo != nil {
			cmo.ReqOpts = authorizeOpts(m.client, cmo.ReqOpts...)
		}
	})

	return CreateMessage(ctx, threadID, role, content, opts...)
}

// Retrieve 从指定的线程中检索一条消息。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - threadID: 线程的唯一标识符。
//   - messageID: 消息的唯一标识符。
//   - opts: 可选参数，用于配置请求选项。
//
// 返回值:
//   - *types.Message: 检索到的消息对象。
//   - error: 执行过程中产生的错误信息。
func (m *Messages) Retrieve(ctx context.Context, threadID, messageID string, opts ...func(*r.Options)) (*types.Message, error) {
	opts = authorizeOpts(m.client, opts...)

	return GetMessage(ctx, threadID, messageID, opts...)
}

// Update 更新指定的消息内容。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - threadID: 线程ID，表示消息所在的线程。
//   - messageID: 消息ID，表示需要更新的消息。
//   - content: 新的消息内容。
//   - fileIDs(optional): 可选参数,文件ID列表，表示消息关联的文件。
//   - opts: 可选参数，用于配置请求选项。
//
// 返回值:
//   - *types.Message: 更新后的消息对象。
//   - error: 如果更新失败，返回错误信息。
func (m *Messages) Update(
	ctx context.Context,
	threadID, messageID, content string,
	opts ...func(*types.UpdateMessageOptions),
) (*types.Message, error) {
	opts = append(opts, func(umo *types.UpdateMessageOptions) {
		if umo != nil {
			umo.ReqOpts = authorizeOpts(m.client, umo.ReqOpts...)
		}
	})

	return UpdateMessage(ctx, threadID, messageID, content, opts...)
}

// List 获取指定线程的消息列表。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - threadID: 线程的唯一标识符。
//   - opts: 用于配置获取消息选项的可变参数函数。
//
// 返回:
//   - msgs: 获取到的消息列表。
//   - hasMore: 是否有更多的消息。
//   - err: 获取消息过程中发生的错误。
func (m *Messages) List(
	ctx context.Context,
	threadID string,
	opts ...func(*types.GetMessagesOptions),
) (msgs []*types.Message, hasMore bool, err error) {
	opts = append(opts, func(gmo *types.GetMessagesOptions) {
		if gmo != nil {
			gmo.ReqOpts = authorizeOpts(m.client, gmo.ReqOpts...)
		}
	})

	return GetMessages(ctx, threadID, opts...)
}

// DeleteLastMessage 删除指定线程中最后一个消息。
//
// 参数:
//   - ctx: 上下文对象，用于控制请求的生命周期。
//   - threadID: 线程的唯一标识符。
//   - runID: 运行ID，用于标识消息所属的运行。
//   - opts: 用于配置获取消息选项的可变参数函数。
//
// 返回:
//   - deleted: 是否删除成功。
//   - err: 执行过程中发生的错误信息。
func (m *Messages) DeleteLastMessage(
	ctx context.Context,
	threadID, runID string,
	opts ...func(*types.DeleteLastMessageOptions),
) (deleted bool, err error) {
	return false, errors.New("not implemented")
}
