package yiyan

import (
	"context"
	"fmt"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// CreateThread 创建一个新的线程。
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期。
//   - messages: 消息列表，包含该线程中的消息。
//   - metadata: 元数据，包含一些键值对的额外信息。
//   - opts: 可选参数，用于配置请求选项。
//
// 返回值:
//   - ret: 新创建的线程对象。
//   - err: 如果创建过程中发生错误，返回错误信息。
func CreateThread(
	ctx context.Context,
	messages []*types.Message,
	metadata map[string]any,
	opts ...func(*r.Options),
) (ret *types.Thread, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	body := map[string]any{
		"messages": messages,
		"metadata": metadata,
	}

	reqRet, err := r.Req(
		ctx,
		"POST",
		"/v1/threads",
		nil,
		body,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// GetThread 获取指定线程的详细信息。
// 参数:
//   - ctx: 请求的上下文，支持取消和超时控制。
//   - threadID: 线程的唯一标识符。
//   - opts: 可选函数，用于配置请求选项。
//
// 返回值:
//   - ret: 线程的详细信息。
//   - err: 错误信息，如果请求失败则返回相关错误。
func GetThread(ctx context.Context, threadID string, opts ...func(*r.Options)) (ret *types.Thread, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	reqRet, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/threads/%s", threadID),
		nil,
		nil,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// UpdateThread 更新特定 ID 的线程信息
//
// 这个函数会根据提供的线程 ID 和元数据更新相应的线程信息。
//
// 参数:
//   - ctx: 请求的上下文信息
//   - threadID: 线程的唯一标识符
//   - metadata: 包含线程元数据信息的 map
//   - opts: 可选的请求配置项
//
// 返回值:
//   - ret: 更新后的线程信息
//   - err: 请求过程中发生的错误信息（如果有）
func UpdateThread(
	ctx context.Context,
	threadID string,
	metadata map[string]any,
	opts ...func(*r.Options),
) (ret *types.Thread, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	reqRet, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/threads/%s", threadID),
		nil,
		map[string]any{"metadata": metadata},
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = reqRet.Error(); err != nil {
		return
	}

	ret = &types.Thread{}
	err = reqRet.Unmarshal(ret)
	return
}

// DeleteThread 删除指定线程。
//
// 该函数发送一个HTTP DELETE请求以删除指定的线程。
//
// 参数:
//   - ctx - 上下文对象，用于控制请求的生命周期。
//   - threadID - 要删除的线程ID。
//   - opts - 可选的请求配置函数。
//
// 返回值:
//   - err - 如果请求过程中出现错误，返回相应的错误信息。
func DeleteThread(ctx context.Context, threadID string, opts ...func(*r.Options)) (err error) {
	opts = r.SetYiYanReqOpts(opts...)

	repRet, err := r.Req(
		ctx,
		http.MethodDelete,
		fmt.Sprintf("/v1/threads/%s/delete", threadID),
		nil,
		nil,
		nil,
		opts...)
	if err != nil {
		return
	}

	if err = repRet.Error(); err != nil {
		return
	}
	return
}
