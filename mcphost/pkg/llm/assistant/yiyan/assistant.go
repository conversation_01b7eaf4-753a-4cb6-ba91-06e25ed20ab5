package yiyan

import (
	"context"
	"fmt"
	"net/http"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// CreateAssistant 创建一个新的助手。
//
// 参数：
//   - ctx: 上下文，用于控制请求的生命周期。
//   - model: 模型信息，指定要使用的模型。
//   - name: 助手的名称。
//   - description: 助手的描述。
//   - instructions: 助手的使用说明。
//   - tools: 工具集合，如果传入 nil 则自动赋值为空列表。
//   - opts: 可选配置函数，用于自定义创建助手的行为。
//
// 返回值：
//   - assistantInfo: 创建的助手信息。
//   - err: 在创建过程中遇到的错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func CreateAssistant(
	ctx context.Context,
	model types.Model,
	name, description, instructions string,
	tools []types.Tool,
	opts ...func(*types.CreateAssistantOptions),
) (assistantInfo *types.Assistant, err error) {
	options := &types.CreateAssistantOptions{}
	for _, opt := range opts {
		opt(options)
	}

	if tools == nil {
		tools = []types.Tool{}
	}

	body := map[string]any{
		"model":        model,
		"name":         name,
		"description":  description,
		"instructions": instructions,
		"tools":        tools,
	}

	options.Apply(body)
	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		"/v1/assistants",
		nil,
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistantInfo = &types.Assistant{}
	err = resp.Unmarshal(&assistantInfo)
	return
}

// GetAssistant 获取指定ID的助理信息。
//
// 参数:
//   - ctx: 请求的上下文，用于控制请求的生命周期。
//   - assistantID: 助理的唯一标识符。
//   - opts: 可选的请求参数配置函数，用于自定义请求选项。
//
// 返回值:
//   - assistant: 返回包含助理信息的Assistant结构体指针。
//   - err: 如果请求失败或处理响应时出错，则返回相应的错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetAssistant(ctx context.Context, assistantID string, opts ...func(*r.Options)) (assistant *types.Assistant, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/assistants/%s", assistantID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistant = &types.Assistant{}
	err = resp.Unmarshal(&assistant)
	return
}

// GetAssistants 获取助手列表。
//
// 通过提供的选项函数，可自定义查询参数。
//
// 参数:
//   - ctx: 操作的上下文信息。
//   - opts: 可选的配置函数，用于修改查询选项。
//
// 返回值:
//   - assistants: 返回的助手列表。
//   - hasMore: 标识是否有更多助手的数据。
//   - err: 错误信息，如果有的话。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetAssistants(
	ctx context.Context,
	opts ...func(*types.GetAssistantsOptions),
) (assistants []*types.Assistant, hasMore bool, err error) {
	options := &types.GetAssistantsOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		"/v1/assistants",
		nil,
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.Assistant `json:"data"`
		HasMore bool               `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// UpdateAssistant 更新指定ID的助手信息
//
// 该函数接受一个上下文、助手ID和可选的修改选项，并返回更新后的助手对象和错误信息。
//
// 参数:
//   - ctx: 上下文，用于控制请求的生命周期。
//   - assistantID: 要更新的助手ID。
//   - opts: 可选的修改助手选项的函数。
//
// 返回值:
//   - assistant: 更新后的助手对象。
//   - err: 请求过程中的错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func UpdateAssistant(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.UpdateAssistantOptions),
) (assistant *types.Assistant, err error) {
	options := &types.UpdateAssistantOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	if assistantID == "" {
		return nil, fmt.Errorf("assistantID is empty")
	}

	body := map[string]any{}
	body["assistant_id"] = assistantID
	options.Apply(body)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/assistants/%s", assistantID),
		nil,
		body,
		nil,
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	assistant = &types.Assistant{}
	err = resp.Unmarshal(&assistant)
	return
}

// DeleteAssistant 删除助理。
// 该函数接受一个上下文、助理ID和可选参数，并发送删除请求以移除指定的助理。
//
// 参数：
//   - ctx：context.Context，上下文信息
//   - assistantID：string，助理ID
//   - opts：可变参数，函数类型，用于配置请求选项
//
// 返回：
//   - err：error，操作过程中发生的错误，如果成功删除则返回nil
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func DeleteAssistant(ctx context.Context, assistantID string, opts ...func(*r.Options)) (err error) {
	opts = r.SetYiYanReqOpts(opts...)

	del, err := r.Req(
		ctx,
		http.MethodDelete,
		fmt.Sprintf("/v1/assistants/%s", assistantID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	ret, err := del.Map()
	if err != nil {
		return
	}

	if ret != nil {
		if deleted, ok := ret["deleted"].(bool); ok && !deleted {
			return fmt.Errorf("delete assistant failed, %v", ret)
		}
	}

	return
}

// GetAssistantFiles 获取助手的文件列表。
//
// 参数:
//   - ctx: 上下文用于控制请求的生命周期。
//   - assistantID: 助手的唯一标识符。
//   - opts: 可选参数用于配置获取文件的选项。
//
// 返回值:
//   - files: 文件列表，包含挂载在助手上的文件信息。
//   - hasMore: 是否有更多的文件未返回。
//   - err: 如果发生错误，将返回错误信息。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func GetAssistantFiles(
	ctx context.Context,
	assistantID string,
	opts ...func(*types.GetAssistantFilesOptions),
) (files []*types.FileMountOnAssistant, hasMore bool, err error) {
	options := &types.GetAssistantFilesOptions{}
	for _, opt := range opts {
		opt(options)
	}

	options.ReqOpts = r.SetYiYanReqOpts(options.ReqOpts...)

	resp, err := r.Req(
		ctx,
		http.MethodGet,
		fmt.Sprintf("/v1/assistants/%s/files", assistantID),
		nil,
		nil,
		options.Query(),
		options.ReqOpts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	type ret struct {
		Data    []*types.FileMountOnAssistant `json:"data"`
		HasMore bool                          `json:"has_more"`
	}

	var r ret
	err = resp.Unmarshal(&r)
	return r.Data, r.HasMore, err
}

// AddFileToAssistant 将文件添加到助手
//
// 参数:
//   - ctx: context.Context 上下文，用于控制请求的生命周期
//   - assistantID: string 助手的ID
//   - fileID: string 文件的ID
//   - opts: ...func(*r.Options) 可选的请求配置函数
//
// 返回:
//   - file: *FileMountOnAssistant 与助手关联的文件对象
//   - err: error 错误信息，如果有错误发生则返回错误信息
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func AddFileToAssistant(
	ctx context.Context,
	assistantID, fileID string,
	opts ...func(*r.Options),
) (file *types.FileMountOnAssistant, err error) {
	opts = r.SetYiYanReqOpts(opts...)

	resp, err := r.Req(
		ctx,
		http.MethodPost,
		fmt.Sprintf("/v1/assistants/%s/files", assistantID),
		nil,
		map[string]any{"file_id": fileID},
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = resp.Error(); err != nil {
		return
	}

	file = &types.FileMountOnAssistant{}
	err = resp.Unmarshal(&file)
	return
}

// DeleteFileFromAssistant 从助理系统中删除指定文件
//
// 该函数用于从指定的助理（assistantID）中删除一个文件（fileID）。
//
// 参数:
// - ctx: 上下文（context.Context），用于控制请求的生命周期。
// - assistantID: 助理ID（string），用于标识从哪个助理中删除文件。
// - fileID: 文件ID（string），用于标识要删除的文件。
// - opts: 可变参数（可选），用于修改请求选项（func(*r.Options)）。
//
// 返回值:
// - err: 错误信息（error），如果删除操作失败则返回相关错误。
//
// NOTE: 此函数后端使用一言后端, 默认测试 appID 为 123456, 如果需要修改 appID, 请通过 option 中的 ReqOpts 进行控制
func DeleteFileFromAssistant(ctx context.Context, assistantID, fileID string, opts ...func(*r.Options)) (err error) {
	opts = r.SetYiYanReqOpts(opts...)

	del, err := r.Req(
		ctx,
		http.MethodDelete,
		fmt.Sprintf("/v1/assistants/%s/files/%s", assistantID, fileID),
		nil,
		nil,
		nil,
		opts...,
	)
	if err != nil {
		return
	}

	if err = del.Error(); err != nil {
		return
	}

	ret, err := del.Map()
	if err != nil {
		return
	}

	if ret != nil {
		if deleted, ok := ret["deleted"].(bool); ok && !deleted {
			return fmt.Errorf("delete file from assistant failed, %v", ret)
		}
	}

	return
}
