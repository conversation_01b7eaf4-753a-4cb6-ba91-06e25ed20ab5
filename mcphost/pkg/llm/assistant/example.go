package assistant

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/gdp/logit"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
	"github.com/mark3labs/mcphost/utils"
)

const GetWeatherFunc = `{
	"name": "get_current_weather",
	"description": "获得指定地点的天气",
	"examples": [
		{
			"role": "user",
			"content": "波士顿的天气怎么样？"
		},
		{
			"role": "assistant",
			"content": null,
			"function_call": {
				"name": "get_current_weather",
				"arguments": "{ \"location\": \"Boston, MA\"}"
			}
		}
	],
	"parameters": {
		"type": "object",
		"properties": {
			"location": {
				"type": "string",
				"description": "省，市名，例如：河北省"
			},
			"unit": {
				"type": "string",
				"enum": [
					"摄氏度",
					"华氏度"
				]
			}
		},
		"required": [
			"location"
		]
	},
	"responses": {
		"type": "object",
		"properties": {
			"temperature": {
				"type": "number",
				"description": "当前温度"
			}
		},
		"required": [
			"temperature"
		]
	}
}`

func GetWeatherFuncMap() map[string]any {
	var ret map[string]any
	err := json.Unmarshal([]byte(GetWeatherFunc), &ret)
	if err != nil {
		panic(err)
	}
	return ret
}

func main() {
	ctx := logit.NewContext(context.Background())
	err := utils.RegisterServer(utils.ServerToml{
		Name:              "assistant_v1",
		ConnectionTimeOut: 1000000,
		WriteTimeOut:      1000000,
		ReadTimeOut:       2000000,
		Retry:             0,
		IP:                "*************",
		Port:              8361,
	})
	if err != nil {
		panic(err)
	}

	const (
		Model = "ERNIE-4.5-single-custom-yiyanweb"
	)

	var (
		ThreadID string
		RunID    string
		AssistID string
	)

	tools := []types.Tool{
		{
			Type:     types.FunctionTool,
			Function: GetWeatherFuncMap(),
		},
	}

	AppID := "123456"
	// 现在默认 token
	AssistantAuthorization := "testldx/1722333193666/9c2285103869105c43191fd48abb420eadf6286de45c7576e3bd3130168a606f"
	// 方便 debug
	req.Verbose = true
	// assistant.Logger = logit.DefaultLogger

	client := NewYiyanClient(AppID)

	a, err := client.Assistants().Create(
		ctx,
		Model,
		"weather_assistant",
		"帮助用户查询天气的助手",
		`你可以帮助用户查询各个城市的天气情况,并且整理成易于理解的格式。`,
		tools,
		[]func(*types.CreateAssistantOptions){
			func(o *types.CreateAssistantOptions) {
				o.ReqOpts = []func(*req.Options){
					func(o *req.Options) {
						o.Authorization = AssistantAuthorization
					},
				}
			},
		}...,
	)
	if err != nil {
		panic(fmt.Errorf("CreateAssistant: %w", err))
	}

	AssistID = a.ID
	// AssistID = "asst_0f7d44d2843e4fb6a79a410155c31341"
	fmt.Println("AssistID:", AssistID)

	opts := []func(*req.Options){
		func(o *req.Options) {
			o.Authorization = AssistantAuthorization
		},
	}
	thread, err := client.Threads().Create(ctx, []*types.Message{}, nil, opts...)
	if err != nil {
		panic(fmt.Errorf("CreateThread: %w", err))
	}

	ThreadID = thread.ID
	// ThreadID = "thread_03b95b99d1584fb2bdafd1b96e5ecbac"
	fmt.Println("ThreadID:", ThreadID)
	startOpts := []func(*types.StartRunOptions){
		func(sro *types.StartRunOptions) {
			sro.Tools = tools
			sro.ThreadID = ThreadID
			sro.Thread = types.RequestThread{
				Messages: []types.ChatMessage{
					{
						Role:    types.RoleUser,
						Content: "今天北京天气怎么样？",
					},
				},
			}
			sro.ReqOpts = opts
			sro.Model = Model
			sro.Stream = false
		},
	}

	run, _, err := client.Threads().Runs().CreateAndStream(ctx, AssistID, startOpts...)
	if err != nil {
		panic(fmt.Errorf("StartRunOnThread: %w", err))
	}

	wt := time.NewTimer(6000 * time.Second)

	for {
		select {
		case <-wt.C:
			panic("timeout")
		case evt, ok := <-run:
			if !ok {
				// fmt.Println("run closed")
				return
			}

			if evt.Error != nil {
				// fmt.Println("evt error:", evt.Error)
				// fmt.Println("evt:", gjson.ParseBytes(evt.Bytes()).Get("@pretty").String())
				return
			}

			// evtb, _ := json.Marshal(evt)

			// fmt.Println("evt:", gjson.ParseBytes(evtb).Get("@pretty").String())

			switch evt.Type {
			case types.RunEventMessage:
				em := evt.MessageEvent
				for _, m := range em.Content {
					fmt.Println("model:", m.Text.Value)
				}
			case types.RunEventStatus:
				es := evt.StatusEvent
				switch es.Type {
				case types.RunStatusEventObjectTypeRunStep:
					// 因为是动态创建的 thread 和 run 因此需要在 RunStep 事件中获取 ThreadID 和 RunID
					ThreadID = es.RunStep.ThreadID
					RunID = es.RunStep.RunID
					// fmt.Println("ThreadID:", ThreadID, "RunID:", RunID)
				case types.RunStatusEventObjectTypeToolCalls:
					for _, m := range es.ToolCalls {
						fmt.Println("模型调用工具 ID:", m.ID, "function:", m.Function.Name, "arguments:", m.Function.Arguments)
						// 提交工具调用的输出
						_, err := client.Threads().Runs().SubmitToolOutputs(ctx, ThreadID, RunID, []types.ToolOutput{
							{
								ToolCallID: m.ID,
								Output:     "{\"temperature\": 21 }",
							},
						}, opts...)
						if err != nil {
							fmt.Println("submit tool output error:", err)
						}
					}
				}
			}
		}
	}
}
