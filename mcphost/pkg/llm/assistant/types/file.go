package types

import r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"

// File 结构体表示一个文件对象。
type File struct {
	Bytes            float64 `json:"bytes,omitempty"`
	ClassificationID string  `json:"classification_id,omitempty"`
	CreateAt         float64 `json:"create_at,omitempty"`
	File             string  `json:"file,omitempty"`
	FileName         string  `json:"filename,omitempty"`
	ID               string  `json:"id,omitempty"`
	Properties       string  `json:"properties,omitempty"`
	Purpose          string  `json:"purpose,omitempty"`
}

// UploadFileOptions 上传文件的选项
type UploadFileOptions struct {
	// ClassificationID 分类ID
	// NOTE: 只有一言后端支持此字段
	// 千帆后端默认此字段为 AppID
	// 本代码库所有一言函数默认实现也给此字段赋值为 AppID
	ClassificationID string
	// Purpose  文件用途 默认为assistant
	Purpose string
	// ReqOpts 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

// GetFilesOptions 获取文件列表的选项
type GetFilesOptions struct {
	// ClassificationID 分类
	// NOTE: 只有一言后端支持此字段
	// 千帆后端默认此字段为 AppID
	// 本代码库所有一言函数默认实现也给此字段赋值为 AppID
	ClassificationID string
	// Purpose 文件用途
	// NOTE: 只有一言后端支持此字段
	Purpose string
	// ReqOpt 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

// GetFileInfoOptions 获取文件信息的选项
type GetFileInfoOptions = GetFilesOptions

// DeleteFileOptions 删除文件的选项
type DeleteFileOptions = GetFilesOptions

// FileRegisterInput 文件注册的输入
type FileRegisterInput struct {
	FileURL  string         `json:"file_url"`
	FileName string         `json:"file_name"`
	FileSize int64          `json:"file_size"`
	Purpose  string         `json:"purpose,omitempty"`
	MetaData map[string]any `json:"metadata,omitempty"`
}

// FileRegisterOutput 文件输出对象
type FileRegisterOutput struct {
	ID               string         `json:"id"`
	Bytes            int            `json:"bytes"`
	Object           string         `json:"object"`
	Purpose          string         `json:"purpose"`
	Properties       string         `json:"properties"`
	Censored         int            `json:"censored"`
	CreateAt         int            `json:"create_at"`
	CreatedAt        int            `json:"created_at"`
	Filename         string         `json:"filename"`
	ClassificationId string         `json:"classification_id"`
	FileType         string         `json:"file_type"`
	MetaData         map[string]any `json:"metadata"`
	FileURL          string         `json:"file_url"`
}
