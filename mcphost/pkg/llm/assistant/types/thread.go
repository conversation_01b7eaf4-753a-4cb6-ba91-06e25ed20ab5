package types

// APPID 请求者的 APPID
// string
type APPID string

// Thread 会话
type Thread struct {
	ID        string         `json:"id,omitempty"`
	Object    string         `json:"object,omitempty"`
	MetaData  map[string]any `json:"meta_data,omitempty"`
	CreatedAt int64          `json:"created_at,omitempty"`
}

// RequestThread 运行携带的message
type RequestThread struct {
	MetaData map[string]any `json:"metadata,omitempty"`
	Messages []ChatMessage  `json:"messages,omitempty"`
}
