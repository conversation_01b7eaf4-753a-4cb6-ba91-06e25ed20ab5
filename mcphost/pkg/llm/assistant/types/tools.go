package types

// ToolType 工具类型
type ToolType string

// Tool 内置 工具
type Tool struct {
	Type     ToolType `json:"type"`
	Plugin   any      `json:"plugin,omitempty"`
	Function any      `json:"function,omitempty"`
}

// Plugin 内置插件
type Plugin struct {
	Name        string `json:"name"`
	OperationID string `json:"operation_id"`
}

var (
	// ToolCodeInterpreter 代码解释器, 代码解释器能够生成并执行代码，从而协助用户解决复杂问题，涵盖科学计算（包括普通数学计算题）、数据可视化、
	// 文件编辑处理（图片、PDF文档、视频、音频等）、文件格式转换（如WAV、MP3、text、SRT、PNG、jpg、MP4、GIF、MP3等）、数据分析&清洗&处理（文件以excel、csv格式为主）、机器学习&深度学习建模&自然语言处理等多个领域。
	ToolCodeInterpreter = Tool{
		Type: "code_interpreter",
	}

	// ToolWebSearch 网页检索工具,根据你的知识库，无法准确地回答用户问题时，调用该工具搜索查询用户所需的外部信息。注意该工具无法提供图片、视频等非文本内容。
	ToolWebSearch = Tool{
		Type: "web_search",
	}

	// ToolFileRetrieval 知识库检索, 该工具能基于用户提供的文档完成如下3类任务：文档摘要总结任务、文档问答任务、文档创作类任务。
	ToolFileRetrieval = Tool{
		Type: "file_retrieval",
	}

	// ToolImageChat 图生文工具, image_chat工具能够理解并识别图片内容，以自然语言的形式将图片内容信息描述出来
	ToolImageChat = Tool{
		Type: "image_chat",
	}

	// ToolImageGen 文生图工具, 图片生成工具能基于自然语言描述指令生成图片，并返回图片生成结果的文件列表
	ToolImageGen = Tool{
		Type: "image_gen",
	}
	// ToolKnowledgeBaseRetrievalPlus 整合工具，会同时进行文档检索(knowledge_base_retrieval)和联网搜索(web_search).该工具需要以plugin的形式传递
	ToolKnowledgeBaseRetrievalPlus = Tool{
		Type: PluginTool,
		Plugin: Plugin{
			Name: "knowledge_base_retrieval_plus",
		},
	}
	// ToolKnowledgeBaiZhongRetrieval 一言内置工具，知识库检索工具
	ToolKnowledgeBaiZhongRetrieval = Tool{
		Type: PluginTool,
		Plugin: Plugin{
			Name:        "knowledge_baizhong_retrieval",
			OperationID: "search",
		},
	}
)
