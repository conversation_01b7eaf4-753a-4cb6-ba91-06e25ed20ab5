package types

import (
	"fmt"
	"net/url"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
)

// Model 模型
type Model string

// ResponseFormat 返回格式
type ResponseFormat string

// Assistant 助手信息
type Assistant struct {
	ChatInstructions    string         `json:"chat_instructions,omitempty"`
	CreatedAt           int64          `json:"created_at,omitempty"`
	Description         string         `json:"description,omitempty"`
	FileIDs             []string       `json:"file_ids,omitempty"`
	ID                  string         `json:"id,omitempty"`
	Instructions        string         `json:"instructions,omitempty"`
	Metadata            map[string]any `json:"metadata,omitempty"`
	Model               string         `json:"model,omitempty"`
	Name                string         `json:"name,omitempty"`
	Object              string         `json:"object,omitempty"`
	ResponseFormat      string         `json:"response_format,omitempty"`
	ThoughtInstructions string         `json:"thought_instructions,omitempty"`
	Tools               []Tool         `json:"tools,omitempty"`
	UserStorage         string         `json:"user_storage,omitempty"`
	// 用于标识此信息是否是对外函数产生的,对于对外函数产生的信息可以直接在这个基础上调用方法
	canOperate bool `json:"-"`
}

// CreateAssistantOptions 创建助手选项
type CreateAssistantOptions struct {
	// UserStorage 用户偏好设定
	// NOTE: 该字段暂时不支持设置,传递无用
	UserStorage string
	// FileIDs  文件 ID
	FileIDs []string
	// ResponseFormat  返回格式
	ResponseFormat ResponseFormat
	// ChatInstructions  聊天规范指令
	ChatInstructions string
	// ThoughtInstructuions  思考规范指令
	ThoughtInstructuions string
	MetaData             map[string]any

	// 控制请求的选项
	ReqOpts []func(*r.Options)
}

// Apply 应用选项
func (cao *CreateAssistantOptions) Apply(body map[string]any) {
	if cao.UserStorage != "" {
		body["user_storage"] = cao.UserStorage
	}
	if len(cao.FileIDs) > 0 {
		body["file_ids"] = cao.FileIDs
	}
	if cao.ResponseFormat != "" {
		body["response_format"] = cao.ResponseFormat
	}
	if cao.ChatInstructions != "" {
		body["chat_instructions"] = cao.ChatInstructions
	}
	if cao.ThoughtInstructuions != "" {
		body["thought_instructions"] = cao.ThoughtInstructuions
	}
	if len(cao.MetaData) > 0 {
		body["meta_data"] = cao.MetaData
	}
}

// UpdateAssistantOptions 更新助手选项
type UpdateAssistantOptions struct {
	CreateAssistantOptions
	Model        Model
	Name         string
	Description  string
	Instructions string
	Tools        []Tool
}

func (mao *UpdateAssistantOptions) Apply(body map[string]any) {
	mao.CreateAssistantOptions.Apply(body)
	if mao.Model != "" {
		body["model"] = mao.Model
	}
	if mao.Name != "" {
		body["name"] = mao.Name
	}
	if mao.Description != "" {
		body["description"] = mao.Description
	}
	if mao.Instructions != "" {
		body["instructions"] = mao.Instructions
	}
	if len(mao.Tools) > 0 {
		body["tools"] = mao.Tools
	}
}

type GetAssistantsOptions struct {
	// Limit 默认 20
	Limit int `json:"limit,omitempty"`
	// Order desc/asc
	Order string `json:"order,omitempty"`
	// After 从某个 AssistantID 之后
	After string `json:"after,omitempty"`
	// Before 从某个 AssistantID 之前
	Before string `json:"before,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (gao *GetAssistantsOptions) Query() url.Values {
	v := url.Values{}
	if gao.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", gao.Limit))
	}
	if gao.Order != "" {
		v.Set("order", gao.Order)
	}
	if gao.After != "" {
		v.Set("after", gao.After)
	}
	if gao.Before != "" {
		v.Set("before", gao.Before)
	}
	return v
}

// FileMountOnAssistant assistant绑定的文件信息
type FileMountOnAssistant struct {
	AssistantID string  `json:"assistant_id,omitempty"`
	CreatedAt   float64 `json:"created_at,omitempty"`
	ID          string  `json:"id,omitempty"`
	Object      string  `json:"object,omitempty"`
}

type GetAssistantFilesOptions struct {
	// AssistantID 助手 ID
	AssistantID string `json:"assistant_id,omitempty"`
	// Limit 默认 20
	Limit int `json:"limit,omitempty"`
	// Order desc/asc
	Order string `json:"order,omitempty"`
	// After 从某个 RunID 之后
	After string `json:"after,omitempty"`
	// Before 从某个 RunID 之前
	Before string `json:"before,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (gafo *GetAssistantFilesOptions) Query() url.Values {
	v := url.Values{}
	if gafo.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", gafo.Limit))
	}
	if gafo.Order != "" {
		v.Set("order", gafo.Order)
	}
	if gafo.After != "" {
		v.Set("after", gafo.After)
	}
	if gafo.Before != "" {
		v.Set("before", gafo.Before)
	}
	return v
}
