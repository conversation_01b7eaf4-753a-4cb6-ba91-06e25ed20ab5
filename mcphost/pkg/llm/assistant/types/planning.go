package types

import (
	"encoding/json"
	"fmt"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"icode.baidu.com/baidu/gdp/ghttp"
)

// PlanningOptions planning方法的可选参数
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/MYFIkxNl2o/-UXFkEkRyR/U4YdW7gpWY0LIM
type PlanningOptions struct {
	// 可为空。会话ID，相当于业务方的sessionId。如果不传，相当于不关联thread对象。
	ThreadID string `json:"thread_id,omitempty"`
	// 可为空，智能体ID（由业务方传入），一般应该为Assistant-API创建的Assistant ID。如果不传，相当于不关联Assistant对象。
	AssistantID string `json:"assistant_id,omitempty"`
	// 本次对话追加的thread
	Thread *RequestThread `json:"thread"`
	// 模型名称极简模式下必填；支持非极简模式后，此字段和assistant_id所指向的对象的model字段二者之一非空。 示例：ERNIE-3.5-8K、ERNIE-Speed-8K
	Model string `json:"model"`
	// Stream 流式处理
	Stream bool `json:"stream"`
	// 系统人设指令
	// 长度限制4096，所有用户输入的token和需要小于4096
	// 概述Agent的整体功能和定位，需要它扮演一个什么样的"角色。
	// 1. 可以参考“你是xxx，你具有xx的特点，你可以做xxx，你需要满足用户xxx的需求”这样的结构。
	// 2. 可以指定一些与能力边界相关要求（对于哪些类型的问题，无法回答）
	// 示例：
	//	你是一个音乐智能语音助手，可以通过调用技能的方式满足用户听音乐，看电影，追剧，刷短视频，听有声读物，信息问答等需求。你无法提供与政治和政策相关的内容；
	//	当用户提出与音乐、视频等娱乐信息不相关的问题时，你需要委婉地拒绝用户。
	Instructions string `json:"instructions"`
	// 思考指令
	// 建议将thought_instructions字段的相关内容，合并填入instructions字段中。
	ThoughtInstructions string `json:"thought_instructions,omitempty"`
	// 回复指令
	// 仅用于回复工具启用的场景。建议将chat_instructions字段的相关内容，合并填入instructions字段中
	ChatInstructions string `json:"chat_instructions,omitempty"`
	// 模型参数
	// 模型超参定义，包含思考模型和回复模型，参考公共对象[公共对象定义] (doc-346704)
	ModelParameters *ModelParameters `json:"model_parameters,omitempty"`
	// 工具函数
	Tools []Tool `json:"tools"`
	// 元数据
	// 一组可以附加到对象的16个键值对。这对于以结构化格式存储关于对象的附加信息非常有用。键的长度最多为64个字符，值的长度最多可为512个字符
	MetaData map[string]any `json:"metadata,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

// PlanningResp planning响应结果
type PlanningResp struct {
	Code    int            `json:"code"`    // 状态码
	Message string         `json:"message"` // 当前的状态描述
	LogID   string         `json:"logId"`   // 日志id
	Data    PlanningObject `json:"data"`    // 返回值
}

// PlanningObject planningResp返回的数据
type PlanningObject struct {
	ID                  string           `json:"id,omitempty"`               // 任务ID
	Object              string           `json:"object,omitempty"`           // 对象
	Status              string           `json:"status"`                     // 状态
	Model               string           `json:"model"`                      // 模型名称
	Planned             bool             `json:"planned"`                    // 是否已计划
	PlannedResult       *PlannedResult   `json:"planned_result,omitempty"`   // 计划结果
	Usage               *Usage           `json:"usage,omitempty"`            // 使用情况
	Tools               []Tool           `json:"tools"`                      // FunctionCall工具
	Metadata            map[string]any   `json:"metadata"`                   // 元信息
	Instructions        string           `json:"instructions"`               // 系统人设指令
	ThoughtInstructions string           `json:"thought_instructions"`       // 思考规范
	ChatInstructions    string           `json:"chat_instructions"`          // 回复规范
	Thread              RequestThread    `json:"thread"`                     // 本次对话追加的thread
	CompletedAt         int              `json:"completed_at"`               // 结束时间
	CreatedAt           int              `json:"created_at"`                 // 创建时间
	StartedAt           int              `json:"started_at"`                 // 开始时间
	ModelParameters     *ModelParameters `json:"model_parameters,omitempty"` // 模型参数
	LastError           *LastError       `json:"last_error"`
	FailedAt            int              `json:"failed_at"`
}

// PlannedResult 计划结果
type PlannedResult struct {
	Type      string         `json:"type"`       // 类型
	ToolCalls []RunToolCalls `json:"tool_calls"` // function_call事件数组
}

// Usage 使用情况
type Usage struct {
	Core UsageCore `json:"core"` // 核心使用情况
}

// UsageCore 核心使用情况
type UsageCore struct {
	PromptTokens          int `json:"prompt_tokens"`           // 输入token数
	CompletionTokens      int `json:"completion_tokens"`       // 输出token数
	TotalTokens           int `json:"total_tokens"`            // 总token数
	ModelPromptTokens     int `json:"model_prompt_tokens"`     // 模型输入token数
	ModelCompletionTokens int `json:"model_completion_tokens"` // 模型输出token数
	ModelTotalTokens      int `json:"model_total_tokens"`      // 模型总token数
}

// PlanningEventType planning事件类型
type PlanningEventType string

const (
	// PlanningEventPing _
	PlanningEventPing PlanningEventType = "ping"
	// PlanningEventDone _
	PlanningEventDone PlanningEventType = "done"
	// PlanningEventError _
	PlanningEventError PlanningEventType = "error"
	// PlanningEventThoughts  输出思考流式消息，多条。
	PlanningEventThoughts PlanningEventType = "thoughts"
	// PlanningEventStatus 输出执行状态和最后计划结果，多条。
	PlanningEventStatus PlanningEventType = "status"
	// PlanningEventContentFlag 输出内容特征消息（输入安全）,一般只有一条。
	PlanningEventContentFlag PlanningEventType = "content_flag"
	// PlanningEventDone 结束消息，应该最多有一条，data body可以包含logid，status, cost_time（包含server内部总时延)、usage(包含总体的计费信息) 等关键信息
)

// PlanningEvent planning事件
type PlanningEvent struct {
	Type             PlanningEventType
	Error            error
	PingEvent        *PlanningPingEvent
	DoneEvent        *PlanningDoneEvent
	ErrorEvent       *PlanningErrorEvent
	ThoughtEvent     *PlanningThoughtEvent
	StatusEvent      *PlanningStatusEvent
	ContentFlagEvent *PlanningContentFlagEvent
}

// LoadFromEvent 从事件中加载
func (pe *PlanningEvent) LoadFromEvent(ev *ghttp.Event) error {
	pe.Type = PlanningEventType(ev.Event)
	switch ev.Event {
	case string(PlanningEventPing):
		err := json.Unmarshal(ev.Data, &pe.PingEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningPingEvent error: %w", err)
		}
	case string(PlanningEventError):
		err := json.Unmarshal(ev.Data, &pe.ErrorEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningErrorEvent error: %w", err)
		}
	case string(PlanningEventDone):
		err := json.Unmarshal(ev.Data, &pe.DoneEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningDoneEvent error: %w", err)
		}
	case string(PlanningEventThoughts):
		err := json.Unmarshal(ev.Data, &pe.ThoughtEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningThoughtEvent error: %w", err)
		}
	case string(PlanningEventStatus):
		err := json.Unmarshal(ev.Data, &pe.StatusEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningStatusEvent error: %w", err)
		}
	case string(PlanningEventContentFlag):
		err := json.Unmarshal(ev.Data, &pe.ContentFlagEvent)
		if err != nil {
			return fmt.Errorf("unmarshal PlanningContentFlagEvent error: %w", err)
		}
	default:
		return fmt.Errorf("unknown event type: %s, data: %s", ev.Event, ev.Data)
	}
	return nil
}

// PlanningStatusEventType 状态事件类型
type PlanningStatusEventType string

const (
	// PlanningStatusEventTypePlanningBegin 开始事件
	PlanningStatusEventTypePlanningBegin PlanningStatusEventType = "planning_begin"
	// PlanningStatusEventTypePlanningEnd 结束事件
	PlanningStatusEventTypePlanningEnd PlanningStatusEventType = "planning_end"
)

// PlanningStatusEvent event:status 返回的数据包
type PlanningStatusEvent struct {
	Status    string                  `json:"status"`            // 当前处于的状态
	SendID    int                     `json:"send_id"`           // 当前的消息ID，用来标记每条消息的编号
	Message   string                  `json:"message"`           // 当前的状态消息内容，可以呈现给用户，让用户感知当前正在执行的动作，由调度系统产生，而非工具自身产生。
	EventType PlanningStatusEventType `json:"event_type"`        // 当前通知的消息类型，可能为空。event_type为空时，detail为空对象。 detail对象的详情与event_type相关。
	Details   *PlanningStatusObject   `json:"details,omitempty"` // details中的字段直接返回给调用方，后续可扩展，参考各种event_type对应的details定义。
}

// PlanningPingEvent ping事件
type PlanningPingEvent struct {
	LogID        string `json:"logId"`
	Status       string `json:"status"`
	CostTimeInMs int    `json:"cost_time_in_ms"`
	Timestamp    int    `json:"timestamp"`
}

// PlanningErrorEvent 错误事件
type PlanningErrorEvent struct {
	LogID        string `json:"logId"`
	Status       string `json:"status"`
	CostTimeInMs int    `json:"cost_time_in_ms"`
	Timestamp    int    `json:"timestamp"`
}

// PlanningDoneEvent 完成事件
type PlanningDoneEvent struct {
	LogID        string `json:"logId"`
	Status       string `json:"status"`
	CostTimeInMs int    `json:"cost_time_in_ms"`
	Timestamp    int    `json:"timestamp"`
}

// PlanningThoughtEvent 思考事件
// 需要白名单并打开meta参数
//
//	{
//		"metadata" : {
//			"option.output.thoughts.stream.mode": true,  // thoughts信息是否以流式输出
//			"option.output.thoughts.data": true                 // thoughts信息是否输出
//		}
//	}
type PlanningThoughtEvent struct {
	StepID        string                `json:"step_id"`              // run_step的id
	SendID        int                   `json:"send_id"`              // 全局id
	ThoughtsIndex int                   `json:"thoughts_index"`       // 局部id
	IsWithDraw    int                   `json:"is_with_draw"`         // 当is_end时有值，1- 需要撤回 0 - 不需要撤回
	TokensAll     string                `json:"tokens_all,omitempty"` // 非必填，为当前所有thought的加和，如果is_with_draw为1，则tokens_all为撤回后，需要展示的新文案
	IsEnd         int                   `json:"is_end"`               // 是否结束 0- 不是结束 1- 是结束
	Content       string                `json:"content"`              // 具体内容
	Detail        *PlanningStatusObject `json:"detail,omitempty"`     // 思考的详细过程
}

// PlanningStatusObject 对象 填充到Details字段中
type PlanningStatusObject struct {
	Type           string          `json:"type"` // "planning_object" 枚举值 指向需要读取的字段，无需关注
	PlanningObject *PlanningObject `json:"planning_object"`
}

// PlanningContentFlagEvent 内容特征事件
// 需要打开meta配置
//
//	{
//		"metadata" : {
//			"option.output.content_flag.message":"true"
//		}
//	}
type PlanningContentFlagEvent struct {
	Status      string      `json:"status"` // "in_progress"
	SendID      int         `json:"send_id"`
	StepID      string      `json:"step_id"` // "step-1"
	Type        string      `json:"type"`    // "content_flag"
	ContentFlag ContentFlag `json:"content_flag"`
}
