package types

//	assistant 服务授权 token
//
// 在使用 assistant 包提供的方法之前,需要先设置这个
var AssistantAuthorization string

const (
	// FormatText text response format
	FormatText ResponseFormat = "text"
	// FormatJSON json response format
	FormatJSON ResponseFormat = "json"
)

const (
	// PluginTool plugin tool type
	PluginTool ToolType = "plugin"
	// FunctionTool function tool type
	FunctionTool ToolType = "function"
)

const (
	MessageContentTypeText      MessageContentType = "text"
	MessageContentTypeImageFile MessageContentType = "image_file"
	MessageContentTypeFile      MessageContentType = "file"
)

// assistant thread历史对话role的角色列表
const (
	RoleUser      Role = "user"
	RoleAssistant Role = "assistant"
	RoleFunction  Role = "function"
)

// threads：
// {endpoint}/v2/threads
// {endpoint}/v2/threads/query
// {endpoint}/v2/threads/delete

var (
	// ThreadsPath 是线程的路径
	ThreadsPath = "/v2/threads"
	// ThreadsQueryPath 是查询线程的路径
	ThreadsQueryPath = "/v2/threads/query"
	// ThreadsDeletePath 是删除线程的路径
	ThreadsDeletePath = "/v2/threads/delete"
	// ThreadsUpdatePath 是更新线程的路径
	ThreadsUpdatePath = "/v2/threads/update"
)

// v1 threads
// post {endpoint}/v1/threads
// get  {endpoint}/v1/threads/{thread_id}
// post {endpoint}/v1/threads/{thread_id}
// del  {endpoint}/v1/threads/{thread_id}

// runs：
// {endpoint}/v2/threads/runs
// {endpoint}/v2/threads/runs/submit_tool_outputs
// {endpoint}/v2/threads/runs/cancel
// {endpoint}/v2/threads/runs/list
// {endpoint}/v2/threads/runs/query
// {endpoint}/v2/threads/runs/steps/list
// {endpoint}/v2/threads/runs/steps/query

var (
	// ThreadsRunsPath 是线程运行的路径
	ThreadsRunsPath = "/v2/threads/runs"
	// ThreadsRunsSubmitToolOutputsPath 是提交工具输出的路径
	ThreadsRunsSubmitToolOutputsPath = "/v2/threads/runs/submit_tool_outputs"
	// ThreadsRunsCancelPath 是取消运行的路径
	ThreadsRunsCancelPath = "/v2/threads/runs/cancel"
	// ThreadsRunsListPath 是列出运行的路径
	ThreadsRunsListPath = "/v2/threads/runs/list"
	// ThreadsRunsQueryPath 是查询运行的路径
	ThreadsRunsQueryPath = "/v2/threads/runs/query"
	// ThreadsRunsStepsListPath 是列出运行步骤的路径
	ThreadsRunsStepsListPath = "/v2/threads/runs/steps/list"
	// ThreadsRunsStepsQueryPath 是查询运行步骤的路径
	ThreadsRunsStepsQueryPath = "/v2/threads/runs/steps/query"
)

// v1 runs：
// get  {endpoint}/v1/threads/{thread_id}/runs
// post {endpoint}/v1/threads/{thread_id}/run/
// get  {endpoint}/v1/threads/{thread_id}/runs/{run_id}
// post {endpoint}/v1/threads/{thread_id}/runs/{run_id}
// post {endpoint}/v1/threads/{thread_id}/runs/{run_id}/submit_tool_outputs
// post {endpoint}/v1/threads/{thread_id}/runs/{run_id}/cancel
// post {endpoint}/v1/threads/runs
// get 	{endpoint}/v1/threads/{thread_id}/runs/{run_id}/steps
// get  {endpoint}/v1/threads/{thread_id}/runs/{run_id}/steps/{step_id}

// messages：
// {endpoint}/v2/threads/messages
// {endpoint}/v2/threads/messages/list
// {endpoint}/v2/threads/messages/query
// {endpoint}/v2/threads/messages/update
// {endpoint}/v2/threads/messages/files/list

var (
	// ThreadsMessagesPath 是线程消息的路径
	ThreadsMessagesPath = "/v2/threads/messages"
	// ThreadsMessagesListPath 是列出消息的路径
	ThreadsMessagesListPath = "/v2/threads/messages/list"
	// ThreadsMessagesQueryPath 是查询消息的路径
	ThreadsMessagesQueryPath = "/v2/threads/messages/query"
	// ThreadsMessagesUpdatePath 是更新消息的路径
	ThreadsMessagesUpdatePath = "/v2/threads/messages/update"
	// ThreadsMessagesFilesListPath 是列出消息文件的路径
	ThreadsMessagesFilesListPath = "/v2/threads/messages/files/list"
	// ThreadsMessageDeleteLastPath 是删除最后一轮消息的路径
	ThreadsMessageDeleteLastPath = "/v2/threads/runs/last_run/delete"
)

// v1 messages：
// get  {endpoint}/v1/threads/{thread_id}/messages
// post {endpoint}/v1/threads/{thread_id}/messages
// get  {endpoint}/v1/threads/{thread_id}/messages/{message_id}
// post {endpoint}/v1/threads/{thread_id}/messages/{message_id}
// get  {endpoint}/v1/threads/{thread_id}/messages/{message_id}/files
var ()

// assistants：
// {endpoint}/v2/assistants
// {endpoint}/v2/assistants/query
// {endpoint}/v2/assistants/list
// {endpoint}/v2/assistants/delete
// {endpoint}/v2/assistants/files/list
// {endpoint}/v2/assistants/files
// {endpoint}/v2/assistants/files/delete

var (
	// AssistantsPath 是助手的路径
	AssistantsPath = "/v2/assistants"
	// AssistantsUpdatePath 是更新助手的路径
	AssistantsUpdatePath = "/v2/assistants/update"
	// AssistantsQueryPath 是查询助手的路径
	AssistantsQueryPath = "/v2/assistants/query"
	// AssistantsListPath 是列出助手的路径
	AssistantsListPath = "/v2/assistants/list"
	// AssistantsDeletePath 是删除助手的路径
	AssistantsDeletePath = "/v2/assistants/delete"
	// AssistantsFilesListPath 是列出助手文件的路径
	AssistantsFilesListPath = "/v2/assistants/files/list"
	// AssistantsFilesPath 是助手文件的路径
	AssistantsFilesPath = "/v2/assistants/files"
	// AssistantsFilesDeletePath 是删除助手文件的路径
	AssistantsFilesDeletePath = "/v2/assistants/files/delete"
)

// v1 assistants：
// get  {endpoint}/v1/assistants
// post {endpoint}/v1/assistants
// get  {endpoint}/v1/assistants/{assistant_id}
// post {endpoint}/v1/assistants/{assistant_id}
// del  {endpoint}/v1/assistants/{assistant_id}
// get  {endpoint}/v1/assistants/{assistant_id}/files
// post {endpoint}/v1/assistants/{assistant_id}/files
// del  {endpoint}/v1/assistants/{assistant_id}/files/{file_id}

// files
// {endpoint}/v2/storage/files
// {endpoint}/v2/storage/files/list
// {endpoint}/v2/storage/files/query
// {endpoint}/v2/storage/files/delete
// {endpoint}/v2/storage/files/download
// {endpoint}/v2/storage/files/content

var (
	// StorageFilesPath 是存储文件的路径
	StorageFilesPath = "/v2/storage/files"
	// StorageFilesListPath 是列出存储文件的路径
	StorageFilesListPath = "/v2/storage/files/list"
	// StorageFilesQueryPath 是查询存储文件的路径
	StorageFilesQueryPath = "/v2/storage/files/query"
	// StorageFilesDeletePath 是删除存储文件的路径
	StorageFilesDeletePath = "/v2/storage/files/delete"
	// StorageFilesDownloadPath 是下载存储文件的路径
	StorageFilesDownloadPath = "/v2/storage/files/download"
	// StorageFilesContentPath 是存储文件内容的路径
	StorageFilesContentPath = "/v2/storage/files/content"
	// StorageFilesURLPath 是存储文件的URL
	StorageFilesURLPath = "/v2/storage/files/url"
)

// v1 files
// get  {endpoint}/v1/files
// post {endpoint}/v1/files
// get  {endpoint}/v1/files/{file_id}
// del  {endpoint}/v1/files/{file_id}
// get  {endpoint}/v1/files/{file_id}/download
// get  {endpoint}/v1/files/{file_id}/content

var (
	// AssistantsPlanningFunctionCallPath 规划函数调用的路径
	AssistantsPlanningFunctionCallPath = "/v2/assistants/planning/function_call"
)

var (
	RegisterFilePath       = "/v2/storage/files/register"
	RegisterSearchFilePath = "/v2/storage/files/get"
)
