package types

import (
	"fmt"
	"net/url"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
)

// Role 请求者的角色
// string
type Role string

// MessageContentType 消息内容类型
type MessageContentType string

// Message 消息信息
type Message struct {
	AssistantID string           `json:"assistant_id,omitempty"`
	Content     []MessageContent `json:"content,omitempty"`
	ContentType string           `json:"content_type,omitempty"`
	CreatedAt   float64          `json:"created_at,omitempty"`
	FileIDs     []string         `json:"file_ids,omitempty"`
	ID          string           `json:"id,omitempty"`
	Metadata    map[string]any   `json:"metadata,omitempty"`
	Name        string           `json:"name,omitempty"`
	Object      string           `json:"object,omitempty"`
	Role        Role             `json:"role,omitempty"`
	RunDetail   float64          `json:"run_detail,omitempty"`
	RunID       string           `json:"run_id,omitempty"`
	ThreadID    string           `json:"thread_id,omitempty"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	Role         Role            `json:"role"`
	Name         string          `json:"name,omitempty"` // 当role=function的时候设置工具名称
	Content      string          `json:"content"`
	FileIDs      []string        `json:"file_ids,omitempty"`
	ToolCallID   string          `json:"tool_call_id,omitempty"`  // 当role=assistant 召回function的时候填写callID，和FunctionCall一起使用 工具调用id
	FunctionCall []*FunctionCall `json:"function_call,omitempty"` // 当role=assistant 召回function的时候填写工具调用信息
}

// FunctionCall 函数调用历史记录结果
type FunctionCall struct {
	// Name 函数名称
	// Name  函数名称
	Name string `json:"name,omitempty"`
	// Arguments  函数参数
	Arguments string `json:"arguments,omitempty"`
	// Output  函数输出
	Output string `json:"output,omitempty"`
	// Prompt  函数执行完后给到模型的提示词
	// WARN: 此字段目前未提供
	Prompt string `json:"prompt,omitempty"`
	// Thoughts  模型思考
	Thoughts string `json:"thoughts,omitempty"`
}

// Text 文本消息
type Text struct {
	// Value message 消息内容
	Value string `json:"value,omitempty"`
	// Annotations 引文列表
	Annotations []Annotations `json:"annotations,omitempty"`
}

// Annotations 引文
type Annotations struct {
	// Text 引用的文字内容
	Text string `json:"text,omitempty"`
	// StartIndex 引用的文字在message中的起始位置
	StartIndex int `json:"start_index,omitempty"`
	// EndIndex 引用的文字在message中的结束位置
	EndIndex int `json:"end_index,omitempty"`
	// FileInfo 引用的文件信息
	FileInfo *FileInfo `json:"file_info,omitempty"`
}

// FileInfo 文件信息
type FileInfo struct {
	// URL 文件的URL
	URL string `json:"url,omitempty"`
	// URLType 文件的URL类型 content/download
	URLType string `json:"url_type,omitempty"`
	// FileName 文件名
	FileName string `json:"file_name,omitempty"`
	// FileID 文件
	FileID string `json:"file_id,omitempty"`
}

// MessageContent 消息内容
type MessageContent struct {
	Type MessageContentType `json:"type,omitempty"`
	Text *Text              `json:"text,omitempty"`
	// ImageFile 当 type 为 image_file 时有效
	ImageFile struct {
		FileID string `json:"file_id,omitempty"`
	} `json:"image_file,omitempty"`
	// File 当 type 为 file 时有效
	File struct {
		FileID string `json:"file_id,omitempty"`
	} `json:"file,omitempty"`
}

// FileMountOnMessage assistant绑定的文件信息
type FileMountOnMessage struct {
	MessageID string  `json:"message_id,omitempty"`
	CreatedAt float64 `json:"created_at,omitempty"`
	ID        string  `json:"id,omitempty"`
	Object    string  `json:"object,omitempty"`
}

type CreateMessageOptions struct {
	// FileIDs 文件ID列表
	FileIDs []string `json:"file_ids,omitempty"`
	// MetaData 元数据
	MetaData map[string]any `json:"metadata,omitempty"`
	// ReqOpts 请求选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (cmo *CreateMessageOptions) Apply(body map[string]any) {
	if cmo.FileIDs != nil {
		body["file_ids"] = cmo.FileIDs
	}

	if cmo.MetaData != nil {
		body["metadata"] = cmo.MetaData
	}
}

type GetMessagesOptions struct {
	// ThreadID 线程ID
	ThreadID string `json:"thread_id,omitempty"`
	// Limit 一次获取的文件数量
	Limit int `json:"limit,omitempty"`
	// Order 排序方式
	Order string `json:"order,omitempty"`
	// Before 在某个消息之前
	Before string `json:"before,omitempty"`
	// After 在某个消息之后
	After string `json:"after,omitempty"`

	// ReqOpts 请求选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (gmo *GetMessagesOptions) Query() url.Values {
	v := url.Values{}
	if gmo.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", gmo.Limit))
	}
	if gmo.Order != "" {
		v.Set("order", gmo.Order)
	}
	if gmo.Before != "" {
		v.Set("before", gmo.Before)
	}
	if gmo.After != "" {
		v.Set("after", gmo.After)
	}
	return v
}

type GetFilesInMessageOptions struct {
	// ThreadID 线程ID
	ThreadID string `json:"thread_id,omitempty"`
	// MessageID 消息ID
	MessageID string `json:"message_id,omitempty"`
	// Limit 限制
	Limit int `json:"limit,omitempty"`
	// Order 排序方式
	Order string `json:"order,omitempty"`
	// Before 在某个消息之前
	Before string `json:"before,omitempty"`
	// After 在某个消息之后
	After string `json:"after,omitempty"`

	ReqOpts []func(*r.Options) `json:"-"`
}

func (gfimo *GetFilesInMessageOptions) Query() url.Values {
	v := url.Values{}
	if gfimo.Limit > 0 {
		v.Set("limit", fmt.Sprintf("%d", gfimo.Limit))
	}
	if gfimo.Order != "" {
		v.Set("order", gfimo.Order)
	}
	if gfimo.Before != "" {
		v.Set("before", gfimo.Before)
	}
	if gfimo.After != "" {
		v.Set("after", gfimo.After)
	}
	return v
}

type UpdateMessageOptions struct {
	// FileIDs 文件ID列表
	FileIDs []string `json:"file_ids,omitempty"`
	// MetaData 元数据
	MetaData map[string]any `json:"metadata,omitempty"`
	// ReqOpts 请求选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (umo *UpdateMessageOptions) Apply(body map[string]any) {
	if umo.FileIDs != nil {
		body["file_ids"] = umo.FileIDs
	}

	if umo.MetaData != nil {
		body["metadata"] = umo.MetaData
	}
}

// DeleteLastMessageOptions 删除最后一条消息的选项
type DeleteLastMessageOptions struct {
	// ReserveRoleOfUser 是否保留被删除run的消息列表中的的role:user的消息
	ReserveRoleOfUser bool `json:"reserve_role_of_user,omitempty"`
	// MetaData 元数据
	MetaData map[string]any `json:"metadata,omitempty"`
	// ReqOpts 请求选项
	ReqOpts []func(*r.Options) `json:"-"`
}

// Apply 应用选项
func (dmo *DeleteLastMessageOptions) Apply(body map[string]any) {
	if dmo.ReserveRoleOfUser {
		body["reserve_role_of_user"] = dmo.ReserveRoleOfUser
	}

	if dmo.MetaData != nil {
		body["metadata"] = dmo.MetaData
	}
}
