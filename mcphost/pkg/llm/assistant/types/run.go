package types

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/tidwall/gjson"
	"icode.baidu.com/baidu/gdp/ghttp"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
)

// RunStatus 表示Run运行状态的字符串类型。
type RunStatus string

// CallbackCancelRunStart 取消 Run 开始回调
type CallbackCancelRunStart func(ctx context.Context, threadID, runID string)

// CallbackCancelRunEnd 取消 Run 结束回调
type CallbackCancelRunEnd func(ctx context.Context, run *Run)

const (
	// RunQueued 队列中
	RunQueued RunStatus = "queued"

	// RunInProgress 运行中
	RunInProgress RunStatus = "in_progress"

	// RunRequiresAction 需要操作
	RunRequiresAction RunStatus = "requires_action"

	// RunCancelling 取消中
	RunCancelling RunStatus = "cancelling"

	// RunCancelled 取消
	RunCancelled RunStatus = "cancelled"

	// RunFailed 失败
	RunFailed RunStatus = "failed"

	// RunCompleted 完成
	RunCompleted RunStatus = "completed"

	// RunExpired 过期
	RunExpired RunStatus = "expired"
)

// RunFinishedStatus Assistant Thread/Run 结束/异常状态
// 作用: 判断对应的 Run 是否结束
// 需要注意的是，对于调用方，cancelling也可以看作结束
var RunFinishedStatus = []string{
	"completed",
	"cancelled",
	"failed",
	"expired",
}

// ChatInfo chatinfo
type ChatInfo struct {
	Thoughts string `json:"thoughts,omitempty"`
	Hint     string `json:"hint,omitempty"`
	Text     string `json:"text,omitempty"`
}

// Run 运行
type Run struct {
	// AssistantID  助手 ID
	AssistantID string `json:"assistant_id,omitempty"`

	// CancelledAt  取消时间
	CancelledAt float64 `json:"cancelled_at,omitempty"`

	// CompletedAt  完成时间
	CompletedAt float64 `json:"completed_at,omitempty"`

	// CreatedAt  创建时间
	CreatedAt float64 `json:"created_at,omitempty"`

	// CurrentTool  当前工具
	ExpiredAt float64 `json:"expired_at,omitempty"`

	// ExpiresAt  过期时间
	ExpiresAt float64 `json:"expires_at,omitempty"`

	// FailedAt  失败时间
	FailedAt float64 `json:"failed_at,omitempty"`

	// FileIDs  文件 ID
	FileIDs []string `json:"file_ids,omitempty"`

	// ID  ID
	ID string `json:"id,omitempty"`

	// Instructions  指令
	Instructions string `json:"instructions,omitempty"`

	// LastError  最后错误
	LastError LastError `json:"last_error,omitempty"`

	// Metadata  元数据
	// - finish_reason 结束原因
	// - content_flag 当前消息的风控标记信息
	// 目前一言在此处返回的是字符串,因此需要自行解析
	// "{\"flag\":1,\"target\":0,\"round\":0}"
	// 也可以使用 Run 提供的 MetadataContentFlag 和 MetadataFinishReason 方法自行获取
	Metadata map[string]any `json:"metadata,omitempty"`

	// Model  模型
	Model string `json:"model,omitempty"`

	// Object  thread.run
	Object string `json:"object,omitempty"`

	// StartedAt  开始时间
	StartedAt float64 `json:"started_at,omitempty"`

	// Status  状态 queued,in_progress,requires_action,expired,cancelling,cancelled,failed,completed
	//
	// queued：当Run对象首次创建就会变成queued等待运行，正常情况下，很快就会变成in_progress状态。
	//
	// in_progress：run正在执行中，这时候可以调用run_step接口来查看具体的执行状态。
	//
	// requires_action：如果Assistant需要执行函数调用(function call)，就会转到这个状态，然后你必须按给定的参数调用指定的方法，并提交输出之后，run才可以继续运行，进入in_progress状态。
	//
	// expired：当没有在expires_at之前提交函数调用输出，run将会过期。另外，如果在expires_at之前没获取输出，run也会变成expired状态。
	//
	// cancelling：当调用cancel接口后，run就会变成cancelling，取消成功后就会变成cancelled状态
	//
	// cancelled：Run已成功取消。
	//
	// failed：运行失败，可以通过查看Run对象中的last_error对象来查看失败的原因。参考 LastError对象。
	//
	// completed：执行完成，可以获取Assistant返回的消息了，也可以继续向Assistant提问了。
	Status string `json:"status,omitempty"`

	// ThreadID  Thread ID
	ThreadID string `json:"thread_id,omitempty"`

	Tools []struct {
		Type string `json:"type,omitempty"`
	} `json:"tools,omitempty"`
	Submitted bool `json:"-"`

	// 模型请求参数
	ModelParameters ModelParameters `json:"model_parameters,omitempty"`

	// Usage Token使用情况统计
	Usage *RunUsage `json:"usage,omitempty"`
}

// RunUsage Token使用情况统计
type RunUsage struct {
	// Core 核心模型的token使用情况
	Core *RunUsageStats `json:"core,omitempty"`

	// Tools 工具的token使用情况
	Tools map[string]any `json:"tools,omitempty"`
}

// RunUsageStats Token使用统计信息
type RunUsageStats struct {
	// PromptTokens 提示token数量
	PromptTokens int `json:"prompt_tokens,omitempty"`

	// PromptTokensDetails 提示token详情
	PromptTokensDetails map[string]any `json:"prompt_tokens_details,omitempty"`

	// TruncatedPromptTokens 截断的提示token数量
	TruncatedPromptTokens int `json:"truncated_prompt_tokens,omitempty"`

	// CompletionTokens 完成token数量
	CompletionTokens int `json:"completion_tokens,omitempty"`

	// CompletionTokensDetails 完成token详情
	CompletionTokensDetails map[string]any `json:"completion_tokens_details,omitempty"`

	// TotalTokens 总token数量
	TotalTokens int `json:"total_tokens,omitempty"`

	// ModelPromptTokens 模型提示token数量
	ModelPromptTokens int `json:"model_prompt_tokens,omitempty"`

	// ModelCompletionTokens 模型完成token数量
	ModelCompletionTokens int `json:"model_completion_tokens,omitempty"`

	// ModelTotalTokens 模型总token数量
	ModelTotalTokens int `json:"model_total_tokens,omitempty"`
}

// MetadataFinishReason 返回 metadata 中的结束原因
// 如果 metadata 为空或结束原因不存在，则返回空字符串。
// 返回值:
//   - string: 结束原因, 如果不存在则为 ""。
func (r *Run) MetadataFinishReason() string {
	if r == nil || r.Metadata == nil {
		return ""
	}

	if vs, ok := r.Metadata["finish_reason"].(string); ok {
		return vs
	}
	return ""
}

// MetadataContentFlag 返回包含 RunMessageEvent 元数据中 "content_flag" 的 ContentFlag 实例。如果元数据为空或 "content_flag" 不是字符串，则返回 nil。
//
// 返回值:
//   - cf: 如果存在并成功解析 "content_flag"，返回 *ContentFlag 实例；否则返回 nil。
func (r *Run) MetadataContentFlag() (cf *ContentFlag) {
	if r == nil || r.Metadata == nil {
		return nil
	}

	if vs, ok := r.Metadata["content_flag"].(string); ok {
		cf = &ContentFlag{}
		_ = json.Unmarshal([]byte(vs), cf)
		return cf
	}
	return nil
}

// RunStep 描述一个运行步骤
type RunStep struct {
	AssistantID string          `json:"assistant_id,omitempty"`
	CancelledAt float64         `json:"cancelled_at,omitempty"`
	CompletedAt float64         `json:"completed_at,omitempty"`
	CreateAt    float64         `json:"create_at,omitempty"`
	ExpiredAt   float64         `json:"expired_at,omitempty"`
	FailedAt    float64         `json:"failed_at,omitempty"`
	ID          string          `json:"id,omitempty"`
	LastError   any             `json:"last_error,omitempty"`
	Object      string          `json:"object,omitempty"`
	RunID       string          `json:"run_id,omitempty"`
	Status      string          `json:"status,omitempty"`
	StepDetails *RunStepDetails `json:"step_details,omitempty"`
	ThreadID    string          `json:"thread_id,omitempty"`
	Type        string          `json:"type,omitempty"`

	// Metadata  元数据
	// - finish_reason 结束原因
	// - content_flag 当前消息的风控标记信息
	// 目前一言在此处返回的是字符串,因此需要自行解析
	// "{\"flag\":1,\"target\":0,\"round\":0}"
	// 也可以使用 RUnStep 提供的 MetadataContentFlag 和 MetadataFinishReason 方法自行获取
	Metadata map[string]any `json:"metadata,omitempty"`
}

// MetadataFinishReason 返回 metadata 中的结束原因
// 如果 metadata 为空或结束原因不存在，则返回空字符串。
// 返回值:
//   - string: 结束原因, 如果不存在则为 ""。
func (rs *RunStep) MetadataFinishReason() string {
	if rs == nil || rs.Metadata == nil {
		return ""
	}

	if vs, ok := rs.Metadata["finish_reason"].(string); ok {
		return vs
	}
	return ""
}

// MetadataContentFlag 返回包含 RunMessageEvent 元数据中 "content_flag" 的 ContentFlag 实例。如果元数据为空或 "content_flag" 不是字符串，则返回 nil。
//
// 返回值:
//   - cf: 如果存在并成功解析 "content_flag"，返回 *ContentFlag 实例；否则返回 nil。
func (rs *RunStep) MetadataContentFlag() (cf *ContentFlag) {
	if rs == nil || rs.Metadata == nil {
		return nil
	}

	if vs, ok := rs.Metadata["content_flag"].(string); ok {
		cf = &ContentFlag{}
		_ = json.Unmarshal([]byte(vs), cf)
		return cf
	}
	return nil
}

// RunStepDetails run step object中的step_details字段
type RunStepDetails struct {
	Type      string         `json:"type,omitempty"`
	ToolCalls []RunToolCalls `json:"tool_calls,omitempty"`
}

// RunStatusEventType Run 接口 Status Event 事件类型
type RunStatusEventType string

const (
	// RunStatusEventTypeRunBegin  run开始运行
	RunStatusEventTypeRunBegin RunStatusEventType = "run_begin"

	// RunStatusEventTypeRunCancelling   run被取消运行，尚未终止
	RunStatusEventTypeRunCancelling RunStatusEventType = "run_cancelling"

	// RunStatusEventTypeRunEnd  run结束运行
	RunStatusEventTypeRunEnd RunStatusEventType = "run_end"

	// RunStatusEventTypeToolCalls  run运行中，触发了function_call的requires_action动作，status:requires_action。
	RunStatusEventTypeToolCalls RunStatusEventType = "tool_calls"

	// RunStatusEventTypeToolCallInfo     run运行过程中，触发了工具调用的信息
	// details.tool_info.name 是工具的英文名
	// details.tool_info.arguments 是工具的请求，json dump的string
	// details.tool_info.output 是这个工具这次调用的返回，json dump的string
	RunStatusEventTypeToolCallInfo RunStatusEventType = "tool_call_info"

	// RunStatusEventTypeTollStepBegin   单独的一个工具步骤开始运行
	RunStatusEventTypeTollStepBegin RunStatusEventType = "tool_step_begin"

	// RunStatusEventTypeTollStepEnd     单独的一个工具步骤结束运行
	RunStatusEventTypeTollStepEnd RunStatusEventType = "tool_step_end"

	// RunStatusEventTypeChatInfo       run运行过程中，输出chat工具的调用信息【2024.3.16新增】
	RunStatusEventTypeChatInfo RunStatusEventType = "chat_info"

	// RunStatusEventTypeMessageCreation run运行过程中，大模型产出消息
	RunStatusEventTypeMessageCreation RunStatusEventType = "message_creation"
)

// RunStatusEventObjectType Run 接口 Status Event Object 类型
type RunStatusEventObjectType string

const (
	// RunStatusEventObjectTypeMessageCreation  run运行过程中，产生了大模型的最终消息
	RunStatusEventObjectTypeMessageCreation RunStatusEventObjectType = "message_creation"

	// RunStatusEventObjectTypeRun run开始运行或者结束运行时，将run_object完整对象通知调用方。
	RunStatusEventObjectTypeRun RunStatusEventObjectType = "run_object"

	// RunStatusEventObjectTypeRunStep  单独的一个工具步骤开始运行或者结束运行时，将run_step_object完整对象通知调用方。
	RunStatusEventObjectTypeRunStep RunStatusEventObjectType = "run_step_object"

	// RunStatusEventObjectTypeActionInfo  run运行过程中，输出action_info的调用信息
	RunStatusEventObjectTypeActionInfo RunStatusEventObjectType = "action_info"

	// RunStatusEventObjectTypeToolCalls  run运行中，触发了function_call的requires_action动作时，将tool_calls的信息（含tool_calls_id，arguments等）通知调用方
	RunStatusEventObjectTypeToolCalls RunStatusEventObjectType = "tool_calls"

	// RunStatusEventObjectTypeToolInfo  run运行过程中，触发了工具调用时，将tool_info的信息（含type，name，arguments等）通知调用方
	RunStatusEventObjectTypeToolInfo RunStatusEventObjectType = "tool_info"

	// RunStatusEventObjectTypeChatInfo run运行过程中，触发了Chat工具时的相关信息，将工具调用的相关信息通知调用方(含thoughts, hint, text等）。【内部测试用，线上用户不用关注】【2024.3.16新增】
	RunStatusEventObjectTypeChatInfo RunStatusEventObjectType = "chat_info"
)

// RunEventTypes Run 接口 Event 事件类型
type RunEventTypes string

const (
	// RunEventStatus      status event
	RunEventStatus RunEventTypes = "status"

	// RunEventMessage     message event
	RunEventMessage RunEventTypes = "message"

	// RunEventToolMessage tool message event
	RunEventToolMessage RunEventTypes = "tool_message"

	// RunEventThoughts    thoughts event
	// NOTE: 目前无用, 一言未开放 thought event
	RunEventThoughts RunEventTypes = "thoughts"

	// RunEventContentFlag content flag event
	RunEventContentFlag RunEventTypes = "content_flag"
)

// RunStatusEvent sse 模式下 Run 接口返回的 event 为 status 的事件
// 目前 RunStatusEvent 只有 tool_call_info 一种类型我们需要关注
// 其他的都无所谓
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/ac4e0aa2793c44
type RunStatusEvent struct {
	// Status 当前处于的状态
	// - completed
	// - in_progress
	// 当 Status 为 completed 时, Run (run_end),RunStep(tool_step_end) 中 Metadata 包含的风控信息才有意义
	Status string `json:"status,omitempty"`

	// SendID  发送 ID 依次递增
	SendID int `json:"send_id,omitempty"`

	// Message  消息, 描述这个 Run 在干啥
	Message string `json:"message,omitempty"`

	// EventType 事件类型,次字段为空的时候应该忽略此结构数据
	// 此字段用于标识具体阶段
	EventType RunStatusEventType `json:"event_type,omitempty"`

	// Type 事件数据类型,通过此字段决定获取哪个字段的数据
	Type RunStatusEventObjectType `json:"type,omitempty"`

	// Run 当 EventType 为 run_begin / run_end 开头使此字段包含 Run 信息
	// 此时 Status 字段应该是 queued / completed
	Run *Run `json:"run,omitempty"`

	// RunStep 当 EventType 为 tool_step_begin/tool_step_end 开头使此字段包含 RunStep 信息
	// 此时 Status 字段应该是 in_progress
	RunStep *RunStep `json:"run_step,omitempty"`

	// ToolCallInfo 当 EventType 为 tool_call_info 时包含工具调用事件详情
	// 此时 Status 字段应该是 in_progress-
	ToolCallInfo *RunToolCallInfo `json:"tool_call_info,omitempty"`

	// ToolCalls 当 EventType 为 tool_calls 时 需要调用的 Function 信息
	// 此时 Status 字段应该是 requires_action
	ToolCalls []*RunToolCalls `json:"tool_calls,omitempty"`

	// ActionInfo 当 EventType 为 action_info 时包含工具内部调用信息
	// 此时 Status 字段应该是 in_progress
	ActionInfo map[string]any `json:"action_info,omitempty"`

	// ChatInfo 当 EventType 为 chat_info 时包含 Chat 工具调用信息
	// 此时 Status 字段应该是 in_progress
	ChatInfo *ChatInfo `json:"chat_info,omitempty"`
}

// RunMessageEvent sse 模式下 Run 接口返回的 event 为 message 的事件
type RunMessageEvent struct {
	// Status  状态
	// 目前只有 completed 不支持流式穿出模型编写的内容
	Status string `json:"status,omitempty"`

	// SendID  发送 ID 依次递增
	SendID int `json:"send_id,omitempty"`

	// IsEnd int 是否结束 0/1
	IsEnd int `json:"is_end,omitempty"`

	// Details
	Details *ChatInfo `json:"details,omitempty"`

	// MessageID  消息 ID
	MessageID string `json:"message_id,omitempty"`

	// Content 模型生成的回复
	Content []MessageContent `json:"content,omitempty"`

	// Metadata  元数据
	// - finish_reason 结束原因
	// - content_flag 当前消息的风控标记信息
	// 目前一言在此处返回的是字符串,因此需要自行解析
	// "{\"flag\":1,\"target\":0,\"round\":0}"
	// 也可以使用 RunMessageEvent 提供的 MetadataContentFlag 方法自行获取
	Metadata map[string]any `json:"metadata,omitempty"`
}

// MetadataFinishReason 返回 metadata 中的结束原因
// 如果 metadata 为空或结束原因不存在，则返回空字符串。
// 返回值:
//   - string: 结束原因, 如果不存在则为 ""。
func (r *RunMessageEvent) MetadataFinishReason() string {
	if r == nil || r.Metadata == nil {
		return ""
	}

	if vs, ok := r.Metadata["finish_reason"].(string); ok {
		return vs
	}
	return ""
}

// MetadataContentFlag 返回包含 RunMessageEvent 元数据中 "content_flag" 的 ContentFlag 实例。如果元数据为空或 "content_flag" 不是字符串，则返回 nil。
//
// 返回值:
//   - cf: 如果存在并成功解析 "content_flag"，返回 *ContentFlag 实例；否则返回 nil。
func (r *RunMessageEvent) MetadataContentFlag() (cf *ContentFlag) {
	if r == nil || r.Metadata == nil {
		return nil
	}

	if vs, ok := r.Metadata["content_flag"].(string); ok {
		cf = &ContentFlag{}
		_ = json.Unmarshal([]byte(vs), cf)
		return cf
	}
	return nil
}

// RunToolMessageEvent sse 模式下 Run 接口返回的 event 为 tool_message 的事件
type RunToolMessageEvent struct {
	// Status  状态
	Status string `json:"status,omitempty"`

	// SendID  发送 ID 依次递增
	SendID int `json:"send_id,omitempty"`

	// ToolName  工具名称
	ToolName string `json:"tool_name,omitempty"`

	// MessageIndex  消息索引
	MessageIndex int `json:"message_index,omitempty"`

	// Content
	Content []MessageContent `json:"content,omitempty"`
}

// RunThoughtsEvent sse 模式下 Run 接口返回的 event 为 thoughts 的事件
type RunThoughtsEvent struct {
	//  StepID 步骤 ID
	StepID string `json:"step_id,omitempty"`

	// SendID  发送 ID 依次递增
	SendID int `json:"send_id,omitempty"`

	// ThoughtsIndex  思考索引
	ThoughtsIndex int `json:"thoughts_index,omitempty"`

	// IsEnd int 是否结束 0/1
	IsEnd int `json:"is_end,omitempty"`

	// Content 模型的思考
	Content string `json:"content,omitempty"`
}

// RunContentFlagEvent
type RunContentFlagEvent struct {
	// Status  状态
	Status string `json:"status,omitempty"`

	// SendID  发送 ID 依次递增
	SendID int `json:"send_id,omitempty"`

	//  StepID 步骤 ID
	StepID string `json:"step_id,omitempty"`

	// Type 类型 固定值 content_flag
	Type string `json:"type,omitempty"`

	// ContentFlag  内容标记
	ContentFlag *ContentFlag `json:"content_flag,omitempty"`
}

// ContentFlag 风控标记
type ContentFlag struct {
	// Flag 标记
	// • 0：正常，安全
	// • 1：可以继续对话，没有正常回复（不是EB模型正常且完整输出）。
	// • 2：禁聊：不允许继续对话，但是可以展示内容。
	// • 3：禁⽌上屏：不允许继续对话且不能上屏展示。
	Flag int `json:"flag,omitempty"`

	// Target 目标
	// • 0：content 对话内容 （含义待明确）
	// • 1：instruction (与4的区别待确认)
	// • 2：user (含义待确认)
	// • 3：functions ⼯具声明
	// • 4：instruction 不安全(与1的区别待确认)
	// • 5：chat_instruction 不安全
	// • 6：thoughts_instruction 不安全
	// • 7：agent_storage不安全
	Target int `json:"target,omitempty"`

	// Round
	// 命中⾮安全对话轮次，round = 0 表示当前query，⾮0为历史对话轮次, 当target=0时有效。
	Round int `json:"round,omitempty"`
}

// RunToolCallInfo 工具调用事件详情
type RunToolCallInfo struct {
	ID   string   `json:"id,omitempty"`
	Type ToolType `json:"type,omitempty"`

	// Name  工具名称
	Name string `json:"name,omitempty"`

	// Arguments  工具参数
	Arguments string `json:"arguments,omitempty"`

	// Output  工具输出
	Output string `json:"output,omitempty"`

	// Prompt  工具执行完后给到模型的提示词
	Prompt string `json:"prompt,omitempty"`

	// Thoughts  模型思考
	Thoughts string `json:"thoughts,omitempty"`
}

// RunToolCalls 工具调用事件详情
type RunToolCalls struct {
	// ID 用于提交工具返回的时候需要使用到
	ID string `json:"id,omitempty"`

	// Type
	Type ToolType `json:"type,omitempty"`

	// Function 当 Type 为 function 时 需要调用的 Function 信息
	Function FunctionCall `json:"function,omitempty"`

	// Plugin 当 Type 为 plugin 时 需要调用的 Plugin 信息
	Plugin struct {
		// Name  插件名称
		Name string `json:"name,omitempty"`
		// Thoughts  插件思考
		Thoughts string `json:"thoughts,omitempty"`
		// Data  插件结果
		Output string `json:"output,omitempty"`
	} `json:"plugin,omitempty"`
}

func (ru *RunStatusEvent) UnmarshalJSON(b []byte) error {
	type runEvent RunStatusEvent
	var r runEvent
	// 先把能解析的字段解析出来
	err := json.Unmarshal(b, &r)
	if err != nil {
		return err
	}

	// 再根据 EventType 解析具体的内容
	jb := gjson.ParseBytes(b)

	// 根据 EventType 解析具体的内容
	// 匹配 run_begin,run_cancelling
	if strings.HasPrefix(string(r.EventType), "run_") {
		r.Type = RunStatusEventObjectTypeRun
		// 如果是 run_ 开头的事件,则解析 Run 信息
		var run Run
		err = json.Unmarshal([]byte(jb.Get("details.run_object").String()), &run)
		if err != nil {
			return err
		}

		r.Run = &run
		*ru = RunStatusEvent(r)
	} else if strings.HasPrefix(string(r.EventType), "tool_step_") {
		// 如果是 tool_step 开头的事件,则解析 RunStep 信息
		r.Type = RunStatusEventObjectTypeRunStep
		var runStep RunStep
		err = json.Unmarshal([]byte(jb.Get("details.run_step_object").String()), &runStep)
		if err != nil {
			return err
		}

		r.RunStep = &runStep
		*ru = RunStatusEvent(r)
	} else if strings.HasPrefix(string(r.EventType), "tool_call_") {
		r.Type = RunStatusEventObjectTypeToolInfo
		r.ToolCallInfo = &RunToolCallInfo{
			Type: ToolType(jb.Get("details.tool_info.type").String()),
			Name: jb.Get("details.tool_info.name").String(),
			// 由于 tool_call_info 无法在里面区分到底是输入还是输出,因此这里都取
			Arguments: jb.Get("details.tool_info.arguments").String(),
			Output:    jb.Get("details.tool_info.output").String(),
			Thoughts:  jb.Get("details.tool_info.thoughts").String(),
		}
		*ru = RunStatusEvent(r)
	} else if strings.HasPrefix(string(r.EventType), "action_info") {
		r.Type = RunStatusEventObjectTypeActionInfo
		r.ActionInfo = make(map[string]any)
		err = json.Unmarshal([]byte(jb.Get("details.action_info").String()), &r.ActionInfo)
		if err != nil {
			return err
		}

		*ru = RunStatusEvent(r)
	} else if strings.HasPrefix(string(r.EventType), "tool_calls") {
		r.Type = RunStatusEventObjectTypeToolCalls
		jb.Get("details.tool_calls").ForEach(func(_, value gjson.Result) bool {
			var toolCall RunToolCalls
			_ = json.Unmarshal([]byte(value.String()), &toolCall)
			r.ToolCalls = append(r.ToolCalls, &toolCall)
			return true
		})

		*ru = RunStatusEvent(r)
	} else if strings.HasPrefix(string(r.EventType), "chat_info") {
		r.Type = RunStatusEventObjectTypeChatInfo
		r.ChatInfo = &ChatInfo{}
		err = json.Unmarshal([]byte(jb.Get("details.chat_info").String()), &r.ChatInfo)
		if err != nil {
			return err
		}

		*ru = RunStatusEvent(r)
	}

	return nil
}

// RunEvent sse 模式下 Run 接口返回的 RunEvent
// 由于 Event 有两种 status 和 message, 因此这是一个枚举类型
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/XgkQZxlquhlqnA
type RunEvent struct {
	Type RunEventTypes `json:"type,omitempty"`

	// StatusEvent 当 Type 为 status 时包含的事件
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/ac4e0aa2793c44
	StatusEvent *RunStatusEvent `json:"status_event,omitempty"`

	// MessageEvent 当 Type 为 message 时包含的事件
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/a25f17c5898940
	MessageEvent *RunMessageEvent `json:"message_event,omitempty"`

	// ToolMessageEvent 当 Type 为 tool_message 时包含的事件
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/VjnLrt1JorToa4
	// 此字段只有在使用内建工具和注册且按照 Assistant API 的 x-return-raw 规范开发的插件时才会返回
	ToolMessageEvent *RunToolMessageEvent `json:"tool_message_event,omitempty"`

	// ContentFlagEvent 当 Type 为 content_flag 时此字段不为空
	// 此消息只会在 sse 运行时通过 meta 传递 "option.output.content_flag.message":"true" 才会返回
	ContentFlagEvent *RunContentFlagEvent `json:"content_flag_event,omitempty"`

	// ThoughtsEvent 当 Type 为 thoughts 时包含的事件
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/dNbVbqfQ_6f5QB
	// NOTE: ThoughtsEvent 目前不对外提供,未实现

	// Error 错误
	Error error `json:"-"`
}

func (re *RunEvent) Bytes() []byte {
	switch re.Type {
	case RunEventStatus:
		b, _ := json.Marshal(re.StatusEvent)
		return b
	case RunEventMessage:
		b, _ := json.Marshal(re.MessageEvent)
		return b
	case RunEventToolMessage:
		b, _ := json.Marshal(re.ToolMessageEvent)
		return b
	case RunEventContentFlag:
		b, _ := json.Marshal(re.ContentFlagEvent)
		return b
	}

	return nil
}

// LoadFromEvent 从 ghttp.Event 加载 RunEvent
func (re *RunEvent) LoadFromEvent(ev *ghttp.Event) error {
	if ev.Event == string(RunEventStatus) {
		var statusEvent RunStatusEvent
		err := json.Unmarshal(ev.Data, &statusEvent)
		if err != nil {
			return fmt.Errorf("unmarshal status event error: %w, raw data: %s", err, ev.Data)
		}

		re.Type = RunEventStatus
		re.StatusEvent = &statusEvent
		return nil
	} else if ev.Event == string(RunEventMessage) {
		var messageEvent RunMessageEvent
		err := json.Unmarshal(ev.Data, &messageEvent)
		if err != nil {
			return fmt.Errorf("unmarshal message event error: %w, raw data: %s", err, ev.Data)
		}

		re.Type = RunEventMessage
		re.MessageEvent = &messageEvent
		return nil
	} else if ev.Event == string(RunEventToolMessage) {
		var toolMessageEvent RunToolMessageEvent
		err := json.Unmarshal(ev.Data, &toolMessageEvent)
		if err != nil {
			return fmt.Errorf("unmarshal tool message event error: %w, raw data: %s", err, ev.Data)
		}

		re.Type = RunEventToolMessage
		re.ToolMessageEvent = &toolMessageEvent
		return nil
	} else if ev.Event == string(RunEventContentFlag) {
		var contentFlagEvent RunContentFlagEvent
		err := json.Unmarshal(ev.Data, &contentFlagEvent)
		if err != nil {
			return fmt.Errorf("unmarshal content flag event error: %w, raw data: %s", err, ev.Data)
		}
		re.Type = RunEventContentFlag
		re.ContentFlagEvent = &contentFlagEvent
		return nil
	}

	return fmt.Errorf("unknown event type: %s,raw data: %s", ev.Event, ev.Data)
}

// ToolOutput 工具输出
type ToolOutput struct {
	// ToolCallID 工具调用 ID
	ToolCallID string `json:"tool_call_id,omitempty"`

	// Output 工具返回的 json 序列化内容
	Output string `json:"output"`

	// ForceTerminate 提交的工具输出不经过大模型润色,同事终止对话
	ForceTerminate bool `json:"force_terminate,omitempty"`
}

// StartRunOnThreadOptions 开始运行线程选项
type StartRunOnThreadOptions struct {
	Stream      bool
	AssistantID string
}

// StartRunOptions 开始运行选项
type StartRunOptions struct {
	// Model 模型
	Model Model `json:"model,omitempty"`

	// Source 来源
	Source string `json:"source,omitempty"`

	// ThreadID 线程 ID 与 Thread 二选一
	ThreadID string `json:"thread_id,omitempty"`

	// FunctionModelID
	FunctionModelID string `json:"function_model_id,omitempty"`

	// Thread run 可以直接指定一个 Thread 运行
	Thread RequestThread `json:"thread,omitempty"`

	// Tools 可以直接指定一串工具运行
	Tools []Tool `json:"tools,omitempty"`

	// MetaData 元数据
	MetaData map[string]any `json:"metadata,omitempty"`

	// Stream 是否是流式运行
	Stream bool `json:"stream,omitempty"`

	// AssistantID 助手 ID
	AssistantID string `json:"assistant_id,omitempty"`

	// Instructions 指令
	// 长度限制4096，所有用户输入的token和需要小于4096
	// 概述Agent的整体功能和定位，需要它扮演一个什么样的"角色。
	Instructions string `json:"instructions"`

	// ThoughtInstructions 思考指令
	// 长度限制4096，所有用户输入的token和需要小于4096与业务逻辑和规则相关的指令。希望模型遵守的行为规范和准则、要求都尽可能清晰、详尽地在这里给出描述。包括但不限于：
	//
	// 在什么情况下需要调用什么工具；在调用xxx工具之前需要先调用xxx工具获取什么信息；
	//
	// xxx工具填参的要求和偏好；（优先设定在工具描述中，如果较复杂，可以在这里强调）
	//
	// 在模型判断结果无法满足用户时采取什么行动（换一个工具，或者改变参数调同一个工具，或是告知用户无法满足但努力基于当前信息提供结果，等）；
	ThoughtInstructions string `json:"thought_instructions"`

	// ChatInstructions 聊天指令
	// 长度限制4096，所有用户输入的token和需要小于4096, 与模型最终给出的回复内容相关的指令。包括但不限于：
	//
	// 语气偏好
	//
	// 回复格式要求
	//
	// 回复内容的丰富程度
	//
	// 开头和结尾的形式要求，例如在结尾时需要向用户提问引导话题深入进行
	ChatInstructions string `json:"chat_instructions"`

	// UserStorage 用户偏好设定
	UserStorage string `json:"user_storage,omitempty"`

	// ResponseFormat 返回格式
	ResponseFormat ResponseFormat `json:"response_format,omitempty"`

	// 模型请求参数
	ModelParameters ModelParameters `json:"model_parameters,omitempty"`

	// 字符串格式或者对象
	ToolChoice any `json:"tool_choice,omitempty"`

	// cancel run 回调函数
	CallbackCancelRunStart CallbackCancelRunStart `json:"-"`

	CallbackCancelRunEnd CallbackCancelRunEnd `json:"-"`

	// 工具调用是否并行
	ParallelToolCalls bool `json:"parallel_tool_calls,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

// ModelParameters 模型参数
// 思考模型的默认值：
//
//	"temperature": 0.01,
//	"top_p": 0,
//	"penalty_score": 1.0,
//
// 回复模型EB的默认值：
//
//	"temperature": 0.8,
//	"top_p": 0.8,
//	"penalty_score": 1.0,
type ModelParameters struct {
	ThoughtParameters ModelParams `json:"thought_parameters,omitempty"`
	ChatParameters    ModelParams `json:"chat_parameters,omitempty"`
}

// ModelParams 模型参数
type ModelParams struct {
	Temperature  *float64 `json:"temperature,omitempty"`   // 取值>0.0 不传为线上默认值。
	TopP         *float64 `json:"top_p,omitempty"`         // 0.0<=取值<=1.0，不传为线上默认值。
	PenaltyScore *float64 `json:"penalty_score,omitempty"` // 1<=取值<=2 不传为线上默认值。
}

func (sr *StartRunOptions) CopyFrom(so *StartRunOptions) {
	if so == nil {
		return
	}

	sr.Model = so.Model
	sr.ThreadID = so.ThreadID
	sr.Thread = so.Thread
	sr.Tools = so.Tools
	sr.MetaData = so.MetaData
	sr.Stream = so.Stream
	sr.AssistantID = so.AssistantID
	sr.Instructions = so.Instructions
	sr.ThoughtInstructions = so.ThoughtInstructions
	sr.ChatInstructions = so.ChatInstructions
	sr.UserStorage = so.UserStorage
	sr.ResponseFormat = so.ResponseFormat
	sr.ReqOpts = so.ReqOpts
	sr.ToolChoice = so.ToolChoice
	sr.ParallelToolCalls = so.ParallelToolCalls
}

// GetRunsOptions 获取 Runs 选项
type GetRunsOptions struct {
	// ThreadID
	ThreadID string `json:"thread_id,omitempty"`

	// Limit 一次获取的文件数量
	Limit int `json:"limit,omitempty"`

	// Order desc/asc
	Order string `json:"order,omitempty"`

	// After 从某个 RunID 之后
	After string `json:"after,omitempty"`

	// Before 从某个 RunID 之前
	Before string `json:"before,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (gr *GetRunsOptions) Query() url.Values {
	q := url.Values{}
	if gr.Limit > 0 {
		q.Set("limit", strconv.Itoa(gr.Limit))
	}
	if gr.Order != "" {
		q.Set("order", gr.Order)
	}
	if gr.After != "" {
		q.Set("after", gr.After)
	}
	if gr.Before != "" {
		q.Set("before", gr.Before)
	}
	return q
}

// GetRunStepsOptions 获取 Run Steps 选项
type GetRunStepsOptions struct {
	// ThreadID
	ThreadID string `json:"thread_id,omitempty"`

	// RunID
	RunID string `json:"run_id,omitempty"`

	// Limit 一次获取的 Step 数量
	Limit int `json:"limit,omitempty"`

	// Order desc/asc
	Order string `json:"order,omitempty"`

	// After 从某个 RunID 之后
	After string `json:"after,omitempty"`

	// Before 从某个 RunID 之前
	Before string `json:"before,omitempty"`

	// 控制请求的选项
	ReqOpts []func(*r.Options) `json:"-"`
}

func (grso *GetRunStepsOptions) Query() url.Values {
	q := url.Values{}
	if grso.Limit > 0 {
		q.Set("limit", strconv.Itoa(grso.Limit))
	}
	if grso.Order != "" {
		q.Set("order", grso.Order)
	}
	if grso.After != "" {
		q.Set("after", grso.After)
	}
	if grso.Before != "" {
		q.Set("before", grso.Before)
	}
	return q
}
