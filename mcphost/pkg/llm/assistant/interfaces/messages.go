package interfaces

import (
	"context"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Messages 定义了对消息的操作接口。
type Messages interface {
	// Create 创建一个新的消息
	//
	// 参数:
	//   - ctx: 上下文对象，用于控制请求的生命周期
	//   - threadID: 线程 ID，用于标识消息所属的线程
	//   - role: 消息的角色，如用户或系统
	//   - content: 消息的内容
	//   - opts: 可选参数，配置请求的函数选项
	//
	// 返回值:
	//   - *types.Message: 创建的消息对象
	//   - error: 执行过程中产生的错误信息
	Create(
		ctx context.Context,
		threadID string,
		role types.Role,
		content string,
		opts ...func(*types.CreateMessageOptions),
	) (*types.Message, error)

	// Retrieve 从指定的线程中检索一条消息
	//
	// 参数:
	//   - ctx: 上下文对象，用于控制请求的生命周期
	//   - threadID: 线程的唯一标识符
	//   - messageID: 消息的唯一标识符
	//   - opts: 可选参数，用于配置请求选项
	//
	// 返回值:
	//   - *types.Message: 检索到的消息对象
	//   - error: 执行过程中产生的错误信息
	Retrieve(ctx context.Context, threadID, messageID string, opts ...func(*r.Options)) (*types.Message, error)

	// Update 更新指定的消息内容。
	//
	// 参数:
	//   - ctx: 上下文对象，用于控制请求的生命周期
	//   - threadID: 线程ID，表示消息所在的线程
	//   - messageID: 消息ID，表示需要更新的消息
	//   - content: 新的消息内容
	//   - opts: 可选参数，用于配置请求选项
	//
	// 返回值:
	//   - *types.Message: 更新后的消息对象
	//   - error: 如果更新失败，返回错误信息
	Update(ctx context.Context, threadID, messageID, content string, opts ...func(*types.UpdateMessageOptions)) (*types.Message, error)

	// List 获取指定线程的消息列表
	//
	// 参数:
	//   - ctx: 上下文对象，用于控制请求的生命周期
	//   - threadID: 线程的唯一标识符
	//   - opts: 用于配置获取消息选项的可变参数函数
	//
	// 返回值:
	//   - []*types.Message: 获取到的消息列表
	//   - bool: 是否有更多的消息
	//   - error: 获取消息过程中发生的错误
	List(ctx context.Context, threadID string, opts ...func(*types.GetMessagesOptions)) (msgs []*types.Message, hasMore bool, err error)

	// DeleteLastMessage 删除最后一轮消息
	//
	// 参数:
	//   - ctx: 上下文对象，用于控制请求的生命周期。
	//   - threadID: 线程的唯一标识符。
	//   - runID: 运行ID，用于标识消息所属的运行。
	//   - opts: 用于配置获取消息选项的可变参数函数。
	//
	// 返回:
	//   - deleted: 是否删除成功。
	//   - err: 执行过程中发生的错误信息。
	DeleteLastMessage(ctx context.Context, threadID, runID string, opts ...func(*types.DeleteLastMessageOptions)) (deleted bool, err error)
}
