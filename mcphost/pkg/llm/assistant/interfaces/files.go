package interfaces

import (
	"context"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Files 定义文件操作接口。
//
// 该接口提供了一组基本文件操作，如创建、检索、列出、删除和下载文件内容。
// 所有方法都支持使用上下文和可选的请求选项，允许调用者自定义行为。
type Files interface {
	// Create 将文件上传到系统。
	// 参数:
	//   - ctx: 上下文，包含请求的截止日期、取消信号等。
	//   - fileName: 待上传文件的名称。
	//   - fileContent: 待上传文件的内容。
	//   - opts: 可选参数，用于配置上传选项。
	// 返回值:
	//   - types.File: 上传后返回的文件信息。
	//   - error: 上传过程中的错误信息，如果有的话。
	Create(ctx context.Context, fileName, fileContent string, opts ...func(*types.UploadFileOptions)) (types.File, error)

	// Retrieve 检索文件信息。
	// 参数:
	//   - ctx: 上下文，用于控制请求的生命周期。
	//   - fileID: 需要检索的文件ID。
	//   - opts: 可选的函数，用于修改types.GetFileInfoOptions。
	// 返回:
	//   - files: 返回获取的文件信息（*types.File类型）。
	//   - err: 如果请求过程中出现错误，返回错误信息。
	Retrieve(ctx context.Context, fileID string, opts ...func(*types.GetFileInfoOptions)) (*types.File, error)

	// List 检索文件列表。
	// 参数:
	//   - ctx: 上下文，用于控制请求的生命周期。
	//   - opts: 可选参数，用于自定义 GetFiles 请求选项。
	// 返回值:
	//   - files: 文件切片。
	//   - err: 错误信息。
	List(ctx context.Context, opts ...func(*types.GetFilesOptions)) ([]*types.File, error)

	// Delete 删除指定ID的文件。
	// 参数:
	//   - ctx: 上下文对象，用于控制操作的生命周期。
	//   - fileID: 要删除的文件ID。
	//   - opts: 变参选项函数，用于定制请求选项。
	// 返回:
	//   - err: 如果发生错误，返回相应的错误信息。
	Delete(ctx context.Context, fileID string, opts ...func(*types.DeleteFileOptions)) error

	// Content 下载指定文件ID的内容。
	// 参数:
	//   - ctx: 上下文，用于控制请求的生命周期。
	//   - fileID: 需要下载的文件ID。
	//   - opts: 可选的请求选项，允许调用者自定义请求行为。
	// 返回值:
	//   - content: 下载的文件内容，字节数组形式。
	//   - err: 可能发生的错误，如果没有错误则为nil。
	Content(ctx context.Context, fileID string, opts ...func(*r.Options)) ([]byte, error)
}
