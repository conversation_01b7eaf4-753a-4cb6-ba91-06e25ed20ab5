package interfaces

import (
	"context"

	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Plannings function_call 极简模式协议
type Plannings interface {
	// Call 同步调用
	// 参数:
	//   - ctx: 上下文，包含请求的截止日期、取消信号等。
	// 返回值:
	//   - error: 上传过程中的错误信息，如果有的话。
	//   - resp: 返回的响应信息。
	Call(context.Context, ...func(*types.PlanningOptions)) (*types.PlanningResp, error)

	// StreamCall 流式调用
	// 参数:
	//   - ctx: 上下文，包含请求的截止日期、取消信号等。
	// 返回值:
	//   - error: 上传过程中的错误信息，如果有的话。
	StreamCall(context.Context, ...func(*types.PlanningOptions)) (<-chan *types.PlanningEvent, error)
}
