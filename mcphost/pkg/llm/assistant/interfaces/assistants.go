package interfaces

import (
	"context"

	r "github.com/mark3labs/mcphost/pkg/llm/assistant/req"
	"github.com/mark3labs/mcphost/pkg/llm/assistant/types"
)

// Assistants 是对 assistant 控制接口的抽象。
type Assistants interface {
	// Create 初始化一个具有给定参数和选项的新助手。
	//
	// 参数:
	//   - ctx: 用于管理请求生命周期的上下文。
	//   - model: 基于该模型的助手。
	//   - name: 分配给助手的名称。
	//   - description: 助手用途的简要描述。
	//   - instructions: 指导助手行为的指令。
	//   - tools: 助手可以使用的工具列表。
	//   - opts: 其他可选的配置功能。
	//
	// 返回:
	//   - *types.Assistant: 指向创建的助手对象的指针。
	//   - error: 如果创建失败的错误对象，成功则为 nil。
	Create(
		ctx context.Context,
		model types.Model,
		name, description, instructions string,
		tools []types.Tool,
		opts ...func(*types.CreateAssistantOptions),
	) (*types.Assistant, error)

	// Retrieve 根据给定的ID使用可选的请求选项获取助手。
	//
	// 参数：
	//	 - ctx: 用于控制取消和截止日期的上下文。
	//	 - id: 要检索的助手的标识符。
	//	 - opts: 修改请求选项的可选函数。
	//
	// 返回：
	//	 - *types.Assistant: 检索到的助手，如果发生错误则为nil。
	//	 - error: 如果在检索过程中出现问题则为错误对象，否则为nil。
	Retrieve(
		ctx context.Context,
		id string,
		opts ...func(*r.Options),
	) (*types.Assistant, error)

	// Update 更新修改当前上下文中具有给定 assistantID 的助手的详细信息。
	//
	// 参数：
	//	 - ctx: 用于管理请求生命周期的上下文。
	//	 - assistantID: 要修改的助手的唯一标识符。
	//	 - opts: 修改助手的可变函数选项。
	//
	// 返回：
	//   - *types.Assistant: 指向更新后的助手对象的指针。
	//   - error: 如果更新操作失败，则返回错误。
	Update(
		ctx context.Context,
		assistantID string,
		opts ...func(*types.UpdateAssistantOptions),
	) (*types.Assistant, error)

	// List 根据提供的选项检索助手列表。
	//
	// 参数：
	//   - ctx: 请求的上下文，用于取消和超时。
	//   - opts: 变参选项函数以自定义 GetAssistantsOptions。
	//
	// 返回：
	//   - []*types.Assistant: 指向 types.Assistant 对象的指针切片。
	//   - bool: 指示是否有更多助手可获取的布尔值。
	//   - err: 在检索过程中发生错误时的错误对象。
	List(
		ctx context.Context,
		opts ...func(*types.GetAssistantsOptions),
	) ([]*types.Assistant, bool, error)

	// Delete 删除指定的助手。
	//
	// 参数：
	//	 - ctx: 用于控制请求生命周期的上下文。
	//	 - assistantID: 要删除的助手的唯一标识符。
	//	 - opts: 可选的请求配置函数列表。
	//
	// 返回：
	//   - error: 如果删除成功，返回nil，否则返回错误信息。
	Delete(
		ctx context.Context,
		assistantID string,
		opts ...func(*r.Options),
	) error
}
