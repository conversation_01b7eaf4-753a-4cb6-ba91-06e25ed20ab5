package openai

import "encoding/json"

type CreateRequest struct {
	Model          string         `json:"model"`
	Messages       []MessageParam `json:"messages"`
	Tools          []Tool         `json:"tools,omitempty"`
	MaxTokens      int            `json:"max_tokens,omitempty"`
	Temperature    float32        `json:"temperature,omitempty"`
	Stream         *bool          `json:"stream,omitempty"`
	EnableThinking *bool          `json:"enable_thinking,omitempty"`
	ReturnTokenIDs *bool          `json:"return_token_ids,omitempty"`
}

type MessageParam struct {
	Role             string        `json:"role"`
	Content          *string       `json:"content"`
	ReasoningContent *string       `json:"reasoning_content,omitempty"`
	FunctionCall     *FunctionCall `json:"function_call,omitempty"`
	ToolCalls        []ToolCall    `json:"tool_calls,omitempty"`
	Name             string        `json:"name,omitempty"`
	ToolCallID       string        `json:"tool_call_id,omitempty"`
}

type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"`
	Function FunctionCall `json:"function"`
}

type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// UnmarshalJSON 自定义解码方法，兼容对象格式和字符串格式的 arguments
func (fc *FunctionCall) UnmarshalJSON(data []byte) error {
	// 定义一个临时结构体用于解码
	type Alias FunctionCall
	aux := &struct {
		Arguments json.RawMessage `json:"arguments"`
		*Alias
	}{
		Alias: (*Alias)(fc),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 尝试将 arguments 解析为字符串
	var argsStr string
	if err := json.Unmarshal(aux.Arguments, &argsStr); err == nil {
		// 如果成功解析为字符串，直接使用
		fc.Arguments = argsStr
		return nil
	}

	// 如果解析为字符串失败，说明是对象格式，将其转换为字符串
	fc.Arguments = string(aux.Arguments)
	return nil
}

type Tool struct {
	Type     string      `json:"type"`
	Function FunctionDef `json:"function"`
}

type FunctionDef struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Parameters  interface{} `json:"parameters"`
}

type APIResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Usage   Usage    `json:"usage"`
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Index        int          `json:"index"`
	Message      MessageParam `json:"message"`
	FinishReason string       `json:"finish_reason"`
}

type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}
