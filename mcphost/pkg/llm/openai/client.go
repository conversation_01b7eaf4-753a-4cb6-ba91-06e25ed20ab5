package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/mark3labs/mcphost/utils"
)

// HTTPError 包含HTTP状态码的错误类型
type HTTPError struct {
	StatusCode int
	Message    string
	Type       string
	Code       string
}

func (e *HTTPError) Error() string {
	if e.Type != "" && e.Message != "" {
		return fmt.Sprintf("HTTP %d: %s - %s", e.StatusCode, e.Type, e.Message)
	}
	return fmt.Sprintf("HTTP %d: %s", e.StatusCode, e.Message)
}

type Client struct {
	apiKey  string
	baseURL string
	client  *http.Client
}

func NewClient(apiKey string, baseURL string) *Client {
	if baseURL == "" {
		baseURL = "https://api.openai.com/v1"
	}
	return &Client{
		apiKey:  apiKey,
		baseURL: baseURL,
		client:  &http.Client{},
	}
}

func (c *Client) CreateChatCompletion(ctx context.Context, req CreateRequest) (*APIResponse, error) {
	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request: %w", err)
	}
	startTime := time.Now()
	// 记录请求 JSON（压缩格式）
	fmt.Printf("📤 OpenAI API 请求 JSON: %s\n", string(body))
	httpReq, err := http.NewRequestWithContext(
		ctx,
		"POST",
		fmt.Sprintf("%s/chat/completions", c.baseURL),
		bytes.NewReader(body),
	)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体用于日志记录
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// 记录响应 JSON（压缩格式）
	// 记录请求 JSON（压缩格式，正确显示中文）
	var responseData interface{}
	if err := json.Unmarshal(responseBody, &responseData); err == nil {
		buffer := &bytes.Buffer{}
		encoder := json.NewEncoder(buffer)
		encoder.SetEscapeHTML(false)
		if err := encoder.Encode(responseData); err == nil {
			// 移除末尾的换行符
			result := strings.TrimSuffix(buffer.String(), "\n")
			fmt.Printf("📤 OpenAI API 响应 JSON: %s\n", result)
		}
	}
	// 模型调用耗时
	fmt.Printf("🕒 模型调用耗时: %s\n", time.Since(startTime).String())

	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Error struct {
				Message string `json:"message"`
				Type    string `json:"type"`
				Code    string `json:"code"`
			} `json:"error"`
		}
		// 尝试解析错误响应
		if err := json.NewDecoder(bytes.NewReader(responseBody)).Decode(&errResp); err != nil {
			// 如果无法解析错误响应，尝试解码unicode转义序列后返回带状态码的通用错误
			decodedResponse := utils.TryDecodeResponse(responseBody)
			return nil, &HTTPError{
				StatusCode: resp.StatusCode,
				Message:    fmt.Sprintf("error response with status %d, response: %s", resp.StatusCode, decodedResponse),
			}
		}

		// 返回包含详细信息的HTTP错误
		return nil, &HTTPError{
			StatusCode: resp.StatusCode,
			Message:    errResp.Error.Message,
			Type:       errResp.Error.Type,
			Code:       errResp.Error.Code,
		}
	}

	var response APIResponse
	if err := json.NewDecoder(bytes.NewReader(responseBody)).Decode(&response); err != nil {
		return nil, fmt.Errorf("error decoding response: %w", err)
	}

	return &response, nil
}
