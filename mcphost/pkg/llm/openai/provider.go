package openai

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net"
	"strings"
	"time"

	"github.com/charmbracelet/log"
	"github.com/mark3labs/mcphost/pkg/history"
	"github.com/mark3labs/mcphost/pkg/llm"
)

// 重试配置常量
const (
	maxRetries    = 5
	baseDelay     = 500 * time.Millisecond
	maxDelay      = 5 * time.Second
	backoffFactor = 2
)

type Provider struct {
	client       *Client
	model        string
	systemPrompt string
}

func convertSchema(schema llm.Schema) map[string]interface{} {
	// Ensure required is a valid array, defaulting to empty if nil
	required := schema.Required
	if required == nil {
		required = []string{}
	}

	return map[string]interface{}{
		"type":       schema.Type,
		"properties": schema.Properties,
		"required":   required,
	}
}

func NewProvider(apiKey, baseURL, model, systemPrompt string) *Provider {
	return &Provider{
		client:       NewClient(apiKey, baseURL),
		model:        model,
		systemPrompt: systemPrompt,
	}
}

// isRetryableError 判断错误是否可以重试
func isRetryableError(err error) bool {
	// 检查是否为HTTPError类型
	if httpErr, ok := err.(*HTTPError); ok {
		switch httpErr.StatusCode {
		case 429, // Too Many Requests
			500, // Internal Server Error
			502, // Bad Gateway
			503, // Service Unavailable
			504: // Gateway Timeout
			return true
		default:
			return false
		}
	}

	// 网络错误
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}

	// DNS错误
	if dnsErr, ok := err.(*net.DNSError); ok && dnsErr.Temporary() {
		return true
	}

	// 连接错误
	errStr := err.Error()
	if strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "connection timeout") ||
		strings.Contains(errStr, "no such host") {
		return true
	}

	return false
}

// calculateDelay 计算重试延迟时间（指数退避）
func calculateDelay(attempt int) time.Duration {
	delay := time.Duration(math.Pow(backoffFactor, float64(attempt))) * baseDelay
	if delay > maxDelay {
		delay = maxDelay
	}
	return delay
}

func (p *Provider) CreateMessage(
	ctx context.Context,
	prompt string,
	messages []llm.Message,
	tools []llm.Tool,
) (llm.Message, error) {
	log.Debug("creating message",
		"prompt", prompt,
		"num_messages", len(messages),
		"num_tools", len(tools))

	openaiMessages := make([]MessageParam, 0, len(messages))

	// Add system prompt if provided
	if p.systemPrompt != "" {
		openaiMessages = append(openaiMessages, MessageParam{
			Role:    "system",
			Content: &p.systemPrompt,
		})
	}

	// Convert previous messages
	for _, msg := range messages {
		log.Debug("converting message",
			"role", msg.GetRole(),
			"content", msg.GetContent(),
			"is_tool_response", msg.IsToolResponse())

		param := MessageParam{
			Role: msg.GetRole(),
		}

		if msg.GetContent() != "" {
			content := msg.GetContent()
			param.Content = &content
		}

		// Handle function/tool calls
		toolCalls := msg.GetToolCalls()
		if len(toolCalls) > 0 {
			// Convert to OpenAI tool calls format
			param.ToolCalls = make([]ToolCall, len(toolCalls))
			for i, call := range toolCalls {
				args, err := json.Marshal(call.GetArguments())
				if err != nil {
					return nil, fmt.Errorf(
						"error marshaling function arguments: %w",
						err,
					)
				}

				param.ToolCalls[i] = ToolCall{
					ID:   call.GetID(),
					Type: "function",
					Function: FunctionCall{
						Name:      call.GetName(),
						Arguments: string(args),
					},
				}
			}
		}

		// Handle function/tool responses
		if msg.IsToolResponse() {
			log.Debug("processing tool response",
				"tool_call_id", msg.GetToolResponseID(),
				"raw_message", msg)

			// Extract content from tool response
			var contentStr string
			if content := msg.GetContent(); content != "" {
				contentStr = content
			} else {
				// Try to extract text from history message content blocks
				if historyMsg, ok := msg.(*history.HistoryMessage); ok {
					var texts []string
					for _, block := range historyMsg.Content {
						if block.Type == "tool_result" {
							if block.Text != "" {
								texts = append(texts, block.Text)
							} else if contentArray, ok := block.Content.([]interface{}); ok {
								for _, item := range contentArray {
									if contentMap, ok := item.(map[string]interface{}); ok {
										if text, ok := contentMap["text"]; ok {
											texts = append(texts, fmt.Sprint(text))
										}
									}
								}
							}
						}
					}
					contentStr = strings.Join(texts, "\n")
				}
			}

			if contentStr == "" {
				contentStr = "No content returned from function"
			}

			param.Content = &contentStr

			param.ToolCallID = msg.GetToolResponseID()
			// Don't set name field for tool responses
		}

		openaiMessages = append(openaiMessages, param)
	}

	// Log the final message array
	log.Debug("sending messages to OpenAI",
		"messages", openaiMessages,
		"num_tools", len(tools))

	// Note: prompt is already included in messages from root.go, so we don't add it again here

	// Convert tools to OpenAI format
	openaiTools := make([]Tool, len(tools))
	for i, tool := range tools {
		openaiTools[i] = Tool{
			Type: "function",
			Function: FunctionDef{
				Name:        tool.Name,
				Description: tool.Description,
				Parameters:  convertSchema(tool.InputSchema),
			},
		}
	}
	returnTokenIDs := true
	// 准备请求
	request := CreateRequest{
		Model:          p.model,
		Messages:       ConvertQwenMessage(openaiMessages, p.model),
		Tools:          openaiTools,
		MaxTokens:      8192,
		Temperature:    0.7,
		ReturnTokenIDs: &returnTokenIDs,
	}

	if strings.Contains(strings.ToLower(p.model), "qwen") {
		enableThinking := true
		stream := true
		request.EnableThinking = &enableThinking
		request.Stream = &stream
	} else {
		stream := false
		request.Stream = &stream
	}

	// 实现重试机制
	var lastErr error
	for attempt := 0; attempt <= maxRetries; attempt++ {
		// 检查context是否已取消
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("context cancelled: %w", ctx.Err())
		default:
		}

		// 记录重试日志
		if attempt > 0 {
			log.Debug("retrying OpenAI API call",
				"attempt", attempt,
				"max_retries", maxRetries,
				"last_error", lastErr)
		}

		// 发起API调用
		resp, err := p.client.CreateChatCompletion(ctx, request)
		if err != nil {
			lastErr = err
			requestJSON, _ := json.Marshal(request)
			log.Error("OpenAI API call failed", "request_json", string(requestJSON), "error", err)

			// 如果是最后一次尝试，直接返回错误
			if attempt == maxRetries {
				log.Error("max retries exceeded",
					"attempts", attempt+1,
					"final_error", err)
				return nil, fmt.Errorf("max retries (%d) exceeded, last error: %w", maxRetries, err)
			}

			// 计算延迟时间
			delay := calculateDelay(attempt)
			log.Debug("retryable error, waiting before retry",
				"error", err,
				"delay", delay,
				"attempt", attempt+1)

			// 等待重试
			timer := time.NewTimer(delay)
			select {
			case <-ctx.Done():
				timer.Stop()
				return nil, fmt.Errorf("context cancelled during retry delay: %w", ctx.Err())
			case <-timer.C:
				// 继续重试
			}

			continue
		}

		// 成功响应
		if len(resp.Choices) == 0 {
			return nil, fmt.Errorf("no choices in response")
		}

		if attempt > 0 {
			log.Info("OpenAI API call succeeded after retries", "attempt", attempt+1)
		}

		return &Message{Resp: resp, Choice: &resp.Choices[0]}, nil
	}

	// 这里不应该到达，但为了安全起见
	return nil, fmt.Errorf("unexpected error: max retries exceeded")
}

func (p *Provider) SupportsTools() bool {
	return true
}

func (p *Provider) Name() string {
	return "openai"
}

// Message implements the llm.Message interface
type Message struct {
	Resp   *APIResponse
	Choice *Choice
}

func (m *Message) GetRole() string {
	return m.Choice.Message.Role
}

func (m *Message) GetContent() string {
	if m.Choice.Message.Content == nil {
		return ""
	}
	return *m.Choice.Message.Content
}

func (m *Message) GetReasoningContent() string {
	if m.Choice.Message.ReasoningContent == nil {
		return ""
	}
	return *m.Choice.Message.ReasoningContent
}

func (m *Message) GetToolCalls() []llm.ToolCall {
	var calls []llm.ToolCall
	for _, call := range m.Choice.Message.ToolCalls {
		calls = append(calls, &ToolCallWrapper{call})
	}
	return calls
}

func (m *Message) IsToolResponse() bool {
	return m.Choice.Message.ToolCallID != ""
}

func (m *Message) GetToolResponseID() string {
	return m.Choice.Message.ToolCallID
}

func (m *Message) GetUsage() (int, int) {
	return m.Resp.Usage.PromptTokens, m.Resp.Usage.CompletionTokens
}

// ToolCallWrapper implements llm.ToolCall
type ToolCallWrapper struct {
	Call ToolCall
}

func (t *ToolCallWrapper) GetID() string {
	return t.Call.ID
}

func (t *ToolCallWrapper) GetName() string {
	return t.Call.Function.Name
}

func (t *ToolCallWrapper) GetArguments() map[string]interface{} {
	var args map[string]interface{}
	if err := json.Unmarshal([]byte(t.Call.Function.Arguments), &args); err != nil {
		return make(map[string]interface{})
	}
	return args
}

func ConvertQwenMessage(messages []MessageParam, model string) []MessageParam {
	if !strings.Contains(strings.ToLower(model), "qwen") {
		return messages
	}

	// qwen模型的特殊处理
	convertedMessages := make([]MessageParam, len(messages))
	copy(convertedMessages, messages)

	for i := range convertedMessages {
		msg := &convertedMessages[i]

		// 1. 当tool_calls不为空或者ToolCallID不为空的时候role改为function
		if (len(msg.ToolCalls) > 0 || msg.ToolCallID != "") && msg.Role != "function" {
			log.Debug("converting role to function for qwen",
				"original_role", msg.Role,
				"has_tool_calls", len(msg.ToolCalls) > 0,
				"has_tool_call_id", msg.ToolCallID != "")
			msg.Role = "function"
		}

		// 2. 当content为null的时候，赋值空字符串
		if msg.Content == nil {
			emptyContent := ""
			msg.Content = &emptyContent
			log.Debug("setting empty content for qwen message", "role", msg.Role)
		}
	}

	return convertedMessages
}
