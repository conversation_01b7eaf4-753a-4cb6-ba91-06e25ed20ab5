package openai

import (
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/mark3labs/mcphost/pkg/history"
	"github.com/mark3labs/mcphost/pkg/llm"
)

// 无需定义MockMessage，直接使用MessageParam

func TestConvertQwenMessage(t *testing.T) {
	tests := []struct {
		name        string
		model       string
		messages    []MessageParam
		expected    []MessageParam
		description string
	}{
		{
			name:  "非qwen模型不做处理",
			model: "gpt-4",
			messages: []MessageParam{
				{
					Role:       "user",
					Content:    nil,
					ToolCalls:  []ToolCall{{ID: "call_123", Type: "function"}},
					ToolCallID: "call_456",
				},
			},
			expected: []MessageParam{
				{
					Role:       "user", // 保持不变
					Content:    nil,    // 保持不变
					ToolCalls:  []ToolCall{{ID: "call_123", Type: "function"}},
					ToolCallID: "call_456",
				},
			},
			description: "非qwen模型应该原样返回消息",
		},
		{
			name:  "qwen模型-有tool_calls时role改为function",
			model: "qwen-max",
			messages: []MessageParam{
				{
					Role:      "assistant",
					Content:   stringPtr("test content"),
					ToolCalls: []ToolCall{{ID: "call_123", Type: "function"}},
				},
			},
			expected: []MessageParam{
				{
					Role:      "function", // 应该被改为function
					Content:   stringPtr("test content"),
					ToolCalls: []ToolCall{{ID: "call_123", Type: "function"}},
				},
			},
			description: "有tool_calls时，role应该被改为function",
		},
		{
			name:  "qwen模型-有ToolCallID时role改为function",
			model: "qwen-turbo",
			messages: []MessageParam{
				{
					Role:       "user",
					Content:    stringPtr("response content"),
					ToolCallID: "call_456",
				},
			},
			expected: []MessageParam{
				{
					Role:       "function", // 应该被改为function
					Content:    stringPtr("response content"),
					ToolCallID: "call_456",
				},
			},
			description: "有ToolCallID时，role应该被改为function",
		},
		{
			name:  "qwen模型-content为null时赋值空字符串",
			model: "qwen-plus",
			messages: []MessageParam{
				{
					Role:    "user",
					Content: nil, // null content
				},
			},
			expected: []MessageParam{
				{
					Role:    "user",
					Content: stringPtr(""), // 应该被赋值为空字符串
				},
			},
			description: "content为null时应该被赋值为空字符串",
		},
		{
			name:  "qwen模型-已经是function role不重复转换",
			model: "qwen-max",
			messages: []MessageParam{
				{
					Role:      "function",
					Content:   stringPtr("function response"),
					ToolCalls: []ToolCall{{ID: "call_123", Type: "function"}},
				},
			},
			expected: []MessageParam{
				{
					Role:      "function", // 保持function
					Content:   stringPtr("function response"),
					ToolCalls: []ToolCall{{ID: "call_123", Type: "function"}},
				},
			},
			description: "已经是function role的消息不应该重复转换",
		},
		{
			name:  "qwen模型-综合测试",
			model: "Qwen-Max-1201", // 测试大小写不敏感
			messages: []MessageParam{
				{
					Role:       "assistant",
					Content:    nil,
					ToolCalls:  []ToolCall{{ID: "call_123", Type: "function"}},
					ToolCallID: "call_456",
				},
				{
					Role:    "user",
					Content: stringPtr("normal message"),
				},
			},
			expected: []MessageParam{
				{
					Role:       "function",    // 应该被改为function
					Content:    stringPtr(""), // 应该被赋值为空字符串
					ToolCalls:  []ToolCall{{ID: "call_123", Type: "function"}},
					ToolCallID: "call_456",
				},
				{
					Role:    "user",
					Content: stringPtr("normal message"), // 保持不变
				},
			},
			description: "综合测试：role转换+content null处理，同时测试大小写不敏感",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertQwenMessage(tt.messages, tt.model)

			// 检查消息数量
			if len(result) != len(tt.expected) {
				t.Errorf("期望消息数量 %d，实际得到 %d", len(tt.expected), len(result))
				return
			}

			// 逐一检查每个消息
			for i, expectedMsg := range tt.expected {
				actualMsg := result[i]

				// 检查Role
				if actualMsg.Role != expectedMsg.Role {
					t.Errorf("消息 %d: 期望 Role=%s，实际得到 Role=%s",
						i, expectedMsg.Role, actualMsg.Role)
				}

				// 检查Content
				if expectedMsg.Content == nil && actualMsg.Content != nil {
					t.Errorf("消息 %d: 期望 Content=nil，实际得到 Content=%s",
						i, *actualMsg.Content)
				} else if expectedMsg.Content != nil && actualMsg.Content == nil {
					t.Errorf("消息 %d: 期望 Content=%s，实际得到 Content=nil",
						i, *expectedMsg.Content)
				} else if expectedMsg.Content != nil && actualMsg.Content != nil &&
					*expectedMsg.Content != *actualMsg.Content {
					t.Errorf("消息 %d: 期望 Content=%s，实际得到 Content=%s",
						i, *expectedMsg.Content, *actualMsg.Content)
				}

				// 检查ToolCalls数量
				if len(actualMsg.ToolCalls) != len(expectedMsg.ToolCalls) {
					t.Errorf("消息 %d: 期望 ToolCalls 数量=%d，实际得到数量=%d",
						i, len(expectedMsg.ToolCalls), len(actualMsg.ToolCalls))
				}

				// 检查ToolCallID
				if actualMsg.ToolCallID != expectedMsg.ToolCallID {
					t.Errorf("消息 %d: 期望 ToolCallID=%s，实际得到 ToolCallID=%s",
						i, expectedMsg.ToolCallID, actualMsg.ToolCallID)
				}
			}
		})
	}
}

// 辅助函数：创建字符串指针
func stringPtr(s string) *string {
	return &s
}

func TestProviderDoesNotDuplicatePrompt(t *testing.T) {

	// 创建模拟的消息历史，包含用户prompt
	messages := []llm.Message{
		&history.HistoryMessage{
			Role: "user",
			Content: []history.ContentBlock{{
				Type: "text",
				Text: "test user input",
			}},
		},
	}

	// 测试ConvertQwenMessage以确保消息格式正确
	messageParams := make([]MessageParam, len(messages))
	for i, msg := range messages {
		messageParams[i] = MessageParam{
			Role: msg.GetRole(),
		}
		if msg.GetContent() != "" {
			content := msg.GetContent()
			messageParams[i].Content = &content
		}
	}

	// 验证消息格式
	if len(messageParams) != 1 {
		t.Errorf("Expected 1 message, got %d", len(messageParams))
	}

	if messageParams[0].Role != "user" {
		t.Errorf("Expected role 'user', got '%s'", messageParams[0].Role)
	}

	if messageParams[0].Content == nil || *messageParams[0].Content != "test user input" {
		t.Errorf("Expected content 'test user input', got %v", messageParams[0].Content)
	}

	// 测试ConvertQwenMessage不会添加额外的prompt
	convertedMessages := ConvertQwenMessage(messageParams, "gpt-4")
	if len(convertedMessages) != len(messageParams) {
		t.Errorf("ConvertQwenMessage should not change message count, got %d messages from %d input messages",
			len(convertedMessages), len(messageParams))
	}
}

func TestCreateRequestWithDifferentModels(t *testing.T) {
	// 测试用例
	testCases := []struct {
		name              string
		model             string
		query             string
		expectedThinking  *bool
		expectedStream    *bool
		shouldContainQwen bool
	}{
		{
			name:              "Qwen模型_大写",
			model:             "qwen-max",
			query:             "你好，请帮我写一个Hello World程序",
			expectedThinking:  boolPtr(true),
			expectedStream:    boolPtr(true),
			shouldContainQwen: true,
		},
		{
			name:              "Qwen模型_小写",
			model:             "QWEN-TURBO",
			query:             "What is the capital of China?",
			expectedThinking:  boolPtr(true),
			expectedStream:    boolPtr(true),
			shouldContainQwen: true,
		},
		{
			name:              "Qwen模型_混合大小写",
			model:             "QwEn-Plus",
			query:             "解释一下机器学习的基本概念",
			expectedThinking:  boolPtr(true),
			expectedStream:    boolPtr(true),
			shouldContainQwen: true,
		},
		{
			name:              "非Qwen模型_GPT",
			model:             "gpt-4",
			query:             "How to implement a binary search?",
			expectedThinking:  nil,
			expectedStream:    nil,
			shouldContainQwen: false,
		},
		{
			name:              "非Qwen模型_Claude",
			model:             "claude-3-opus",
			query:             "写一段Python代码计算斐波那契数列",
			expectedThinking:  nil,
			expectedStream:    nil,
			shouldContainQwen: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟Provider
			p := &Provider{
				model: tc.model,
				// 这里可以设置其他必要的字段，如果测试需要真实请求的话
			}

			// 模拟输入消息
			content := tc.query
			openaiMessages := []MessageParam{
				{Role: "user", Content: &content},
			}

			// 创建请求对象（模拟代码逻辑）
			request := CreateRequest{
				Model:       p.model,
				Messages:    openaiMessages, // 这里应该调用ConvertQwenMessage，但为了测试简化
				Tools:       []Tool{},       // 空工具列表
				MaxTokens:   8192,
				Temperature: 0.7,
			}

			// 应用qwen特殊逻辑
			if strings.Contains(strings.ToLower(p.model), "qwen") {
				enableThinking := true
				stream := true
				request.EnableThinking = &enableThinking
				request.Stream = &stream
			}
			jsonData, err := json.MarshalIndent(request, "", "  ")
			if err != nil {
				t.Fatalf("JSON序列化失败: %v", err)
			}
			fmt.Println(string(jsonData))

			// 验证结果
			if tc.shouldContainQwen {
				if request.EnableThinking == nil {
					t.Errorf("模型 %s 应该设置 EnableThinking，但未设置", tc.model)
				} else if *request.EnableThinking != true {
					t.Errorf("模型 %s 的 EnableThinking 应该为 true，实际为 %v", tc.model, *request.EnableThinking)
				}

				if request.Stream == nil {
					t.Errorf("模型 %s 应该设置 Stream，但未设置", tc.model)
				} else if *request.Stream != true {
					t.Errorf("模型 %s 的 Stream 应该为 true，实际为 %v", tc.model, *request.Stream)
				}
			} else {
				if request.EnableThinking != nil {
					t.Errorf("模型 %s 不应该设置 EnableThinking，但设置了 %v", tc.model, *request.EnableThinking)
				}

				if request.Stream != nil {
					t.Errorf("模型 %s 不应该设置 Stream，但设置了 %v", tc.model, *request.Stream)
				}
			}

			// 验证基础字段
			if request.Model != tc.model {
				t.Errorf("模型名不匹配: 期望 %s，实际 %s", tc.model, request.Model)
			}

			if request.MaxTokens != 8192 {
				t.Errorf("MaxTokens 不匹配: 期望 8192，实际 %d", request.MaxTokens)
			}

			if request.Temperature != 0.7 {
				t.Errorf("Temperature 不匹配: 期望 0.7，实际 %f", request.Temperature)
			}

			// 打印请求信息用于调试
			t.Logf("模型: %s", request.Model)
			t.Logf("查询: %s", tc.query)
			if request.EnableThinking != nil {
				t.Logf("EnableThinking: %v", *request.EnableThinking)
			} else {
				t.Logf("EnableThinking: nil")
			}
			if request.Stream != nil {
				t.Logf("Stream: %v", *request.Stream)
			} else {
				t.Logf("Stream: nil")
			}
		})
	}
}

// TestMessageFormatOutput 测试消息格式输出
func TestMessageFormatOutput(t *testing.T) {
	content1 := "你是一个有用的助手"
	content2 := "请解释什么是递归"
	content3 := "递归是一种编程技术..."
	content4 := "能给个具体例子吗？"

	testMessages := []MessageParam{
		{Role: "system", Content: &content1},
		{Role: "user", Content: &content2},
		{Role: "assistant", Content: &content3},
		{Role: "user", Content: &content4},
	}

	// 测试JSON序列化
	jsonData, err := json.MarshalIndent(testMessages, "", "  ")
	if err != nil {
		t.Fatalf("JSON序列化失败: %v", err)
	}

	t.Logf("消息格式JSON:\n%s", string(jsonData))

	// 验证消息结构
	for i, msg := range testMessages {
		if msg.Role == "" {
			t.Errorf("消息 %d 的角色不能为空", i)
		}
		if msg.Content == nil || *msg.Content == "" {
			t.Errorf("消息 %d 的内容不能为空", i)
		}

		// 验证角色有效性
		validRoles := []string{"system", "user", "assistant", "tool"}
		isValidRole := false
		for _, validRole := range validRoles {
			if msg.Role == validRole {
				isValidRole = true
				break
			}
		}
		if !isValidRole {
			t.Errorf("消息 %d 的角色 '%s' 无效", i, msg.Role)
		}
	}
}

// TestModelDetection 测试模型检测逻辑
func TestModelDetection(t *testing.T) {
	qwenModels := []string{
		"qwen-max",
		"QWEN-TURBO",
		"QwEn-Plus",
		"qwen2-72b",
		"alibaba-qwen-max",
	}

	nonQwenModels := []string{
		"gpt-4",
		"gpt-3.5-turbo",
		"claude-3-opus",
		"claude-3-sonnet",
		"gemini-pro",
	}

	// 测试qwen模型检测
	for _, model := range qwenModels {
		if !strings.Contains(strings.ToLower(model), "qwen") {
			t.Errorf("模型 %s 应该被识别为qwen模型", model)
		}
	}

	// 测试非qwen模型检测
	for _, model := range nonQwenModels {
		if strings.Contains(strings.ToLower(model), "qwen") {
			t.Errorf("模型 %s 不应该被识别为qwen模型", model)
		}
	}
}

// boolPtr 辅助函数，返回布尔值的指针
func boolPtr(b bool) *bool {
	return &b
}

// 如果需要真实API测试的话，可以使用这个函数
// 注意：这需要真实的API凭据
func TestRealAPICall(t *testing.T) {
	// 跳过真实API测试，除非明确指定
	t.Skip("跳过真实API测试 - 需要设置API凭据")

	// 如果要进行真实测试，请取消注释并提供真实的API信息
	/*
		apiURL := "YOUR_API_URL_HERE"
		apiKey := "YOUR_API_KEY_HERE"
		model := "qwen-max"

		provider := &Provider{
			model: model,
			// 设置其他必要字段
		}

		// 执行真实API调用
		// ... 实现真实API调用逻辑
	*/
}

// TestFunctionCall_UnmarshalJSON 测试 FunctionCall 的自定义 JSON 解码
func TestFunctionCall_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name        string
		jsonData    string
		expectedArg string
		shouldError bool
	}{
		{
			name:        "object format arguments",
			jsonData:    `{"name": "test_function", "arguments": {"path": "./test.py"}}`,
			expectedArg: `{"path": "./test.py"}`,
			shouldError: false,
		},
		{
			name:        "string format arguments",
			jsonData:    `{"name": "test_function", "arguments": "{\"path\": \"./test.py\"}"}`,
			expectedArg: `{"path": "./test.py"}`,
			shouldError: false,
		},
		{
			name:        "complex object arguments",
			jsonData:    `{"name": "test_function", "arguments": {"dryRun": true, "edits": [{"oldText": "old", "newText": "new"}]}}`,
			expectedArg: `{"dryRun": true, "edits": [{"oldText": "old", "newText": "new"}]}`,
			shouldError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var fc FunctionCall
			err := json.Unmarshal([]byte(tt.jsonData), &fc)

			if tt.shouldError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if fc.Name != "test_function" {
				t.Errorf("expected name 'test_function', got '%s'", fc.Name)
			}

			// 验证 arguments 是否正确转换为字符串
			var expectedArgs, actualArgs map[string]interface{}
			if err := json.Unmarshal([]byte(tt.expectedArg), &expectedArgs); err != nil {
				t.Fatalf("failed to unmarshal expected args: %v", err)
			}
			if err := json.Unmarshal([]byte(fc.Arguments), &actualArgs); err != nil {
				t.Fatalf("failed to unmarshal actual args: %v", err)
			}

			expectedJSON, _ := json.Marshal(expectedArgs)
			actualJSON, _ := json.Marshal(actualArgs)
			if string(expectedJSON) != string(actualJSON) {
				t.Errorf("expected arguments %s, got %s", string(expectedJSON), string(actualJSON))
			}
		})
	}
}

// TestAPIResponse_DecodeWithObjectArguments 测试完整的 API 响应解码（包含对象格式的 arguments）
func TestAPIResponse_DecodeWithObjectArguments(t *testing.T) {
	// 模拟原始问题：API 返回对象格式的 arguments
	objectFormatResponse := `{
		"id": "chatcmpl-9f3558c28cb245e1b6b00577fe1df301",
		"object": "chat.completion",
		"created": 125760463,
		"model": "EB_SPV4_32K_DNI_VOC_test_0608v2_dpo0616v1p0618v1_ema_platform",
		"choices": [
			{
				"index": 0,
				"message": {
					"role": "assistant",
					"content": "",
					"tool_calls": [
						{
							"id": "call_63e4186af8a342fc8194d05a8535da79",
							"type": "function",
							"function": {
								"name": "file_system__read_file",
								"arguments": {
									"path": "./file_system_mock_data/task_53/script_module.py"
								}
							}
						},
						{
							"id": "call_4f97e1d52b7a4c19b5767ff364235a0f",
							"type": "function",
							"function": {
								"name": "file_system__create_directory",
								"arguments": {
									"path": "./file_system_output/task_53"
								}
							}
						}
					]
				},
				"finish_reason": "stop"
			}
		],
		"usage": {
			"prompt_tokens": 123,
			"completion_tokens": 123,
			"total_tokens": 123
		}
	}`

	var response APIResponse
	err := json.Unmarshal([]byte(objectFormatResponse), &response)
	if err != nil {
		t.Fatalf("❌ JSON 解析失败: %v", err)
	}

	t.Log("✅ JSON 解析成功")

	if len(response.Choices) == 0 {
		t.Fatal("❌ 没有找到 choices")
	}

	choice := response.Choices[0]
	if len(choice.Message.ToolCalls) == 0 {
		t.Fatal("❌ 没有找到 tool_calls")
	}

	// 测试第一个工具调用
	toolCall1 := choice.Message.ToolCalls[0]
	wrapper1 := &ToolCallWrapper{Call: toolCall1}

	if wrapper1.GetName() != "file_system__read_file" {
		t.Errorf("期望工具名称 'file_system__read_file'，实际得到 '%s'", wrapper1.GetName())
	}

	args1 := wrapper1.GetArguments()
	if path, exists := args1["path"]; !exists {
		t.Error("❌ 未能提取第一个工具的 path 参数")
	} else if path != "./file_system_mock_data/task_53/script_module.py" {
		t.Errorf("期望 path 为 './file_system_mock_data/task_53/script_module.py'，实际得到 '%s'", path)
	} else {
		t.Logf("✅ 成功提取第一个工具的 path 参数: %s", path)
	}

	// 测试第二个工具调用
	toolCall2 := choice.Message.ToolCalls[1]
	wrapper2 := &ToolCallWrapper{Call: toolCall2}

	if wrapper2.GetName() != "file_system__create_directory" {
		t.Errorf("期望工具名称 'file_system__create_directory'，实际得到 '%s'", wrapper2.GetName())
	}

	args2 := wrapper2.GetArguments()
	if path, exists := args2["path"]; !exists {
		t.Error("❌ 未能提取第二个工具的 path 参数")
	} else if path != "./file_system_output/task_53" {
		t.Errorf("期望 path 为 './file_system_output/task_53'，实际得到 '%s'", path)
	} else {
		t.Logf("✅ 成功提取第二个工具的 path 参数: %s", path)
	}

	t.Log("✅ 所有工具调用参数解析成功")
}
