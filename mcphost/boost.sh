#!/bin/bash

# 创建日志目录
mkdir -p ${DATAENG_OUTPUT_DIR}

# 生成日志文件名：日期_时间_随机数.log
LOG_FILE_NAME=$(date '+%Y%m%d_%H%M%S_')${RANDOM}
LOG_FILE="${DATAENG_OUTPUT_DIR}/${LOG_FILE_NAME}.log"


echo "日志将保存到: $LOG_FILE"
echo "开始执行 mcphost..."

# 确定mcphost可执行文件路径
if [ -n "$MCPHOST_PATH" ]; then
    MCPHOST_EXECUTABLE="$MCPHOST_PATH"
    echo "使用环境变量指定的mcphost路径: $MCPHOST_EXECUTABLE"
else
    MCPHOST_EXECUTABLE="./mcphost"
    echo "使用默认的mcphost路径: $MCPHOST_EXECUTABLE"
fi

# 检查mcphost可执行文件是否存在
if [ ! -f "$MCPHOST_EXECUTABLE" ]; then
    echo "错误: mcphost可执行文件不存在: $MCPHOST_EXECUTABLE"
    exit 1
fi

# 启动应用并重定向输出到日志文件
"$MCPHOST_EXECUTABLE" --config ${DATAENG_INPUT_DATA_DIR}/model_config.json \
    --mcpservers ${DATAENG_INPUT_DATA_DIR}/mcpservers.json \
    --bench ${DATAENG_INPUT_DATA_DIR}/bench.json \
    -o ${DATAENG_OUTPUT_DIR} 2>&1 | tee "$LOG_FILE"

echo "mcphost执行完成，日志已保存到: $LOG_FILE"

# 检查是否存在check.py脚本
if [ -f "${DATAENG_INPUT_DATA_DIR}/check.py" ]; then
    echo "发现检查脚本，开始执行检查..."
    
    # 创建检查日志文件名
    CHECK_LOG_FILE_NAME=$(date '+%Y%m%d_%H%M%S_')${RANDOM}_check
    CHECK_LOG_FILE="${DATAENG_OUTPUT_DIR}/${CHECK_LOG_FILE_NAME}.log"
    
    echo "检查日志将保存到: $CHECK_LOG_FILE"
    
    # 从model_config.json中读取检查模型配置
    if [ -f "${DATAENG_INPUT_DATA_DIR}/model_config.json" ]; then
        # 使用python提取检查模型配置
        CHECK_MODEL=$(python -c "
import json
import sys
try:
    with open('${DATAENG_INPUT_DATA_DIR}/model_config.json', 'r') as f:
        config = json.load(f)
    print(config.get('check_model_name', ''))
except:
    print('')
")
        
        CHECK_BASE_URL=$(python -c "
import json
import sys
try:
    with open('${DATAENG_INPUT_DATA_DIR}/model_config.json', 'r') as f:
        config = json.load(f)
    print(config.get('check_openai_url', ''))
except:
    print('')
")
        
        CHECK_API_KEY=$(python -c "
import json
import sys
try:
    with open('${DATAENG_INPUT_DATA_DIR}/model_config.json', 'r') as f:
        config = json.load(f)
    print(config.get('check_openai_api_key', ''))
except:
    print('')
")
        
        # 如果检查模型配置存在，执行检查脚本
        if [ -n "$CHECK_MODEL" ] && [ -n "$CHECK_BASE_URL" ] && [ -n "$CHECK_API_KEY" ]; then
            echo "使用检查模型: $CHECK_MODEL"
            echo "检查API地址: $CHECK_BASE_URL"
            
            # 执行检查脚本
            python "${DATAENG_INPUT_DATA_DIR}/check.py" \
                --bench "${DATAENG_INPUT_DATA_DIR}/bench.json" \
                --result "${DATAENG_OUTPUT_DIR}/result.json" \
                --model "$CHECK_MODEL" \
                --base-url "$CHECK_BASE_URL" \
                --api-key "$CHECK_API_KEY" \
                --output "${DATAENG_OUTPUT_DIR}/check_result.json" \
                --work-dir "./" 2>&1 | tee "$CHECK_LOG_FILE"
            
            echo "检查脚本执行完成，日志已保存到: $CHECK_LOG_FILE"
        else
            echo "检查模型配置不完整，跳过检查脚本执行"
            echo "CHECK_MODEL: $CHECK_MODEL"
            echo "CHECK_BASE_URL: $CHECK_BASE_URL"
            echo "CHECK_API_KEY: ${CHECK_API_KEY:0:10}..."
        fi
    else
        echo "model_config.json不存在，跳过检查脚本执行"
    fi
else
    echo "未发现检查脚本，跳过检查步骤"
fi

echo "所有执行完成"