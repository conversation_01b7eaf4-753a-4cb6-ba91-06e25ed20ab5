package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"
)

// 演示修复后的中文 JSON 显示效果
func main() {
	fmt.Println("=== 中文 JSON 显示修复演示 ===\n")

	// 模拟包含中文的 API 请求
	requestData := map[string]interface{}{
		"model": "gpt-4",
		"messages": []map[string]interface{}{
			{
				"role":    "user",
				"content": "请帮我分析这个数据库查询性能问题，我的查询很慢需要优化",
			},
		},
		"tools": []map[string]interface{}{
			{
				"type": "function",
				"function": map[string]interface{}{
					"name":        "database_analyzer",
					"description": "分析数据库查询性能，提供优化建议",
					"parameters": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"query": map[string]interface{}{
								"type":        "string",
								"description": "需要分析的SQL查询语句",
							},
							"table_info": map[string]interface{}{
								"type":        "string",
								"description": "表结构信息",
							},
						},
						"required": []string{"query"},
					},
				},
			},
		},
		"temperature": 0.7,
	}

	// 模拟包含中文的 API 响应
	responseData := map[string]interface{}{
		"id":      "chatcmpl-123456",
		"object":  "chat.completion",
		"created": 1677652288,
		"model":   "gpt-4",
		"choices": []map[string]interface{}{
			{
				"index": 0,
				"message": map[string]interface{}{
					"role":    "assistant",
					"content": "我建议在user_id列上添加索引来优化查询性能。这样可以显著提高查询速度。",
					"tool_calls": []map[string]interface{}{
						{
							"id":   "call_abc123",
							"type": "function",
							"function": map[string]interface{}{
								"name":      "database_analyzer",
								"arguments": `{"query": "SELECT * FROM users WHERE user_id = 123", "table_info": "用户表包含100万条记录"}`,
							},
						},
					},
				},
				"finish_reason": "tool_calls",
			},
		},
		"usage": map[string]interface{}{
			"prompt_tokens":     150,
			"completion_tokens": 80,
			"total_tokens":      230,
		},
	}

	// 演示修复后的日志格式
	fmt.Println("1. 请求 JSON（修复后，中文正确显示）:")
	demonstrateFixedLogging("📤 OpenAI API 请求 JSON", requestData)

	fmt.Println("2. 响应 JSON（修复后，中文正确显示）:")
	demonstrateFixedLogging("📥 OpenAI API 响应 JSON", responseData)

	fmt.Println("=== 修复效果对比 ===")
	fmt.Println("❌ 修复前：中文被转义为 \\u4e2d\\u6587")
	fmt.Println("✅ 修复后：中文正常显示为 中文")
	fmt.Println()

	fmt.Println("=== 技术实现 ===")
	fmt.Println("✅ 使用 json.NewEncoder() 和 SetEscapeHTML(false)")
	fmt.Println("✅ 压缩格式，单行显示，便于日志查看")
	fmt.Println("✅ 保持原始 JSON 结构，可直接复制使用")
	fmt.Println("✅ 性能优化，避免多次序列化/反序列化")
	fmt.Println()

	fmt.Println("=== 使用场景 ===")
	fmt.Println("🔍 调试包含中文的 API 调用")
	fmt.Println("📊 监控中文内容的处理情况")
	fmt.Println("🧪 复制 JSON 到测试工具进行验证")
	fmt.Println("📝 审计和分析中文内容的传输")
}

// demonstrateFixedLogging 演示修复后的日志记录格式
func demonstrateFixedLogging(prefix string, data interface{}) {
	// 模拟修复后的代码逻辑
	if jsonData, err := json.Marshal(data); err == nil {
		var tempData interface{}
		if err := json.Unmarshal(jsonData, &tempData); err == nil {
			buffer := &bytes.Buffer{}
			encoder := json.NewEncoder(buffer)
			encoder.SetEscapeHTML(false)
			if err := encoder.Encode(tempData); err == nil {
				// 移除末尾的换行符
				result := strings.TrimSuffix(buffer.String(), "\n")
				fmt.Printf("%s: %s\n\n", prefix, result)
			}
		}
	}
}
