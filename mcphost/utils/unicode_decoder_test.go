package utils

import (
	"testing"
)

func TestDecodeUnicodeEscapes(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "中文unicode转义序列",
			input:    "\\u8bf7\\u6c42\\u5931\\u8d25",
			expected: "请求失败",
		},
		{
			name:     "混合中英文",
			input:    "error: \\u8bf7\\u6c42\\u5931\\u8d25",
			expected: "error: 请求失败",
		},
		{
			name:     "完整错误消息",
			input:    "{\"error\":{\"message\":\"\\u5f53\\u524d\\u5206\\u7ec4\\u4e0a\\u6e38\\u8d1f\\u8f7d\\u5df2\\u9971\\u548c\\uff0c\\u8bf7\\u7a0d\\u540e\\u518d\\u8bd5 (request id: 202506102143277586716022vgzsvsG)\",\"type\":\"TooManyRequests\",\"param\":\"\",\"code\":\"InflightBatchsizeExceeded\"}}",
			expected: "{\"error\":{\"message\":\"当前分组上游负载已饱和，请稍后再试 (request id: 202506102143277586716022vgzsvsG)\",\"type\":\"TooManyRequests\",\"param\":\"\",\"code\":\"InflightBatchsizeExceeded\"}}",
		},
		{
			name:     "无unicode转义序列",
			input:    "normal text without unicode",
			expected: "normal text without unicode",
		},
		{
			name:     "无效的unicode转义序列",
			input:    "\\uXXXX invalid unicode",
			expected: "\\uXXXX invalid unicode",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := DecodeUnicodeEscapes(tt.input)
			if result != tt.expected {
				t.Errorf("DecodeUnicodeEscapes(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestTryDecodeResponse(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		expected string
	}{
		{
			name:     "JSON字符串格式",
			input:    []byte(`"\u8bf7\u6c42\u5931\u8d25"`),
			expected: "请求失败",
		},
		{
			name:     "普通文本包含unicode转义序列",
			input:    []byte("error: \\u8bf7\\u6c42\\u5931\\u8d25"),
			expected: "error: 请求失败",
		},
		{
			name:     "普通文本无unicode转义序列",
			input:    []byte("normal error message"),
			expected: "normal error message",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TryDecodeResponse(tt.input)
			if result != tt.expected {
				t.Errorf("TryDecodeResponse(%q) = %q, want %q", string(tt.input), result, tt.expected)
			}
		})
	}
}
