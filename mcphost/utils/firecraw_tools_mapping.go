package utils

import (
	"strings"

	"github.com/mark3labs/mcphost/pkg/llm"
)

// ProcessFirecrawlTool 处理Firecrawl工具，返回自定义的参数结构
func ProcessFirecrawlTool(tool llm.Tool) map[string]interface{} {
	// 根据工具名称匹配对应的Firecrawl工具定义
	for _, firecrawlTool := range FirecrawlTools {
		if function, ok := firecrawlTool["function"].(map[string]interface{}); ok {
			if name, ok := function["name"].(string); ok && strings.Contains(tool.Name, name) {
				// 返回匹配的工具参数
				if parameters, ok := function["parameters"].(map[string]interface{}); ok {
					return parameters
				}
			}
		}
	}

	// 如果没有匹配到，返回原始工具的参数结构
	return map[string]interface{}{
		"type":       tool.InputSchema.Type,
		"properties": tool.InputSchema.Properties,
		"required":   tool.InputSchema.Required,
	}
}

// FirecrawlTools 定义Firecrawl工具的标准参数结构
var FirecrawlTools = []map[string]interface{}{
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_scrape",
			"description": "Scrape a single webpage with advanced options for content extraction. Supports various formats including markdown, HTML, and screenshots. Can execute custom actions like clicking or scrolling before scraping.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"url": map[string]interface{}{
						"type":        "string",
						"description": "The URL to scrape",
					},
					"formats": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
							"enum": []string{"markdown", "html", "rawHtml", "screenshot", "links", "screenshot@fullPage", "extract"},
						},
						"default":     []string{"markdown"},
						"description": "Content formats to extract (default: ['markdown'])",
					},
					"onlyMainContent": map[string]interface{}{
						"type":        "boolean",
						"description": "Extract only the main content, filtering out navigation, footers, etc.",
					},
					"includeTags": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "HTML tags to specifically include in extraction",
					},
					"excludeTags": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "HTML tags to exclude from extraction",
					},
					"waitFor": map[string]interface{}{
						"type":        "number",
						"description": "Time in milliseconds to wait for dynamic content to load",
					},
					"timeout": map[string]interface{}{
						"type":        "number",
						"description": "Maximum time in milliseconds to wait for the page to load",
					},
					"actions": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "object",
							"properties": map[string]interface{}{
								"type": map[string]interface{}{
									"type":        "string",
									"enum":        []string{"wait", "click", "screenshot", "write", "press", "scroll", "scrape", "executeJavascript"},
									"description": "Type of action to perform",
								},
								"selector": map[string]interface{}{
									"type":        "string",
									"description": "CSS selector for the target element",
								},
								"milliseconds": map[string]interface{}{
									"type":        "number",
									"description": "Time to wait in milliseconds (for wait action)",
								},
								"text": map[string]interface{}{
									"type":        "string",
									"description": "Text to write (for write action)",
								},
								"key": map[string]interface{}{
									"type":        "string",
									"description": "Key to press (for press action)",
								},
								"direction": map[string]interface{}{
									"type":        "string",
									"enum":        []string{"up", "down"},
									"description": "Scroll direction",
								},
								"script": map[string]interface{}{
									"type":        "string",
									"description": "JavaScript code to execute",
								},
								"fullPage": map[string]interface{}{
									"type":        "boolean",
									"description": "Take full page screenshot",
								},
							},
							"required": []string{"type"},
						},
						"description": "List of actions to perform before scraping",
					},
					"extract": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"schema": map[string]interface{}{
								"type":        "object",
								"properties":  map[string]interface{}{},
								"description": "Schema for structured data extraction",
							},
							"systemPrompt": map[string]interface{}{
								"type":        "string",
								"description": "System prompt for LLM extraction",
							},
							"prompt": map[string]interface{}{
								"type":        "string",
								"description": "User prompt for LLM extraction",
							},
						},
						"description": "Configuration for structured data extraction",
					},
					"mobile": map[string]interface{}{
						"type":        "boolean",
						"description": "Use mobile viewport",
					},
					"skipTlsVerification": map[string]interface{}{
						"type":        "boolean",
						"description": "Skip TLS certificate verification",
					},
					"removeBase64Images": map[string]interface{}{
						"type":        "boolean",
						"description": "Remove base64 encoded images from output",
					},
					"location": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"country": map[string]interface{}{
								"type":        "string",
								"description": "Country code for geolocation",
							},
							"languages": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
								},
								"description": "Language codes for content",
							},
						},
						"description": "Location settings for scraping",
					},
				},
				"required": []string{"url"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_map",
			"description": "Discover URLs from a starting point. Can use both sitemap.xml and HTML link discovery.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"url": map[string]interface{}{
						"type":        "string",
						"description": "Starting URL for URL discovery",
					},
					"search": map[string]interface{}{
						"type":        "string",
						"description": "Optional search term to filter URLs",
					},
					"ignoreSitemap": map[string]interface{}{
						"type":        "boolean",
						"description": "Skip sitemap.xml discovery and only use HTML links",
					},
					"sitemapOnly": map[string]interface{}{
						"type":        "boolean",
						"description": "Only use sitemap.xml for discovery, ignore HTML links",
					},
					"includeSubdomains": map[string]interface{}{
						"type":        "boolean",
						"description": "Include URLs from subdomains in results",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "Maximum number of URLs to return",
					},
				},
				"required": []string{"url"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_crawl",
			"description": "Start an asynchronous crawl of multiple pages from a starting URL. Supports depth control, path filtering, and webhook notifications.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"url": map[string]interface{}{
						"type":        "string",
						"description": "Starting URL for the crawl",
					},
					"excludePaths": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "URL paths to exclude from crawling",
					},
					"includePaths": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "Only crawl these URL paths",
					},
					"maxDepth": map[string]interface{}{
						"type":        "number",
						"description": "Maximum link depth to crawl",
					},
					"ignoreSitemap": map[string]interface{}{
						"type":        "boolean",
						"description": "Skip sitemap.xml discovery",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "Maximum number of pages to crawl",
					},
					"allowBackwardLinks": map[string]interface{}{
						"type":        "boolean",
						"description": "Allow crawling links that point to parent directories",
					},
					"allowExternalLinks": map[string]interface{}{
						"type":        "boolean",
						"description": "Allow crawling links to external domains",
					},
					"webhook": map[string]interface{}{
						"oneOf": []map[string]interface{}{
							{
								"type":        "string",
								"description": "Webhook URL to notify when crawl is complete",
							},
							{
								"type": "object",
								"properties": map[string]interface{}{
									"url": map[string]interface{}{
										"type":        "string",
										"description": "Webhook URL",
									},
									"headers": map[string]interface{}{
										"type":        "object",
										"properties":  map[string]interface{}{},
										"description": "Custom headers for webhook requests",
									},
								},
								"required": []string{"url"},
							},
						},
					},
					"deduplicateSimilarURLs": map[string]interface{}{
						"type":        "boolean",
						"description": "Remove similar URLs during crawl",
					},
					"ignoreQueryParameters": map[string]interface{}{
						"type":        "boolean",
						"description": "Ignore query parameters when comparing URLs",
					},
					"scrapeOptions": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"formats": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
									"enum": []string{"markdown", "html", "rawHtml", "screenshot", "links", "screenshot@fullPage", "extract"},
								},
							},
							"onlyMainContent": map[string]interface{}{
								"type": "boolean",
							},
							"includeTags": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
								},
							},
							"excludeTags": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
								},
							},
							"waitFor": map[string]interface{}{
								"type": "number",
							},
						},
						"description": "Options for scraping each page",
					},
				},
				"required": []string{"url"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_check_crawl_status",
			"description": "Check the status of a crawl job.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"id": map[string]interface{}{
						"type":        "string",
						"description": "Crawl job ID to check",
					},
				},
				"required": []string{"id"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_search",
			"description": "Search and retrieve content from web pages with optional scraping. Returns SERP results by default (url, title, description) or full page content when scrapeOptions are provided.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"query": map[string]interface{}{
						"type":        "string",
						"description": "Search query string",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "Maximum number of results to return (default: 5)",
					},
					"lang": map[string]interface{}{
						"type":        "string",
						"description": "Language code for search results (default: en)",
					},
					"country": map[string]interface{}{
						"type":        "string",
						"description": "Country code for search results (default: us)",
					},
					"tbs": map[string]interface{}{
						"type":        "string",
						"description": "Time-based search filter",
					},
					"filter": map[string]interface{}{
						"type":        "string",
						"description": "Search filter",
					},
					"location": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"country": map[string]interface{}{
								"type":        "string",
								"description": "Country code for geolocation",
							},
							"languages": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
								},
								"description": "Language codes for content",
							},
						},
						"description": "Location settings for search",
					},
					"scrapeOptions": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"formats": map[string]interface{}{
								"type": "array",
								"items": map[string]interface{}{
									"type": "string",
									"enum": []string{"markdown", "html", "rawHtml"},
								},
								"description": "Content formats to extract from search results",
							},
							"onlyMainContent": map[string]interface{}{
								"type":        "boolean",
								"description": "Extract only the main content from results",
							},
							"waitFor": map[string]interface{}{
								"type":        "number",
								"description": "Time in milliseconds to wait for dynamic content",
							},
						},
						"description": "Options for scraping search results",
					},
				},
				"required": []string{"query"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_extract",
			"description": "Extract structured information from web pages using LLM. Supports both cloud AI and self-hosted LLM extraction.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"urls": map[string]interface{}{
						"type": "array",
						"items": map[string]interface{}{
							"type": "string",
						},
						"description": "List of URLs to extract information from",
					},
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "Prompt for the LLM extraction",
					},
					"systemPrompt": map[string]interface{}{
						"type":        "string",
						"description": "System prompt for LLM extraction",
					},
					"schema": map[string]interface{}{
						"type":        "object",
						"properties":  map[string]interface{}{},
						"description": "JSON schema for structured data extraction",
					},
					"allowExternalLinks": map[string]interface{}{
						"type":        "boolean",
						"description": "Allow extraction from external links",
					},
					"enableWebSearch": map[string]interface{}{
						"type":        "boolean",
						"description": "Enable web search for additional context",
					},
					"includeSubdomains": map[string]interface{}{
						"type":        "boolean",
						"description": "Include subdomains in extraction",
					},
				},
				"required": []string{"urls"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_deep_research",
			"description": "Conduct deep research on a query using web crawling, search, and AI analysis.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"query": map[string]interface{}{
						"type":        "string",
						"description": "The query to research",
					},
					"maxDepth": map[string]interface{}{
						"type":        "number",
						"description": "Maximum depth of research iterations (1-10)",
					},
					"timeLimit": map[string]interface{}{
						"type":        "number",
						"description": "Time limit in seconds (30-300)",
					},
					"maxUrls": map[string]interface{}{
						"type":        "number",
						"description": "Maximum number of URLs to analyze (1-1000)",
					},
				},
				"required": []string{"query"},
			},
		},
	},
	{
		"type": "function",
		"function": map[string]interface{}{
			"name":        "firecrawl_generate_llmstxt",
			"description": "Generate standardized LLMs.txt file for a given URL, which provides context about how LLMs should interact with the website.",
			"parameters": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"url": map[string]interface{}{
						"type":        "string",
						"description": "The URL to generate LLMs.txt from",
					},
					"maxUrls": map[string]interface{}{
						"type":        "number",
						"description": "Maximum number of URLs to process (1-100, default: 10)",
					},
					"showFullText": map[string]interface{}{
						"type":        "boolean",
						"description": "Whether to show the full LLMs-full.txt in the response",
					},
				},
				"required": []string{"url"},
			},
		},
	},
}
