package utils

import (
	"context"
	"fmt"
	"os"

	"github.com/BurntSushi/toml"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/net/servicer"
)

type testIDC string

func (i testIDC) IDC() string {
	return string(i)
}

type ServerToml struct {
	Name              string
	ConnectionTimeOut int
	WriteTimeOut      int
	ReadTimeOut       int
	Retry             int
	IP                string
	Port              int
}

func DefaultServerToml(name, ip string, port int) ServerToml {
	return ServerToml{
		Name:              name,
		ConnectionTimeOut: 1000,
		WriteTimeOut:      1000,
		ReadTimeOut:       1000,
		Retry:             3,
		IP:                ip,
		Port:              port,
	}
}

func RegisterServer(sts ...ServerToml) (err error) {
	// TestToml cases table
	type TestToml struct {
		Name     string
		TomlConf []byte
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	testsToml := []TestToml{}

	for _, st := range sts {
		testsToml = append(testsToml, TestToml{
			Name: st.Name,
			TomlConf: []byte(fmt.Sprintf(`Name = "%s"
ConnTimeOut = %d
WriteTimeOut = %d
ReadTimeOut = %d
Retry = %d

[Strategy]
Name = "RoundRobin"
[Resource.Manual]
[[Resource.Manual.default]]
Host = "%s"
Port = %d`, st.Name, st.ConnectionTimeOut, st.WriteTimeOut, st.ReadTimeOut, st.Retry, st.IP, st.Port)),
		})
	}

	sm := servicer.NewMapper()
	logger := logit.NewSimple(os.Stdout)

	for _, comp := range testsToml {
		conf := make(map[string]interface{})

		if err = toml.Unmarshal(comp.TomlConf, &conf); err != nil {
			fmt.Println(err)
			return
		}

		srv, err := servicer.NewWithConfig(logger, testIDC("tc"), conf)
		if err != nil {
			fmt.Println(err)
			return err
		}

		if err = sm.AddServicer(comp.Name, srv); err != nil {
			fmt.Println(err)
			return err
		}

		if err = srv.Start(ctx); err != nil {
			fmt.Println(err)
			return err
		}

	}

	servicer.DefaultMapper = sm

	return nil
}
