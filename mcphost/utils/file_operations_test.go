package utils

import (
	"archive/tar"
	"compress/gzip"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// TestingInterface 定义测试接口，兼容testing.T和testing.B
type TestingInterface interface {
	TempDir() string
	Fatalf(format string, args ...interface{})
	Logf(format string, args ...interface{})
}

// 测试用的demo URL（来自注释）
const testDemoURL = "https://dataeng-bos-prod.bj.bcebos.com/mcp/f4662e30-7d76-4529-a413-557f7379f5cd-57456/output_data/env.tar.gz?authorization=bce-auth-v1%2FALTAKwPzBBVWHFtDyVhB2xFLTj%2F2025-06-30T05%3A03%3A45Z%2F-1%2Fhost%2Fb3c5d98261423e25ee68fcf6ea2981ba1e531fdd49e6a5d703e32734d923def8"

func TestDownloadFile(t *testing.T) {
	tests := []struct {
		name        string
		url         string
		expectError bool
		description string
	}{
		{
			name:        "valid_demo_url",
			url:         testDemoURL,
			expectError: false,
			description: "下载真实的demo tar.gz文件",
		},
		{
			name:        "invalid_url",
			url:         "http://invalid-url-that-does-not-exist.com/file.tar.gz",
			expectError: true,
			description: "测试无效的URL",
		},
		{
			name:        "empty_url",
			url:         "",
			expectError: true,
			description: "测试空URL",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建临时文件路径
			tempFile := filepath.Join(t.TempDir(), "test_download.tar.gz")

			// 执行下载
			err := DownloadFile(tt.url, tempFile)

			// 检查结果
			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误发生")
				}
				t.Logf("预期错误: %v", err)
			} else {
				if err != nil {
					t.Errorf("意外错误: %v", err)
					return
				}

				// 验证文件是否存在
				if _, err := os.Stat(tempFile); os.IsNotExist(err) {
					t.Errorf("下载的文件不存在: %s", tempFile)
					return
				}

				// 验证文件大小是否大于0
				info, err := os.Stat(tempFile)
				if err != nil {
					t.Errorf("获取文件信息失败: %v", err)
					return
				}
				if info.Size() == 0 {
					t.Errorf("下载的文件大小为0")
				}

				t.Logf("成功下载文件: %s, 大小: %d bytes", tempFile, info.Size())
			}
		})
	}
}

func TestExtractTarGz(t *testing.T) {
	// 首先创建一个测试用的tar.gz文件
	testTarGz := createTestTarGz(t)
	defer os.Remove(testTarGz)

	tests := []struct {
		name        string
		src         string
		dest        string
		expectError bool
		description string
	}{
		{
			name:        "valid_extraction",
			src:         testTarGz,
			dest:        ".",
			expectError: false,
			description: "正常解压到当前目录",
		},
		{
			name:        "extract_to_subdir",
			src:         testTarGz,
			dest:        "test_subdir",
			expectError: false,
			description: "解压到子目录",
		},
		{
			name:        "nonexistent_source",
			src:         "nonexistent.tar.gz",
			dest:        ".",
			expectError: true,
			description: "源文件不存在",
		},
		{
			name:        "invalid_tar_file",
			src:         createInvalidTarFile(t),
			dest:        ".",
			expectError: true,
			description: "无效的tar文件",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 为每个测试创建独立的临时目录
			tempDir := t.TempDir()
			originalDir, _ := os.Getwd()
			os.Chdir(tempDir)
			defer os.Chdir(originalDir)

			// 如果需要创建子目录，确保它不存在
			if tt.dest != "." {
				os.RemoveAll(tt.dest)
			}

			// 执行解压
			err := ExtractTarGz(tt.src, tt.dest)

			// 检查结果
			if tt.expectError {
				if err == nil {
					t.Errorf("期望出现错误，但没有错误发生")
				}
				t.Logf("预期错误: %v", err)
			} else {
				if err != nil {
					t.Errorf("意外错误: %v", err)
					return
				}

				// 验证解压结果
				expectedFiles := []string{"test_file.txt", "test_dir/nested_file.txt"}
				for _, expectedFile := range expectedFiles {
					fullPath := filepath.Join(tt.dest, expectedFile)
					if _, err := os.Stat(fullPath); os.IsNotExist(err) {
						t.Errorf("期望的文件不存在: %s", fullPath)
					}
				}

				t.Logf("成功解压到: %s", tt.dest)
			}
		})
	}
}

func TestDownloadAndExtractRealDemo(t *testing.T) {
	// 集成测试：使用真实的demo URL下载并解压
	if testing.Short() {
		t.Skip("跳过集成测试，因为使用了-short标志")
	}

	tempDir := t.TempDir()
	tarFile := filepath.Join(tempDir, "demo.tar.gz")

	// 下载demo文件
	t.Logf("正在下载demo文件...")
	err := DownloadFile(testDemoURL, tarFile)
	if err != nil {
		t.Fatalf("下载demo文件失败: %v", err)
	}

	// 验证下载的文件
	info, err := os.Stat(tarFile)
	if err != nil {
		t.Fatalf("获取下载文件信息失败: %v", err)
	}
	t.Logf("成功下载demo文件，大小: %d bytes", info.Size())

	// 解压文件
	extractDir := filepath.Join(tempDir, "extracted")
	os.MkdirAll(extractDir, 0755)

	t.Logf("正在解压demo文件...")
	err = ExtractTarGz(tarFile, extractDir)
	if err != nil {
		t.Fatalf("解压demo文件失败: %v", err)
	}

	// 验证解压结果
	entries, err := os.ReadDir(extractDir)
	if err != nil {
		t.Fatalf("读取解压目录失败: %v", err)
	}

	t.Logf("解压完成，发现 %d 个文件/目录:", len(entries))
	for _, entry := range entries {
		t.Logf("  - %s (目录: %v)", entry.Name(), entry.IsDir())
	}
}

// 创建测试用的tar.gz文件
func createTestTarGz(tb TestingInterface) string {
	tempFile := filepath.Join(tb.TempDir(), "test.tar.gz")

	file, err := os.Create(tempFile)
	if err != nil {
		tb.Fatalf("创建测试tar文件失败: %v", err)
	}
	defer file.Close()

	// 创建gzip写入器
	gzw := gzip.NewWriter(file)
	defer gzw.Close()

	// 创建tar写入器
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	// 添加测试文件
	testFiles := map[string]string{
		"test_file.txt":            "这是一个测试文件\n",
		"test_dir/nested_file.txt": "这是嵌套目录中的文件\n",
	}

	for name, content := range testFiles {
		// 如果是嵌套文件，先添加目录
		if strings.Contains(name, "/") {
			dirName := filepath.Dir(name)
			dirHeader := &tar.Header{
				Name:     dirName + "/",
				Typeflag: tar.TypeDir,
				Mode:     0755,
				ModTime:  time.Now(),
			}
			if err := tw.WriteHeader(dirHeader); err != nil {
				tb.Fatalf("写入目录头失败: %v", err)
			}
		}

		// 添加文件
		header := &tar.Header{
			Name:     name,
			Typeflag: tar.TypeReg,
			Size:     int64(len(content)),
			Mode:     0644,
			ModTime:  time.Now(),
		}
		if err := tw.WriteHeader(header); err != nil {
			tb.Fatalf("写入文件头失败: %v", err)
		}
		if _, err := tw.Write([]byte(content)); err != nil {
			tb.Fatalf("写入文件内容失败: %v", err)
		}
	}

	return tempFile
}

// 创建无效的tar文件
func createInvalidTarFile(t *testing.T) string {
	tempFile := filepath.Join(t.TempDir(), "invalid.tar.gz")

	// 写入一些无效数据
	err := os.WriteFile(tempFile, []byte("这不是一个有效的tar.gz文件"), 0644)
	if err != nil {
		t.Fatalf("创建无效tar文件失败: %v", err)
	}

	return tempFile
}

func TestExtractTarGzSecurityCheck(t *testing.T) {
	// 测试安全检查：防止路径遍历攻击
	maliciousTarGz := createMaliciousTarGz(t)
	defer os.Remove(maliciousTarGz)

	tempDir := t.TempDir()

	// 尝试解压恶意tar文件
	err := ExtractTarGz(maliciousTarGz, tempDir)
	if err == nil {
		t.Errorf("期望安全检查失败，但解压成功了")
	}

	if !strings.Contains(err.Error(), "不安全的路径") {
		t.Errorf("期望包含'不安全的路径'错误信息，但得到: %v", err)
	}

	t.Logf("安全检查正常工作: %v", err)
}

// 创建包含路径遍历攻击的恶意tar.gz文件
func createMaliciousTarGz(t *testing.T) string {
	tempFile := filepath.Join(t.TempDir(), "malicious.tar.gz")

	file, err := os.Create(tempFile)
	if err != nil {
		t.Fatalf("创建恶意tar文件失败: %v", err)
	}
	defer file.Close()

	// 创建gzip写入器
	gzw := gzip.NewWriter(file)
	defer gzw.Close()

	// 创建tar写入器
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	// 添加包含路径遍历的恶意文件
	maliciousContent := "恶意内容"
	header := &tar.Header{
		Name:     "../../../tmp/malicious_file.txt", // 尝试写入父目录
		Typeflag: tar.TypeReg,
		Size:     int64(len(maliciousContent)),
		Mode:     0644,
		ModTime:  time.Now(),
	}
	if err := tw.WriteHeader(header); err != nil {
		t.Fatalf("写入恶意文件头失败: %v", err)
	}
	if _, err := tw.Write([]byte(maliciousContent)); err != nil {
		t.Fatalf("写入恶意文件内容失败: %v", err)
	}

	return tempFile
}

func TestAll(t *testing.T) {
	tempDir := "./"
	tarFile := filepath.Join(tempDir, "demo.tar.gz")

	// 下载demo文件
	t.Logf("正在下载demo文件...")
	err := DownloadFile(testDemoURL, tarFile)
	if err != nil {
		t.Fatalf("下载demo文件失败: %v", err)
	}

	err = ExtractTarGz(tarFile, ".")
	if err != nil {
		t.Fatalf("解压demo文件失败: %v", err)
	}

}
