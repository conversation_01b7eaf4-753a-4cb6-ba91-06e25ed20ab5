package utils

import (
	"archive/tar"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/charmbracelet/log"
)

// DownloadFile 从URL下载文件到本地
func DownloadFile(url, filepath string) error {
	// 创建HTTP客户端，设置超时
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 发起GET请求
	resp, err := client.Get(url)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	// 创建本地文件
	out, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %v", err)
	}
	defer out.Close()

	// 复制响应内容到本地文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("下载文件内容失败: %v", err)
	}

	return nil
}

// ExtractTarGz 解压tar.gz文件到指定目录
func ExtractTarGz(src, dest string) error {
	// 打开tar.gz文件
	file, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("打开tar.gz文件失败: %v", err)
	}
	defer file.Close()

	// 创建gzip读取器
	gzr, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("创建gzip读取器失败: %v", err)
	}
	defer gzr.Close()

	// 创建tar读取器
	tr := tar.NewReader(gzr)

	// 获取目标目录的绝对路径用于安全检查
	absDest, err := filepath.Abs(dest)
	if err != nil {
		return fmt.Errorf("获取目标目录绝对路径失败: %v", err)
	}

	// 逐个处理tar中的文件
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break // 文件结束
		}
		if err != nil {
			return fmt.Errorf("读取tar文件失败: %v", err)
		}

		// 构建目标路径
		target := filepath.Join(dest, header.Name)

		// 获取目标文件的绝对路径
		absTarget, err := filepath.Abs(target)
		if err != nil {
			return fmt.Errorf("获取目标文件绝对路径失败: %v", err)
		}

		// 安全检查：确保目标路径在目标目录内
		// 特殊处理：如果目标文件就是目标目录本身（如 "." 条目），则允许
		if absTarget != absDest && !strings.HasPrefix(absTarget, absDest+string(os.PathSeparator)) {
			return fmt.Errorf("不安全的路径: %s", target)
		}

		// 根据文件类型处理
		switch header.Typeflag {
		case tar.TypeDir:
			// 创建目录
			if err := os.MkdirAll(target, os.FileMode(header.Mode)); err != nil {
				return fmt.Errorf("创建目录失败: %v", err)
			}
		case tar.TypeReg:
			// 创建文件
			// 确保父目录存在
			if err := os.MkdirAll(filepath.Dir(target), 0755); err != nil {
				return fmt.Errorf("创建文件父目录失败: %v", err)
			}

			// 创建文件
			outFile, err := os.OpenFile(target, os.O_CREATE|os.O_RDWR, os.FileMode(header.Mode))
			if err != nil {
				return fmt.Errorf("创建文件失败: %v", err)
			}

			// 复制文件内容
			if _, err := io.Copy(outFile, tr); err != nil {
				outFile.Close()
				return fmt.Errorf("复制文件内容失败: %v", err)
			}
			outFile.Close()
		default:
			log.Warn("跳过不支持的文件类型", "type", header.Typeflag, "name", header.Name)
		}
	}

	return nil
}
