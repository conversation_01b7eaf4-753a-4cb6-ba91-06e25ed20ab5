package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/log"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

// ToolCallRecorder 定义能够记录工具调用的接口
type ToolCallRecorder interface {
	AddToolCall(name, arguments, content string, timeCostMs, step int64, messageContent string, thoughts string)
}

// ToolCallResult 工具调用的结果
type ToolCallResult struct {
	Success    bool          `json:"success"`
	Content    string        `json:"content"`
	Duration   time.Duration `json:"duration"`
	Error      error         `json:"error,omitempty"`
	MCPContent []mcp.Content `json:"mcp_content,omitempty"` // 原始MCP内容
}

// ToolCallInput 工具调用的输入参数
type ToolCallInput struct {
	ToolName  string                 `json:"tool_name"` // 完整工具名称，格式: server__tool
	Arguments map[string]interface{} `json:"arguments"` // 工具参数
	ID        string                 `json:"id"`        // 工具调用ID（用于tracking）
}

// ExecuteToolCall 执行单个MCP工具调用
func ExecuteToolCall(
	ctx context.Context,
	input ToolCallInput,
	mcpClients map[string]client.MCPClient,
) ToolCallResult {
	startTime := time.Now()

	// 解析工具名称
	parts := strings.Split(input.ToolName, "__")
	if len(parts) != 2 {
		errMsg := fmt.Sprintf("Invalid tool name format: %s", input.ToolName)
		log.Error("工具名称格式错误", "tool_name", input.ToolName, "error", errMsg)
		return ToolCallResult{
			Success:  false,
			Content:  errMsg,
			Duration: time.Since(startTime),
			Error:    fmt.Errorf(errMsg),
		}
	}

	serverName, actualToolName := parts[0], parts[1]

	// 检查并获取MCP客户端
	mcpClient, ok := mcpClients[serverName]
	if !ok {
		errMsg := fmt.Sprintf("Server not found: %s", serverName)
		log.Error("MCP服务器未找到", "server_name", serverName, "error", errMsg)
		return ToolCallResult{
			Success:  false,
			Content:  errMsg,
			Duration: time.Since(startTime),
			Error:    fmt.Errorf(errMsg),
		}
	}

	// 构建MCP工具调用请求
	req := mcp.CallToolRequest{}
	req.Params.Name = actualToolName
	req.Params.Arguments = input.Arguments

	log.Debug("执行MCP工具调用",
		"server_name", serverName,
		"tool_name", actualToolName,
		"arguments", input.Arguments)

	// 调用MCP工具
	toolResult, err := mcpClient.CallTool(ctx, req)
	duration := time.Since(startTime)

	if err != nil {
		errMsg := fmt.Sprintf("Error calling tool %s: %v", actualToolName, err)
		log.Error("工具调用失败",
			"server_name", serverName,
			"tool_name", actualToolName,
			"error", err,
			"duration_ms", duration.Milliseconds())
		return ToolCallResult{
			Success:  false,
			Content:  errMsg,
			Duration: duration,
			Error:    err,
		}
	}

	// 处理工具调用结果
	if toolResult.Content == nil {
		log.Warn("工具调用返回空内容", "server_name", serverName, "tool_name", actualToolName)
		return ToolCallResult{
			Success:    true,
			Content:    "Tool executed but returned no content",
			Duration:   duration,
			MCPContent: []mcp.Content{},
		}
	}

	// 提取结果文本
	var resultTexts []string
	for _, item := range toolResult.Content {
		if contentMap, ok := item.(mcp.TextContent); ok {
			resultTexts = append(resultTexts, contentMap.Text)
		}
	}

	if len(resultTexts) == 0 {
		return ToolCallResult{
			Success:    true,
			Content:    "Tool executed but returned no text content",
			Duration:   duration,
			MCPContent: toolResult.Content,
		}
	}

	// 返回结果（合并所有文本内容）
	resultText := strings.Join(resultTexts, " ")
	log.Info("工具调用成功",
		"server_name", serverName,
		"tool_name", actualToolName,
		"result_length", len(resultText),
		"duration_ms", duration.Milliseconds(),
		"result_content", resultText)

	return ToolCallResult{
		Success:    true,
		Content:    resultText,
		Duration:   duration,
		MCPContent: toolResult.Content,
	}
}

// ExecuteToolCallWithRecording 执行工具调用并记录结果
func ExecuteToolCallWithRecording(
	ctx context.Context,
	input ToolCallInput,
	mcpClients map[string]client.MCPClient,
	recorder ToolCallRecorder,
	step int64,
	messageContent string,
	thoughts string,
) ToolCallResult {
	// 序列化参数用于记录
	argumentsBytes, _ := json.Marshal(input.Arguments)
	argumentsStr := string(argumentsBytes)

	// 执行工具调用
	result := ExecuteToolCall(ctx, input, mcpClients)

	// 记录工具调用结果
	if recorder != nil {
		recorder.AddToolCall(
			input.ToolName,
			argumentsStr,
			result.Content,
			result.Duration.Milliseconds(),
			step,
			messageContent,
			thoughts,
		)
		log.Debug("工具调用已记录",
			"tool_name", input.ToolName,
			"duration_ms", result.Duration.Milliseconds())
	}

	return result
}

// FormatToolCallResultAsJSON 将工具调用结果格式化为JSON字符串（用于assistant_api）
func FormatToolCallResultAsJSON(result ToolCallResult) string {
	if !result.Success {
		return fmt.Sprintf(`{"error": "%s"}`, result.Content)
	}

	// 将结果包装为JSON格式
	resultJSON := map[string]interface{}{
		"result": result.Content,
	}

	jsonBytes, err := json.Marshal(resultJSON)
	if err != nil {
		log.Error("结果序列化失败", "error", err)
		return fmt.Sprintf(`{"result": "%s"}`, result.Content) // fallback
	}

	return string(jsonBytes)
}

// ParseToolCallInput 从字符串参数解析工具调用输入
func ParseToolCallInput(toolName, argumentsStr, id string) (ToolCallInput, error) {
	log.Info("🔧 开始执行工具",
		"tool_name", toolName,
		"tool_id", id,
		"arguments", argumentsStr)

	var args map[string]interface{}
	if err := json.Unmarshal([]byte(argumentsStr), &args); err != nil {
		return ToolCallInput{}, fmt.Errorf("error parsing tool arguments: %w", err)
	}

	return ToolCallInput{
		ToolName:  toolName,
		Arguments: args,
		ID:        id,
	}, nil
}
