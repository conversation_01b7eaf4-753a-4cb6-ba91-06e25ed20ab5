package utils

import (
	"encoding/json"
	"regexp"
	"strconv"
	"unicode/utf8"
)

// DecodeUnicodeEscapes 解码字符串中的unicode转义序列
// 例如: "\u8bf7\u6c42\u5931\u8d25" -> "请求失败"
func DecodeUnicodeEscapes(s string) string {
	// 匹配 \uXXXX 格式的unicode转义序列
	re := regexp.MustCompile(`\\u([0-9a-fA-F]{4})`)

	return re.ReplaceAllStringFunc(s, func(match string) string {
		// 提取十六进制码点
		hexStr := match[2:] // 去掉 \u 前缀
		codePoint, err := strconv.ParseInt(hexStr, 16, 32)
		if err != nil {
			return match // 解析失败，返回原字符串
		}

		// 转换为对应的UTF-8字符
		r := rune(codePoint)
		if utf8.ValidRune(r) {
			return string(r)
		}
		return match // 无效的unicode码点，返回原字符串
	})
}

// TryDecodeResponse 尝试解码响应体中的unicode转义序列
// 优先尝试作为JSON字符串解码，失败后尝试直接解码unicode转义序列
func TryDecodeResponse(data []byte) string {
	// 首先尝试作为JSON字符串解码
	var jsonStr string
	if err := json.Unmarshal(data, &jsonStr); err == nil {
		return jsonStr
	}

	// JSON解码失败，尝试直接解码unicode转义序列
	return DecodeUnicodeEscapes(string(data))
}
