package cmd

import (
	"encoding/json"
	"testing"
)

func TestEnvironmentDependencyUnmarshaling(t *testing.T) {
	tests := []struct {
		name     string
		jsonStr  string
		expected int // 期望的数组长度，-1表示期望为nil
	}{
		{
			name:     "空字符串",
			jsonStr:  `{"extra_info": {"environment_dependency": ""}}`,
			expected: -1,
		},
		{
			name:     "null值",
			jsonStr:  `{"extra_info": {"environment_dependency": null}}`,
			expected: -1,
		},
		{
			name:     "空数组",
			jsonStr:  `{"extra_info": {"environment_dependency": []}}`,
			expected: -1,
		},
		{
			name:     "有效数组",
			jsonStr:  `{"extra_info": {"environment_dependency": [{"path": "./test.db", "type": "database", "content": "CREATE TABLE test (id INTEGER);"}]}}`,
			expected: 1,
		},
		{
			name:     "多个依赖项",
			jsonStr:  `{"extra_info": {"environment_dependency": [{"path": "./test1.db", "type": "database", "content": "SQL1"}, {"path": "./test2.db", "type": "database", "content": "SQL2"}]}}`,
			expected: 2,
		},
		{
			name:     "没有该字段",
			jsonStr:  `{"extra_info": {}}`,
			expected: -1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var config BenchConfig
			err := json.Unmarshal([]byte(tt.jsonStr), &config)

			if err != nil {
				t.Fatalf("JSON反序列化失败: %v", err)
			}

			envDeps := config.ExtraInfo.EnvironmentDependency

			if tt.expected == -1 {
				// 期望为nil
				if envDeps != nil {
					t.Errorf("期望environment_dependency为nil，但得到长度为%d的数组", len(envDeps))
				}
			} else {
				// 期望特定长度
				if envDeps == nil {
					t.Errorf("期望environment_dependency长度为%d，但得到nil", tt.expected)
				} else if len(envDeps) != tt.expected {
					t.Errorf("期望environment_dependency长度为%d，但得到%d", tt.expected, len(envDeps))
				}
			}
		})
	}
}

func TestCheckListUnmarshaling(t *testing.T) {
	tests := []struct {
		name     string
		jsonStr  string
		expected int // 期望的数组长度，-1表示期望为nil
	}{
		{
			name:     "空字符串",
			jsonStr:  `{"extra_info": {"check_list": ""}}`,
			expected: -1,
		},
		{
			name:     "null值",
			jsonStr:  `{"extra_info": {"check_list": null}}`,
			expected: -1,
		},
		{
			name:     "空数组",
			jsonStr:  `{"extra_info": {"check_list": []}}`,
			expected: -1,
		},
		{
			name:     "有效数组",
			jsonStr:  `{"extra_info": {"check_list": [{"type": "check_file_exist", "target": "./test.txt"}]}}`,
			expected: 1,
		},
		{
			name:     "没有该字段",
			jsonStr:  `{"extra_info": {}}`,
			expected: -1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var config BenchConfig
			err := json.Unmarshal([]byte(tt.jsonStr), &config)

			if err != nil {
				t.Fatalf("JSON反序列化失败: %v", err)
			}

			checkList := config.ExtraInfo.CheckList

			if tt.expected == -1 {
				// 期望为nil
				if checkList != nil {
					t.Errorf("期望check_list为nil，但得到长度为%d的数组", len(checkList))
				}
			} else {
				// 期望特定长度
				if checkList == nil {
					t.Errorf("期望check_list长度为%d，但得到nil", tt.expected)
				} else if len(checkList) != tt.expected {
					t.Errorf("期望check_list长度为%d，但得到%d", tt.expected, len(checkList))
				}
			}
		})
	}
}
