package cmd

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/charmbracelet/log"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcphost/pkg/environment"
	"github.com/mark3labs/mcphost/pkg/history"
	"github.com/mark3labs/mcphost/pkg/llm"
	"github.com/mark3labs/mcphost/pkg/llm/assistant_api"
	"github.com/mark3labs/mcphost/pkg/llm/openai"
	"github.com/mark3labs/mcphost/utils"
	"github.com/spf13/cobra"
)

var (
	configFile             string
	mcpServersFile         string
	benchFile              string
	outputPath             string
	messageWindow          int
	openaiBaseURL          string // Base URL for OpenAI API
	openaiAPIKey           string
	maxToolExecutionRounds int
)

const (

	// initialBackoff = 5 * time.Second
	// maxBackoff     = 60 * time.Second
	// maxRetries     = 30 // Will reach close to max backoff
	DefaultMaxToolExecutionRounds = 30
)

var rootCmd = &cobra.Command{
	Use:   "mcphost",
	Short: "Chat with AI models through a unified interface",
	RunE: func(cmd *cobra.Command, args []string) error {
		return runMCPHost(context.Background())
	},
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func init() {

	rootCmd.PersistentFlags().
		StringVar(&configFile, "config", "", "模型配置文件路径")
	rootCmd.PersistentFlags().
		StringVar(&mcpServersFile, "mcpservers", "", "MCP服务器配置文件路径")
	rootCmd.PersistentFlags().
		StringVar(&benchFile, "bench", "", "测试配置文件路径")
	rootCmd.PersistentFlags().
		StringVarP(&outputPath, "output", "o", "", "输出结果文件路径 (默认: ./)")
	rootCmd.PersistentFlags().
		IntVar(&messageWindow, "message-window", 10, "number of messages to keep in context")
}

// 从模型配置创建提供者
func createProviderFromModelConfig(ctx context.Context, config *ModelConfig, systemPrompt string) (llm.Provider, error) {
	if config.ModelProvider == "" || config.ModelName == "" {
		return nil, fmt.Errorf("模型配置不完整：缺少 model_provider 或 model_name")
	}

	provider := config.ModelProvider
	model := config.ModelName
	maxToolExecutionRounds = config.MaxToolExecutionRounds
	if maxToolExecutionRounds == 0 {
		maxToolExecutionRounds = DefaultMaxToolExecutionRounds
	}

	switch provider {
	case "openai":
		apiKey := config.OpenAIAPIKey
		if apiKey == "" {
			apiKey = openaiAPIKey
		}
		if apiKey == "" {
			apiKey = os.Getenv("OPENAI_API_KEY")
		}

		if apiKey == "" {
			return nil, fmt.Errorf(
				"openAI API key not provided. Use --openai-api-key flag or OPENAI_API_KEY environment variable",
			)
		}

		baseURL := config.OpenAIURL
		if baseURL == "" {
			baseURL = openaiBaseURL
		}
		return openai.NewProvider(apiKey, baseURL, model, systemPrompt), nil
	case "assistant_api":
		// assistant_api需要特殊的配置参数
		// 根据不同的backend创建不同的client
		backend := config.Backend
		if backend == "" {
			backend = "qianfan" // 默认使用千帆
		}
		// 服务器注册现在在NewProvider中处理
		assistantID := config.AssistantID
		// 对于yiyan backend，如果没有提供assistantID，将在provider中自动创建
		if assistantID == "" && backend != "yiyan" {
			return nil, fmt.Errorf("assistant_api需要配置assistant_id（yiyan backend除外）")
		}

		authorization := config.Authorization
		if authorization == "" {
			return nil, fmt.Errorf("assistant_api需要配置authorization")
		}

		appID := config.AppID
		return assistant_api.NewProvider(
			authorization,
			assistantID,
			model,
			backend,
			appID,
			systemPrompt,
			config.AssistantServerIP,
			config.AssistantServerPort,
			config.GlobalTimeout,
		)

	default:
		return nil, fmt.Errorf("不支持的模型提供者: %s", provider)
	}
}

func runMCPHost(ctx context.Context) error {
	log.SetLevel(log.InfoLevel)
	log.SetReportCaller(false)

	// 检查是否使用了三个配置文件参数，如果是则进入基准测试模式
	if configFile != "" && mcpServersFile != "" && benchFile != "" {
		return runBenchMode(ctx)
	} else {
		return errors.New("no config file provided")
	}
}

// 新的基准测试模式：使用三个分离的配置文件
func runBenchMode(ctx context.Context) error {
	log.Info("启动基准测试模式",
		"config", configFile,
		"mcpservers", mcpServersFile,
		"bench", benchFile)

	// 加载bench配置文件
	benchConfig, err := loadBenchConfig()
	if err != nil {
		return fmt.Errorf("加载测试配置失败: %v", err)
	}
	if benchConfig.BenchResult != nil && benchConfig.BenchEnvPathUrl != "" {
		// 不为空说明之前已经完成了mcp基准执行，恢复环境后即可结束，让 boost.sh 继续执行 检查
		// 恢复环境
		err := restoreEnv(benchConfig.BenchEnvPathUrl, benchConfig.BenchResult)
		if err != nil {
			return fmt.Errorf("恢复环境失败: %v", err)
		}
		return nil
	}

	// 初始化结果记录
	var benchResult BenchResult
	benchResult.ToolCallList = make([]ToolCallRecord, 0)
	benchResult.CheckList = make([]CheckResult, 0)
	benchResult.AvailableTools = make([]ToolInfo, 0)
	startTime := time.Now()

	// 从bench配置获取系统提示词
	systemPrompt := benchConfig.GetSystemPrompt()
	log.Info("使用系统提示词", "prompt", systemPrompt)

	// 使用命令行参数指定的模型配置，或者默认配置
	var provider llm.Provider
	modelConfig, err := loadModelConfig()
	if err != nil {
		return fmt.Errorf("加载模型配置失败: %v", err)
	}
	provider, err = createProviderFromModelConfig(ctx, modelConfig, systemPrompt)
	if err != nil {
		return fmt.Errorf("创建模型提供者失败: %v", err)
	}

	// 获取模型详细信息用于日志记录
	var modelName string
	if configFile != "" {
		// 如果有模型配置文件，使用配置文件中的信息
		if modelConfig, err := loadModelConfig(); err == nil {
			modelName = modelConfig.ModelName
		} else {
			modelName = "config_error"
		}
	}
	log.Info("模型已加载",
		"provider", provider.Name(),
		"model", modelName,
		"system_prompt_length", len(systemPrompt))

	// 初始化环境依赖
	if len(benchConfig.ExtraInfo.EnvironmentDependency) > 0 {
		// 创建环境管理器
		envManager := environment.DefaultManagerWithLogger(log.Default())

		// 转换数据结构
		var envDeps []environment.EnvironmentDependency
		for _, dep := range benchConfig.ExtraInfo.EnvironmentDependency {
			envDeps = append(envDeps, environment.EnvironmentDependency{
				Path:    dep.Path,
				Type:    dep.Type,
				Content: dep.Content,
			})
		}

		// 执行环境初始化
		results, err := envManager.Initialize(ctx, envDeps)
		if err != nil {
			return fmt.Errorf("初始化环境依赖失败: %v", err)
		}

		// 检查是否有失败的初始化
		var failedResults []environment.InitializationResult
		for _, result := range results {
			if !result.Success {
				failedResults = append(failedResults, result)
			} else {
				if result.Type == "database" || result.Type == "db" {
					// 更新 mcpservers.json 中的数据库路径
					err := updateMCPServersDBPath(result.Path)
					if err != nil {
						log.Warn("更新MCP服务器数据库路径失败",
							"db_path", result.Path,
							"error", err)
					} else {
						log.Info("已更新MCP服务器数据库路径",
							"new_db_path", result.Path)
					}
				}
			}
		}

		if len(failedResults) > 0 {
			log.Warn("部分环境依赖初始化失败", "failed_count", len(failedResults))
			for _, failed := range failedResults {
				log.Error("环境依赖失败详情",
					"path", failed.Path,
					"type", failed.Type,
					"error", failed.ErrorMessage)
			}
			return fmt.Errorf("有 %d 个环境依赖初始化失败", len(failedResults))
		}
	}

	// 创建 MCP 客户端
	var mcpClients map[string]client.MCPClient
	if mcpServersFile != "" {
		mcpConfig, err := loadMCPServersConfig()
		if err != nil {
			return fmt.Errorf("加载MCP服务器配置失败: %v", err)
		}
		mcpClients, err = createMCPClients(mcpConfig)
		if err != nil {
			return fmt.Errorf("创建 MCP 客户端失败: %v", err)
		}
	} else {
		// 如果没有指定MCP服务器配置，使用空的map
		mcpClients = make(map[string]client.MCPClient)
	}

	defer func() {
		if len(mcpClients) > 0 {
			log.Info("关闭 MCP 服务器...")
			for name, client := range mcpClients {
				if err := client.Close(); err != nil {
					log.Error("关闭服务器失败", "name", name, "error", err)
				} else {
					log.Info("服务器已关闭", "name", name)
				}
			}
		}
	}()

	// 收集可用工具信息并填充到benchResult中
	benchResult.AvailableTools = collectToolInfoFromMCPClients(ctx, mcpClients)
	log.Info("可用工具信息已收集", "total_tools", len(benchResult.AvailableTools))

	// 获取所有工具
	var allTools []llm.Tool
	for serverName, mcpClient := range mcpClients {
		ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
		toolsResult, err := mcpClient.ListTools(ctx, mcp.ListToolsRequest{})
		cancel()

		if err != nil {
			log.Error("获取工具失败", "server", serverName, "error", err)
			continue
		}

		serverTools := mcpToolsToAnthropicTools(serverName, toolsResult.Tools)
		allTools = append(allTools, serverTools...)

		log.Info("工具已加载", "server", serverName, "count", len(toolsResult.Tools))
	}

	// 执行查询
	query := benchConfig.GetQuery()
	if query == "" {
		return fmt.Errorf("测试配置中缺少查询内容")
	}

	messages := make([]history.HistoryMessage, 0)

	log.Info("", "query", query)

	// 根据provider类型选择不同的处理方式
	var lastMessage llm.Message
	if assistantProvider, ok := provider.(*assistant_api.Provider); ok {
		// 使用assistant_api专门的处理函数
		err = runAssistantAPIWithRecording(ctx, assistantProvider, mcpClients, allTools, query, &messages, &benchResult, maxToolExecutionRounds)
	} else {
		// 使用通用的工具调用记录函数
		lastMessage, err = runPromptWithToolRecording(ctx, provider, mcpClients, allTools, query, &messages, &benchResult, 1, maxToolExecutionRounds)
	}

	if err != nil {
		log.Error("执行查询失败", "error", err)
		// 记录错误信息到结果中
		if strings.Contains(err.Error(), "已达到最大工具执行轮数限制") {
			benchResult.Response = fmt.Sprintf("%v", err)
		} else {
			benchResult.Response = fmt.Sprintf("错误: %v", err)
		}
	} else {
		// 记录最后一条消息作为响应
		if lastMessage != nil {
			benchResult.Response = lastMessage.GetContent()
			// 记录reasoning内容到ResponseThoughts字段
			if reasoningContent := lastMessage.GetReasoningContent(); reasoningContent != "" {
				benchResult.ResponseThoughts = reasoningContent
			}
		} else if len(messages) > 0 {
			// 如果没有原始消息，回退到历史消息
			lastHistoryMessage := messages[len(messages)-1]
			benchResult.Response = lastHistoryMessage.GetContent()
		}
	}

	// 执行检查列表（即使API调用失败也要执行检查）
	log.Info("=== 执行检查列表 ===")

	// 创建检查器管理器
	// checkerManager := check.NewCheckerManager(provider)
	// 使用新格式的检查列表
	if len(benchConfig.ExtraInfo.CheckList) > 0 {
		// for _, _ := range benchConfig.ExtraInfo.CheckList {
		// TODO
		// result := checkerManager.Check(ctx, item)
		// }
	}

	// 计算总计时和步数
	benchResult.TotalTimeCostMs = time.Since(startTime).Milliseconds()
	if len(benchResult.ToolCallList) > 0 {
		benchResult.TotalSteps = int(benchResult.ToolCallList[len(benchResult.ToolCallList)-1].Step)
	}
	// 输出结果到JSON文件
	resultJSON, err2 := json.MarshalIndent(benchResult, "", "    ")
	if err2 != nil {
		return fmt.Errorf("序列化结果失败: %v", err2)
	}

	// 输出到控制台
	fmt.Println(string(resultJSON))

	// 确定输出文件路径
	resultFile := outputPath + "/result.json"
	envPathTarFile := outputPath + "/env.tar.gz"

	// 创建输出文件的目录（如果需要）
	if dir := filepath.Dir(resultFile); dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Warn("创建输出目录失败", "dir", dir, "error", err)
		} else {
			log.Debug("创建输出目录", "dir", dir)
		}
	}

	// 同时保存到文件
	if err := os.WriteFile(resultFile, resultJSON, 0644); err != nil {
		log.Warn("保存结果文件失败", "error", err)
	} else {
		log.Info("结果已保存", "file", resultFile)
	}
	// 保存环境目录信息到tar文件
	if err := saveEnvPathToTar(envPathTarFile); err != nil {
		log.Warn("保存环境目录信息失败", "error", err)
	} else {
		log.Info("环境目录信息已保存", "file", envPathTarFile)
	}

	log.Info("基准测试完成")

	// 如果原始API调用失败，最后返回原始错误（但文件已保存）
	if err != nil {
		return fmt.Errorf("执行查询失败: %v", err)
	}

	return nil
}

func restoreEnv(envPathTarUrl string, benchResult map[string]any) error {
	log.Info("开始恢复环境", "url", envPathTarUrl)

	// 1. 下载环境目录信息到tar文件
	tempTarFile := "temp_env.tar.gz"
	err := utils.DownloadFile(envPathTarUrl, tempTarFile)
	if err != nil {
		return fmt.Errorf("下载环境文件失败: %v", err)
	}
	defer os.Remove(tempTarFile) // 清理临时文件

	log.Info("环境文件下载完成", "file", tempTarFile)

	// 2. 解压tar文件到当前目录
	err = utils.ExtractTarGz(tempTarFile, ".")
	if err != nil {
		return fmt.Errorf("解压环境文件失败: %v", err)
	}

	log.Info("环境文件解压完成")

	// 3. 输出BenchResult到result.json
	resultJSON, err := json.MarshalIndent(benchResult, "", "    ")
	if err != nil {
		return fmt.Errorf("序列化结果失败: %v", err)
	}

	// 确定输出文件路径
	resultFile := outputPath + "/result.json"

	// 创建输出文件的目录（如果需要）
	if dir := filepath.Dir(resultFile); dir != "." && dir != "" {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Warn("创建输出目录失败", "dir", dir, "error", err)
		} else {
			log.Debug("创建输出目录", "dir", dir)
		}
	}

	// 保存结果到文件
	if err := os.WriteFile(resultFile, resultJSON, 0644); err != nil {
		return fmt.Errorf("保存结果文件失败: %v", err)
	}

	log.Info("环境恢复完成", "result_file", resultFile)
	return nil
}

// 保存环境目录信息到tar文件
func saveEnvPathToTar(envPathTarFilePath string) error {
	// 获取当前工作目录（源目录）
	srcDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("获取当前工作目录失败: %v", err)
	}

	// 获取目标文件的绝对路径
	absTargetPath, err := filepath.Abs(envPathTarFilePath)
	if err != nil {
		return fmt.Errorf("获取目标文件绝对路径失败: %v", err)
	}

	// 确保目标文件的目录存在
	targetDir := filepath.Dir(absTargetPath)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return fmt.Errorf("创建目标目录失败: %v", err)
	}

	// 排除 mcphost 和 boost.sh
	excludeFiles := []string{"mcphost", "boost.sh"}
	// 构建命令参数
	args := []string{"-czf", absTargetPath}
	for _, file := range excludeFiles {
		args = append(args, "--exclude="+file)
	}
	args = append(args, "-C", srcDir, ".")

	// 执行命令
	cmd := exec.Command("tar", args...)

	// 执行命令
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("执行tar命令失败: %v, 输出: %s", err, string(output))
	}

	log.Debug("tar命令执行成功", "source", srcDir, "target", absTargetPath)
	return nil
}

// 带工具调用记录的运行提示函数
func runPromptWithToolRecording(
	ctx context.Context,
	provider llm.Provider,
	mcpClients map[string]client.MCPClient,
	tools []llm.Tool,
	prompt string,
	messages *[]history.HistoryMessage,
	benchResult *BenchResult,
	step int64,
	maxRounds int,
) (llm.Message, error) {
	// 检查是否超过最大轮数
	if step > int64(maxRounds) {
		log.Warn("已达到最大工具执行轮数限制",
			"current_step", step,
			"max_rounds", maxRounds)
		return nil, fmt.Errorf("已达到最大工具执行轮数限制: %d", maxRounds)
	}
	// Display the user's prompt if it's not empty (i.e., not a tool response)
	if prompt != "" {
		*messages = append(
			*messages,
			history.HistoryMessage{
				Role: "user",
				Content: []history.ContentBlock{{
					Type: "text",
					Text: prompt,
				}},
			},
		)
	}

	var message llm.Message
	var err error
	// backoff := initialBackoff
	retries := 0

	// Convert MessageParam to llm.Message for provider
	llmMessages := make([]llm.Message, len(*messages))
	for i := range *messages {
		llmMessages[i] = &(*messages)[i]
	}

	for {
		// 记录LLM调用输入信息
		log.Info("=== LLM调用开始 ===",
			"message_count", len(llmMessages),
			"tool_count", len(tools),
			"attempt", retries+1)

		callStartTime := time.Now()
		action := func() {
			message, err = provider.CreateMessage(
				ctx,
				prompt,
				llmMessages,
				tools,
			)
		}
		// 显示思考状态
		log.Info("💭 正在思考中...")

		// Execute the action
		action()
		callDuration := time.Since(callStartTime)

		// 记录LLM调用结果
		if err != nil {
			log.Error("LLM调用失败",
				"provider", provider.Name(),
				"error", err.Error(),
				"duration_ms", callDuration.Milliseconds(),
				"attempt", retries+1)
		} else {
			// 获取使用统计
			inputTokens, outputTokens := message.GetUsage()
			benchResult.PromptTokens += inputTokens
			benchResult.CompletionTokens += outputTokens
			benchResult.TotalTokens += inputTokens + outputTokens

			log.Info("=== LLM调用成功 ===",
				"provider", provider.Name(),
				"duration_ms", callDuration.Milliseconds(),
				"input_tokens", inputTokens,
				"output_tokens", outputTokens,
				"total_tokens", inputTokens+outputTokens,
				"response_length", len(message.GetContent()),
				"tool_calls_count", len(message.GetToolCalls()))

			// 记录完整的响应内容
			if message.GetContent() != "" {
				// log.Info("💬 模型回答内容", "response", message.GetContent())
			}
			if message.GetReasoningContent() != "" {
				// log.Info("💭 模型思考内容", "response_thoughts", message.GetReasoningContent())
			}
		}

		if err != nil {
			// 每个 provider内部自行重试
			return nil, err
		}
		// If we got here, the request succeeded
		break
	}

	var messageContent []history.ContentBlock

	toolResults := []history.ContentBlock{}

	// Add text content
	if message.GetContent() != "" {
		messageContent = append(messageContent, history.ContentBlock{
			Type: "text",
			Text: message.GetContent(),
		})
	}
	// Handle tool calls and record them
	for _, toolCall := range message.GetToolCalls() {
		callStartTime := time.Now()
		input, _ := json.Marshal(toolCall.GetArguments())
		messageContent = append(messageContent, history.ContentBlock{
			Type:  "tool_use",
			ID:    toolCall.GetID(),
			Name:  toolCall.GetName(),
			Input: input,
		})

		// 使用通用工具调用函数
		toolInput, err := utils.ParseToolCallInput(toolCall.GetName(), string(input), toolCall.GetID())
		if err != nil {
			errMsg := fmt.Sprintf("Error parsing tool call: %v", err)
			fmt.Printf("Error: %s\n", errMsg)

			log.Error("工具调用解析失败",
				"tool_name", toolCall.GetName(),
				"error", err,
				"duration_ms", time.Since(callStartTime).Milliseconds())

			// 记录错误的工具调用
			benchResult.ToolCallList = append(benchResult.ToolCallList, ToolCallRecord{
				Name:        toolCall.GetName(),
				Arguments:   string(input),
				ToolContent: errMsg,
				TimeCostMs:  time.Since(callStartTime).Milliseconds(),
				Step:        step,
			})
			continue
		}

		// 执行工具调用并记录
		result := utils.ExecuteToolCallWithRecording(ctx, toolInput, mcpClients, benchResult, step, message.GetContent(), message.GetReasoningContent())

		// 处理工具调用结果
		if !result.Success {
			log.Error("工具调用失败", "tool_name", toolCall.GetName(), "error", result.Content)

			// Add error message as tool result
			toolResults = append(toolResults, history.ContentBlock{
				Type:      "tool_result",
				ToolUseID: toolCall.GetID(),
				Content: []history.ContentBlock{{
					Type: "text",
					Text: result.Content,
				}},
			})
		} else if len(result.MCPContent) > 0 {
			// Create the tool result block with MCP content
			resultBlock := history.ContentBlock{
				Type:      "tool_result",
				ToolUseID: toolCall.GetID(),
				Content:   result.MCPContent,
			}
			resultBlock.Text = strings.TrimSpace(result.Content)
			toolResults = append(toolResults, resultBlock)
		}
	}

	*messages = append(*messages, history.HistoryMessage{
		Role:    message.GetRole(),
		Content: messageContent,
	})

	if len(toolResults) > 0 {
		for _, toolResult := range toolResults {
			*messages = append(*messages, history.HistoryMessage{
				Role:    "tool",
				Content: []history.ContentBlock{toolResult},
			})
		}

		// 递归调用时传递最大轮数参数
		step++
		return runPromptWithToolRecording(ctx, provider, mcpClients, tools, "", messages, benchResult, step, maxRounds)
	}

	fmt.Println() // Add spacing
	return message, nil
}

// runAssistantAPIWithRecording 专门为assistant_api设计的对话处理函数
func runAssistantAPIWithRecording(
	ctx context.Context,
	provider *assistant_api.Provider,
	mcpClients map[string]client.MCPClient,
	tools []llm.Tool,
	prompt string,
	messages *[]history.HistoryMessage,
	benchResult *BenchResult,
	maxRounds int,
) error {
	// Display the user's prompt if it's not empty
	if prompt != "" {
		*messages = append(
			*messages,
			history.HistoryMessage{
				Role: "user",
				Content: []history.ContentBlock{{
					Type: "text",
					Text: prompt,
				}},
			},
		)
	}

	// Convert MessageParam to llm.Message for provider
	llmMessages := make([]llm.Message, len(*messages))
	for i := range *messages {
		llmMessages[i] = &(*messages)[i]
	}

	// 记录调用开始信息
	log.Info("=== Assistant API调用开始 ===",
		"message_count", len(llmMessages),
		"tool_count", len(tools))

	// 显示思考状态
	log.Info("💭 正在思考中...")

	callStartTime := time.Now()

	// 使用assistant_api的专门方法
	// 注意：这里传递空字符串作为prompt，因为prompt已经包含在llmMessages中了
	message, err := provider.CreateMessageWithToolHandling(
		ctx,
		"", // 传递空字符串，避免重复添加prompt，后续如果有预置消息在填充该字段
		llmMessages,
		tools,
		mcpClients,
		benchResult,
		maxRounds,
	)

	callDuration := time.Since(callStartTime)

	if err != nil {
		log.Error("Assistant API调用失败",
			"provider", provider.Name(),
			"error", err.Error(),
			"duration_ms", callDuration.Milliseconds())
		return err
	}

	// 获取使用统计
	inputTokens, outputTokens := message.GetUsage()
	benchResult.PromptTokens = inputTokens
	benchResult.CompletionTokens = outputTokens
	benchResult.TotalTokens = inputTokens + outputTokens

	log.Info("=== Assistant API调用成功 ===",
		"provider", provider.Name(),
		"duration_ms", callDuration.Milliseconds(),
		"input_tokens", inputTokens,
		"output_tokens", outputTokens,
		"total_tokens", inputTokens+outputTokens,
		"response_length", len(message.GetContent()),
		"tool_calls_count", len(message.GetToolCalls()))

	// 构建消息内容
	var messageContent []history.ContentBlock
	if message.GetContent() != "" {
		messageContent = append(messageContent, history.ContentBlock{
			Type: "text",
			Text: message.GetContent(),
		})
	}

	// 添加assistant消息到历史
	*messages = append(*messages, history.HistoryMessage{
		Role:    message.GetRole(),
		Content: messageContent,
	})

	fmt.Println() // Add spacing
	return nil
}

// AddToolCall 实现ToolCallRecorder接口，供assistant_api使用
func (b *BenchResult) AddToolCall(name, arguments, content string, timeCostMs, step int64, messageContent string, thoughts string) {
	// name: sqlite__db_query
	serverName := ""
	toolName := ""
	if strings.Contains(name, "__") {
		serverName = strings.Split(name, "__")[0]
		toolName = strings.Split(name, "__")[1]
	}
	b.ToolCallList = append(b.ToolCallList, ToolCallRecord{
		ServerName:     serverName,
		Name:           toolName,
		Arguments:      arguments,
		ToolContent:    content,
		TimeCostMs:     timeCostMs,
		MessageContent: messageContent,
		Thoughts:       thoughts,
		Step:           step,
	})
}

// updateMCPServersDBPath 更新mcpservers.json中sqlite配置的数据库路径
func updateMCPServersDBPath(newDBPath string) error {
	if mcpServersFile == "" {
		return fmt.Errorf("MCP服务器配置文件路径为空")
	}

	// 读取原始配置文件
	content, err := os.ReadFile(mcpServersFile)
	if err != nil {
		return fmt.Errorf("读取MCP服务器配置文件失败: %w", err)
	}

	// 直接替换 demo.db 为新的数据库文件名
	// 提取新路径中的文件名部分
	newFileName := filepath.Base(newDBPath)

	// 确保新文件名以.db结尾
	if !strings.HasSuffix(newFileName, ".db") {
		newFileName += ".db"
	}

	// 替换所有的 demo.db 为新的文件名，保持路径前缀不变
	newContent := strings.ReplaceAll(string(content), "demo.db", newFileName)

	// 检查是否有替换发生
	if newContent == string(content) {
		return fmt.Errorf("未找到需要更新的demo.db文件")
	}

	// 写回文件
	if err := os.WriteFile(mcpServersFile, []byte(newContent), 0644); err != nil {
		return fmt.Errorf("写入更新后的配置文件失败: %w", err)
	}

	log.Info("已更新mcpservers.json中的数据库路径", "old_file", "demo.db", "new_file", newFileName)
	return nil
}
