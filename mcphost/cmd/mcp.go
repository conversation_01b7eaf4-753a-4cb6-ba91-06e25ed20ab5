package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/charmbracelet/log"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcphost/pkg/llm"
)

const (
	transportStdio = "stdio"
	transportSSE   = "sse"
)

type MCPConfig struct {
	MCPServers map[string]ServerConfigWrapper `json:"mcpServers"`
}

// 模型配置结构
type ModelConfig struct {
	ModelName              string `json:"model_name"`
	ModelProvider          string `json:"model_provider"`
	OpenAIURL              string `json:"openai_url,omitempty"`
	OpenAIAPIKey           string `json:"openai_api_key,omitempty"`
	AnthropicURL           string `json:"anthropic_url,omitempty"`
	AnthropicAPIKey        string `json:"anthropic_api_key,omitempty"`
	GoogleAPIKey           string `json:"google_api_key,omitempty"`
	GlobalTimeout          int    `json:"global_timeout,omitempty"`
	MaxToolExecutionRounds int    `json:"max_tool_execution_rounds,omitempty"`
	// assistant_api 相关字段
	AssistantID         string `json:"assistant_id,omitempty"`
	Authorization       string `json:"authorization,omitempty"`
	Backend             string `json:"backend,omitempty"`
	AppID               string `json:"app_id,omitempty"`
	AssistantServerIP   string `json:"assistant_server_ip,omitempty"`
	AssistantServerPort int    `json:"assistant_server_port,omitempty"`
}

// 环境依赖结构
type EnvironmentDep struct {
	Path    string `json:"path"`
	Type    string `json:"type"`
	Content string `json:"content"`
}

// 自定义环境依赖数组，处理空字符串和空数组
type EnvironmentDependencyArray []EnvironmentDep

func (e *EnvironmentDependencyArray) UnmarshalJSON(data []byte) error {
	// 处理空字符串的情况
	if string(data) == `""` {
		*e = nil
		return nil
	}

	// 处理null的情况
	if string(data) == "null" {
		*e = nil
		return nil
	}

	// 尝试解析为数组
	var deps []EnvironmentDep
	if err := json.Unmarshal(data, &deps); err != nil {
		// 如果解析失败，设置为nil
		*e = nil
		return nil
	}

	// 如果是空数组，设置为nil
	if len(deps) == 0 {
		*e = nil
		return nil
	}

	*e = deps
	return nil
}

// 自定义检查列表，处理空字符串和空数组
type CheckListArray []map[string]any

func (c *CheckListArray) UnmarshalJSON(data []byte) error {
	// 处理空字符串的情况
	if string(data) == `""` {
		*c = nil
		return nil
	}

	// 处理null的情况
	if string(data) == "null" {
		*c = nil
		return nil
	}

	// 尝试解析为数组
	var checks []map[string]any
	if err := json.Unmarshal(data, &checks); err != nil {
		// 如果解析失败，设置为nil
		*c = nil
		return nil
	}

	// 如果是空数组，设置为nil
	if len(checks) == 0 {
		*c = nil
		return nil
	}

	*c = checks
	return nil
}

// 测试配置结构 - 移除旧格式兼容
type BenchConfig struct {
	Query     string `json:"query"`
	Type      string `json:"type"`
	System    string `json:"system"`
	ExtraInfo struct {
		Query                 string                     `json:"query"`
		System                string                     `json:"system"`
		EnvironmentDependency EnvironmentDependencyArray `json:"environment_dependency,omitempty"`
		CheckList             CheckListArray             `json:"check_list,omitempty"`
	} `json:"extra_info"`

	BenchResult     map[string]any `json:"bench_result"`
	BenchEnvPathUrl string         `json:"bench_env_path_url"`
}

// 获取查询内容 - 统一使用新格式
func (b *BenchConfig) GetQuery() string {
	if b.ExtraInfo.Query != "" {
		return b.ExtraInfo.Query
	}
	return b.Query
}

// 获取系统提示词
func (b *BenchConfig) GetSystemPrompt() string {
	if b.ExtraInfo.System != "" {
		return b.ExtraInfo.System
	}
	if b.System != "" {
		return b.System
	}
	return "你是一个有用的助手，擅长调用工具解决问题。"
}

// 工具调用记录
type ToolCallRecord struct {
	ServerName     string `json:"server_name"`
	Name           string `json:"name"`
	Arguments      string `json:"arguments"`
	ToolContent    string `json:"tool_content"`
	TimeCostMs     int64  `json:"time_cost_ms"`
	MessageContent string `json:"message_content"`
	Thoughts       string `json:"thoughts"`
	Step           int64  `json:"step"`
}

// 检查结果记录
type CheckResult struct {
	Type         string `json:"type"`
	Target       string `json:"target"`
	Method       string `json:"method"`
	PassRequired bool   `json:"pass_required"`
	Value        string `json:"value"`
	TimeCostMs   int64  `json:"time_cost_ms"`
	ExcuteResult string `json:"excute_result"`
	ErrMsg       string `json:"err_msg"`
}

// 工具信息记录
type ToolInfo struct {
	ServerName  string                 `json:"server_name"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// 最终结果输出结构
type BenchResult struct {
	Response         string           `json:"response"`
	ResponseThoughts string           `json:"response_thoughts,omitempty"`
	Thoughts         string           `json:"thoughts,omitempty"`
	ToolCallList     []ToolCallRecord `json:"tool_call_list"`
	CheckList        []CheckResult    `json:"check_list"`
	AvailableTools   []ToolInfo       `json:"available_tools"`
	TotalSteps       int              `json:"total_steps"`
	TotalTimeCostMs  int64            `json:"total_time_cost_ms"`
	PromptTokens     int              `json:"prompt_tokens"`
	CompletionTokens int              `json:"completion_tokens"`
	TotalTokens      int              `json:"total_tokens"`
}
type ServerConfig interface {
	GetType() string
}

type STDIOServerConfig struct {
	Command string            `json:"command"`
	Args    []string          `json:"args"`
	Env     map[string]string `json:"env,omitempty"`
}

func (s STDIOServerConfig) GetType() string {
	return transportStdio
}

type SSEServerConfig struct {
	Url     string   `json:"url"`
	Headers []string `json:"headers,omitempty"`
}

func (s SSEServerConfig) GetType() string {
	return transportSSE
}

type ServerConfigWrapper struct {
	Config ServerConfig
}

func (w *ServerConfigWrapper) UnmarshalJSON(data []byte) error {
	var typeField struct {
		Url string `json:"url"`
	}

	if err := json.Unmarshal(data, &typeField); err != nil {
		return err
	}
	if typeField.Url != "" {
		// If the URL field is present, treat it as an SSE server
		var sse SSEServerConfig
		if err := json.Unmarshal(data, &sse); err != nil {
			return err
		}
		w.Config = sse
	} else {
		// Otherwise, treat it as a STDIOServerConfig
		var stdio STDIOServerConfig
		if err := json.Unmarshal(data, &stdio); err != nil {
			return err
		}
		w.Config = stdio
	}

	return nil
}
func (w ServerConfigWrapper) MarshalJSON() ([]byte, error) {
	return json.Marshal(w.Config)
}

func mcpToolsToAnthropicTools(
	serverName string,
	mcpTools []mcp.Tool,
) []llm.Tool {
	anthropicTools := make([]llm.Tool, len(mcpTools))

	for i, tool := range mcpTools {
		namespacedName := fmt.Sprintf("%s__%s", serverName, tool.Name)

		anthropicTools[i] = llm.Tool{
			Name:        namespacedName,
			Description: tool.Description,
			InputSchema: llm.Schema{
				Type:       tool.InputSchema.Type,
				Properties: tool.InputSchema.Properties,
				Required:   tool.InputSchema.Required,
			},
		}
	}

	return anthropicTools
}

// collectToolInfoFromMCPClients 从MCP客户端收集所有工具信息
func collectToolInfoFromMCPClients(
	ctx context.Context,
	mcpClients map[string]client.MCPClient,
) []ToolInfo {
	var allToolInfos []ToolInfo

	for serverName, mcpClient := range mcpClients {
		ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
		toolsResult, err := mcpClient.ListTools(ctx, mcp.ListToolsRequest{})
		cancel()

		if err != nil {
			log.Error("获取工具失败", "server", serverName, "error", err)
			continue
		}

		for _, tool := range toolsResult.Tools {
			// 构建InputSchema，包含完整的schema信息
			inputSchema := map[string]interface{}{
				"type":       tool.InputSchema.Type,
				"properties": tool.InputSchema.Properties,
			}
			if len(tool.InputSchema.Required) > 0 {
				inputSchema["required"] = tool.InputSchema.Required
			}

			toolInfo := ToolInfo{
				ServerName:  serverName,
				Name:        tool.Name,
				Description: tool.Description,
				InputSchema: inputSchema,
			}

			allToolInfos = append(allToolInfos, toolInfo)
		}
	}

	return allToolInfos
}

// 加载模型配置文件
func loadModelConfig() (*ModelConfig, error) {
	if configFile == "" {
		return nil, fmt.Errorf("请使用 --config 参数指定模型配置文件")
	}

	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("模型配置文件不存在: %s", configFile)
	}

	configData, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("读取模型配置文件失败 %s: %w", configFile, err)
	}

	var config ModelConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析模型配置文件失败: %w", err)
	}

	return &config, nil
}

// 加载MCP服务器配置文件
func loadMCPServersConfig() (*MCPConfig, error) {
	if mcpServersFile == "" {
		return nil, fmt.Errorf("请使用 --mcpservers 参数指定MCP服务器配置文件")
	}

	if _, err := os.Stat(mcpServersFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("MCP服务器配置文件不存在: %s", mcpServersFile)
	}

	configData, err := os.ReadFile(mcpServersFile)
	if err != nil {
		return nil, fmt.Errorf("读取MCP服务器配置文件失败 %s: %w", mcpServersFile, err)
	}

	// 替换配置文件中的环境变量
	configStr := string(configData)
	if dataengInputDir := os.Getenv("DATAENG_INPUT_DATA_DIR"); dataengInputDir != "" {
		configStr = strings.ReplaceAll(configStr, "${DATAENG_INPUT_DATA_DIR}", dataengInputDir)
		configStr = strings.ReplaceAll(configStr, "//", "/")
	}

	var config MCPConfig
	if err := json.Unmarshal([]byte(configStr), &config); err != nil {
		return nil, fmt.Errorf("解析MCP服务器配置文件失败: %w", err)
	}

	return &config, nil
}

// 加载测试配置文件
func loadBenchConfig() (*BenchConfig, error) {
	if benchFile == "" {
		return nil, fmt.Errorf("请使用 --bench 参数指定测试配置文件")
	}

	if _, err := os.Stat(benchFile); os.IsNotExist(err) {
		return nil, fmt.Errorf("测试配置文件不存在: %s", benchFile)
	}

	configData, err := os.ReadFile(benchFile)
	if err != nil {
		return nil, fmt.Errorf("读取测试配置文件失败 %s: %w", benchFile, err)
	}

	var config BenchConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	return &config, nil
}

func createMCPClients(
	config *MCPConfig,
) (map[string]client.MCPClient, error) {
	clients := make(map[string]client.MCPClient)

	for name, server := range config.MCPServers {
		var mcpClient client.MCPClient
		var err error

		if server.Config.GetType() == transportSSE {
			sseConfig := server.Config.(SSEServerConfig)

			// Create SSE client using new API
			sseClient, err := client.NewSSEMCPClient(sseConfig.Url)
			if err != nil {
				for _, c := range clients {
					c.Close()
				}
				return nil, fmt.Errorf(
					"failed to create SSE MCP client for %s: %w",
					name,
					err,
				)
			}

			// Start the SSE client
			if err := sseClient.Start(context.Background()); err != nil {
				for _, c := range clients {
					c.Close()
				}
				return nil, fmt.Errorf(
					"failed to start SSE client for %s: %w",
					name,
					err,
				)
			}

			mcpClient = sseClient
		} else {
			stdioConfig := server.Config.(STDIOServerConfig)
			var env []string
			for k, v := range stdioConfig.Env {
				env = append(env, fmt.Sprintf("%s=%s", k, v))
			}
			mcpClient, err = client.NewStdioMCPClient(
				stdioConfig.Command,
				env,
				stdioConfig.Args...)
			if err != nil {
				for _, c := range clients {
					c.Close()
				}
				return nil, fmt.Errorf(
					"failed to create STDIO MCP client for %s: %w",
					name,
					err,
				)
			}
		}

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		log.Info("Initializing server...", "name", name, "config: ", server.Config)
		initRequest := mcp.InitializeRequest{}
		initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
		initRequest.Params.ClientInfo = mcp.Implementation{
			Name:    "mcphost",
			Version: "0.1.0",
		}
		initRequest.Params.Capabilities = mcp.ClientCapabilities{}

		_, err = mcpClient.Initialize(ctx, initRequest)
		if err != nil {
			mcpClient.Close()
			for _, c := range clients {
				c.Close()
			}
			return nil, fmt.Errorf(
				"failed to initialize MCP client for %s: %w",
				name,
				err,
			)
		}

		clients[name] = mcpClient
	}

	return clients, nil
}
