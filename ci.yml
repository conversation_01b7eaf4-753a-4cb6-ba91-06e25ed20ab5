Global:
    version: "2.0"
Default:
    profile:
        - production
Profiles:
    - profile:
      name: production
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - python: 3.11.2
      build:
        command: |
            sh ./build.sh
      artifacts:
        release: true
    - profile:
      name: dev
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - python: 3.11.2
      build:
        command: |
            sh ./build.sh
      artifacts:
        release: true