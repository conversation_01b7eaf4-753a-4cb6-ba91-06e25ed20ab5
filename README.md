# MCP Evaluation Platform - 基于MCPHost的自动化评估系统

一个专为策略工程设计的工程化MCP（Model Context Protocol）评估平台，提供独立的Docker环境来运行MCP评估任务。

## 项目介绍

MCP Evaluation Platform 是一个用于大模型Agent能力评估的自动化平台，基于Model Context Protocol（MCP）协议实现。本项目提供了标准化的测试环境，能够初始化测试场景、记录模型行为、执行工具调用，并进行自动化结果验证。

### 主要功能

- **多模型支持**：支持OpenAI、Anthropic、Google、Baidu千帆、文心等多种大模型
- **环境初始化**：自动创建目录、文件、初始化数据库等测试环境
- **工具调用**：支持文件系统操作、数据库查询等多种工具能力
- **自动化验证**：通过规则判断或LLM判断自动验证测试结果
- **详细日志**：记录所有工具调用和验证步骤的详细信息

## 安装指南

### 前置条件

- Go 1.23+
- Node.js 20+ (MCP服务器)
- Python 3.11+ (算子引擎， MCP服务器依赖)


## 使用方法

### 基本命令

```bash
# 运行mcphost测试用例（本地）
make test-mcphost

# 运行mcphost测试用例（Docker）
make docker-run

# 构建Docker镜像
make docker-build

# 推送Docker镜像
make docker-push

# 运行mcphost本地评估任务（local）
make run-mcp-eval
```

### 配置文件说明

#### 1. 模型配置 (model_config.json)


```json
// ERNIE X1T 模型配置
{
    "model_name": "ERNIE-4.5-single-custom-yiyanweb",
    "model_provider": "assistant_api",
    "backend": "yiyan",
    "authorization": "testldx/1722333193666/9c2285103869105c43191fd48abb420eadf6286de45c7576e3bd3130168a606f",
    "assistant_server_ip": "*************",
    "assistant_server_port": 8361,
    "app_id": "123456",
    "global_timeout": 300
}

// qwen3 模型配置
{
    "model_name": "qwen3235ba22b_pool",
    "model_provider": "openai",
    "openai_url": "http://************:8081/api/real_time/v1",
    "openai_api_key": "sk-123",
    "global_timeout": 300
}

// gpt4.1 模型配置
{
    "model_name": "gpt41_pool",
    "model_provider": "openai",
    "openai_url": "http://************:8081/api/real_time/v1",
    "openai_api_key": "sk-123",
    "global_timeout": 300
}

// doubao-1-5-thinking-pro-250415 模型配置
{
    "model_name": "doubao-1-5-thinking-pro-250415_pool",
    "model_provider": "openai",
    "openai_url": "http://************:8081/api/real_time/v1",
    "openai_api_key": "sk-123",
    "global_timeout": 300
}
```

#### 2. MCP服务器配置 (mcp_config.json)

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"]
    },
    "playwright": {
      "command": "npx",
      "args": [
        "-y",
        "@playwright/mcp@0.0.27",
        "--headless",
        "--browser",
        "chromium",
        "--no-sandbox",
        "--executable-path",
        "/home/<USER>/ms-playwright/chromium-1169/chrome-linux/chrome"
      ]
    },
    "firecrawl-mcp": {
      "command": "npx",
      "args": ["-y", "firecrawl-mcp@1.11.0"],
      "env": {
        "FIRECRAWL_API_KEY": "xxx"
      }
    },
    "sqlite": {
      "command": "uvx",
      "args": ["mcp-server-sqlite", "--db-path", "./order.db"]
    },
    "amap-maps": {
      "type": "sse",
      "url": "https://mcp.api-inference.modelscope.cn/sse/xx"
    }
  }
}

```

#### 3. 评估配置（策略提供） (bench.json)

```json
{
  "data_id": 14,
  "type": "static",
  "tags": ["agent", "单轮"],
  "ground_truth": "",
  "reference": "",
  "system": "你是一个有用的助手，擅长调用工具解决问题，请记住当前时间为2025年5月28日",
  "extra_info": {
    "index": 34,
    "sub_scenario": "内容创作与保存",
    "difficulty_level": "medium",
    "query": "我需要为我的新网站项目 `./file_system_output/task_34/new_website/` 创建基本的文件结构和一些初始内容。请创建以下目录结构：`./file_system_output/task_34/new_website/css` 和 `./file_system_output/task_34/new_website/js`。然后在根目录 `new_website` 下创建一个 `index.html` 文件，包含基本的HTML骨架和一个指向 `style.css` 的链接以及一个指向 `script.js` 的脚本引用。在 `css` 目录下创建一个 `style.css` 文件，包含一个简单的body背景颜色样式。在 `js` 目录下创建一个 `script.js` 文件，包含一个打印 'Hello World' 到控制台的简单脚本。",
    "environment_dependency": [
      {
        "path": "./file_system_mock_data/task_34/",
        "type": "directory",
        "content": ""
      }
    ],
    "tool_call_list": [
      {
        "tool": "create_directory",
        "params": { "path": "./file_system_output/task_34/new_website/css" },
        "purpose": "创建CSS目录"
      },
      {
        "tool": "create_directory",
        "params": { "path": "./file_system_output/task_34/new_website/js" },
        "purpose": "创建JavaScript目录"
      },
      {
        "tool": "write_file",
        "params": {
          "path": "./file_system_output/task_34/new_website/index.html",
          "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>My New Website</title>\n    <link rel=\"stylesheet\" href=\"css/style.css\">\n</head>\n<body>\n    <h1>Welcome!</h1>\n    <script src=\"js/script.js\"></script>\n</body>\n</html>"
        },
        "purpose": "创建并写入 index.html 文件"
      },
      {
        "tool": "write_file",
        "params": {
          "path": "./file_system_output/task_34/new_website/css/style.css",
          "content": "body {\n    background-color: #f0f0f0;\n    font-family: Arial, sans-serif;\n    margin: 20px;\n    color: #333;\n}"
        },
        "purpose": "创建并写入 style.css 文件"
      },
      {
        "tool": "write_file",
        "params": {
          "path": "./file_system_output/task_34/new_website/js/script.js",
          "content": "// Simple script\n\nfunction greet() {\n    console.log('Hello World from script.js!');\n}\n\ngreet();"
        },
        "purpose": "创建并写入 script.js 文件"
      }
    ],
    "expected_result": [
      {
        "path": "./file_system_output/task_34/new_website/",
        "type": "directory",
        "content": ""
      },
      {
        "path": "./file_system_output/task_34/new_website/css/",
        "type": "directory",
        "content": ""
      },
      {
        "path": "./file_system_output/task_34/new_website/js/",
        "type": "directory",
        "content": ""
      },
      {
        "path": "./file_system_output/task_34/new_website/index.html",
        "type": "file",
        "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>My New Website</title>\n    <link rel=\"stylesheet\" href=\"css/style.css\">\n</head>\n<body>\n    <h1>Welcome!</h1>\n    <script src=\"js/script.js\"></script>\n</body>\n</html>"
      },
      {
        "path": "./file_system_output/task_34/new_website/css/style.css",
        "type": "file",
        "content": "body {\n    background-color: #f0f0f0;\n    font-family: Arial, sans-serif;\n    margin: 20px;\n    color: #333;\n}"
      },
      {
        "path": "./file_system_output/task_34/new_website/js/script.js",
        "type": "file",
        "content": "// Simple script\n\nfunction greet() {\n    console.log('Hello World from script.js!');\n}\n\ngreet();"
      }
    ],
    "check_list": [
      {
        "type": "check_directory_exist",
        "target": "./file_system_output/task_34/new_website/css",
        "method": "rule_judge",
        "pass_required": true,
        "value": "CSS目录已创建"
      },
      {
        "type": "check_directory_exist",
        "target": "./file_system_output/task_34/new_website/js",
        "method": "rule_judge",
        "pass_required": true,
        "value": "JS目录已创建"
      },
      {
        "type": "check_file_exist",
        "target": "./file_system_output/task_34/new_website/index.html",
        "method": "rule_judge",
        "pass_required": true,
        "value": "index.html 文件已创建"
      },
      {
        "type": "check_file_exist",
        "target": "./file_system_output/task_34/new_website/css/style.css",
        "method": "rule_judge",
        "pass_required": true,
        "value": "style.css 文件已创建"
      },
      {
        "type": "check_file_exist",
        "target": "./file_system_output/task_34/new_website/js/script.js",
        "method": "rule_judge",
        "pass_required": true,
        "value": "script.js 文件已创建"
      },
      {
        "type": "check_file_content",
        "target": "./file_system_output/task_34/new_website/js/script.js",
        "method": "llm_judge",
        "pass_required": true,
        "value": "script.js 应包含一行JavaScript代码，例如 `console.log('Hello World');` 或类似功能的代码。"
      },
      {
        "type": "check_tool_call_list",
        "target": "response_tool_call_list",
        "method": "rule_judge",
        "pass_required": false,
        "value": [
          "create_directory",
          "create_directory",
          "write_file",
          "write_file",
          "write_file"
        ]
      }
    ],
    "complexity_factors": {
      "path_length": 5,
      "tool_diversity": 2,
      "parameter_complexity": "medium",
      "context_dependency": "low"
    },
    "task_design_thoughts": "1. 任务目标分析: 创建一个基本的Web项目结构，包括特定的子目录和包含初始内容的HTML, CSS, JS文件。这是一个典型的项目脚手架任务。\n2. 解决方案设计: 使用 `create_directory` 创建 `css` 和 `js` 子目录。然后使用 `write_file` 三次，分别创建 `index.html`, `css/style.css`, 和 `js/script.js`，并写入预设的内容。\n3. 工具选择理由: `create_directory` 用于创建目录结构。`write_file` 用于生成包含指定内容的文件。\n4. 执行顺序设计: 先创建目录，再在这些目录中或项目的根目录创建文件。顺序不严格，但通常先创建结构再填充内容比较自然。\n5. 可能的挑战点: 模型需要正确构造每个文件的内容，特别是 `index.html` 中对CSS和JS文件的引用路径必须正确。相对路径的理解是关键。\n6. 检查项设计理由: 检查所有目录和文件是否按预期创建。通过LLM检查每个文件的内容是否符合要求，例如HTML的结构和引用，CSS的简单样式，JS的简单脚本。检查工具调用顺序。"
  }
}

```

## 项目内部相关文档

- [模型资源统一调度服务接口文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/_cb7F-QsrB/smW9MlHI-zzO7K)
- [Assistants API接口文档（For 千帆&厂内调研）V4协议](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/a83e5d6c3ad440)
- [Agent自动评估托管需求](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/yKeL8Lljko/kpUg5sswPy/Um5aeqXnM9N4LG)
- [支持评估场景MCP服务设计](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tjIp17bwPd/_cb7F-QsrB/AuyJyPfyhAvVeF)

## 参考仓库

- [baidu/ai-appengine](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/ps-se/ai-appengine/tree/master)
- [MCPBench](https://github.com/modelscope/MCPBench/)
- [MCPHost](https://github.com/mark3labs/mcphost)
- [MCP-python-sdk](https://github.com/modelcontextprotocol/python-sdk)
- [fastmcp](https://github.com/jlowin/fastmcp)
- [dspy-ai](https://github.com/stanfordnlp/dspy)
