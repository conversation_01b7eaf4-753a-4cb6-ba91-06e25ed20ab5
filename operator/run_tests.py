#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_tests.py
测试运行脚本
"""
import sys
import subprocess
from pathlib import Path


def run_tests():
    """运行测试"""
    # 确保在operator目录下
    operator_dir = Path(__file__).parent
    
    # 添加当前目录到Python路径
    sys.path.insert(0, str(operator_dir))
    
    # 运行pytest
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/",
        "-v",
        "--tb=short",
        "--color=yes"
    ]
    
    print(f"运行命令: {' '.join(cmd)}")
    print(f"工作目录: {operator_dir}")
    
    try:
        result = subprocess.run(cmd, cwd=operator_dir, check=False)
        return result.returncode
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1


def run_specific_test(test_name):
    """运行特定测试"""
    operator_dir = Path(__file__).parent
    sys.path.insert(0, str(operator_dir))
    
    cmd = [
        sys.executable, "-m", "pytest",
        f"tests/test_self_define_operator.py::{test_name}",
        "-v",
        "--tb=short",
        "--color=yes"
    ]
    
    print(f"运行特定测试: {test_name}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=operator_dir, check=False)
        return result.returncode
    except Exception as e:
        print(f"运行测试时出错: {e}")
        return 1


def run_mcp_priority_tests():
    """运行MCP优先级相关测试"""
    return run_specific_test("TestMCPConfigPriority")


def run_integration_tests():
    """运行集成测试"""
    return run_specific_test("TestIntegration")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        if test_type == "mcp":
            exit_code = run_mcp_priority_tests()
        elif test_type == "integration":
            exit_code = run_integration_tests()
        elif test_type.startswith("Test"):
            exit_code = run_specific_test(test_type)
        else:
            print(f"未知的测试类型: {test_type}")
            print("可用选项: mcp, integration, 或具体的测试类名")
            exit_code = 1
    else:
        exit_code = run_tests()
    
    sys.exit(exit_code) 