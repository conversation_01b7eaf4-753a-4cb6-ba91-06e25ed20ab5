#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
self_define_operator.py
MCP配置生成器和批量执行器
"""
import sys
import os
import json
import subprocess
import yaml
import tempfile
import shutil
import requests
from pathlib import Path
from dataeng_sdk.operator_base import OperatorBase
from dataeng_sdk import common_tool
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataeng_sdk.k8s_client.dataeng_k8s_client import submit_k8s_job
import time
import uuid
import subprocess

# K8s执行配置
K8S_IMAGE_ID = "mcp/mcp-env:latest"
K8S_COMMAND = "./boost.sh"
K8S_BASE_MOUNT_PATH = "/home/<USER>/dataeng"
K8S_SOURCE_TYPE = "mcp"
K8S_TIMEOUT = 30 * 60  # 30分钟
LOCAL_TIMEOUT = 15 * 60  # 15分钟


# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入配置
from config import config

logger = common_tool.get_logger(__name__)


class Operator(OperatorBase):
    """
    MCP配置生成器和批量执行器算子

    支持参数：
    - models: 要评估的模型列表（数组或逗号分隔字符串）
    - max_workers: 最大并发执行数
    - run_local: 是否本地运行（true/false，默认false使用K8s）
    - check_model: 检查模型配置（格式：provider:model）
    - retry_failed_only: 失败重试模式（true/false，默认false）
      当为true时，只处理输入JSONL中execution_result.success=false的记录

    支持的输入文件类型：
    - *.jsonl: 测试数据文件
    - mcpservers.json: 自定义MCP服务器配置（支持新格式{"mcpservers": {...}}和旧格式）
    - environment_dependency.json: 环境依赖配置，会添加到bench.json的extra_info.environment_dependency数组前面
    - check.py: 结果检查脚本
    - *mock_mcp*.py/*mcp_server*.py: 自定义MCP服务器脚本
    """

    def _is_auto_evaluation_mode(self):
        """判断是否为自动评估模式"""
        return (
            hasattr(self, "global_params")
            and self.global_params is not None
            and self.global_params.get("models") is not None
        )

    def _convert_models_from_global_params(self):
        """将 global_params 转换为内部模型格式"""
        # 获取 pool_name 数组
        model_list = self.global_params.get("models", [])
        if not model_list:
            raise ValueError("global_params.models 不能为空")

        converted_models = []
        for model in model_list:
            # 拼装成 "uni_crawl:pool_name" 格式
            pool_name = model.get("pool_name")
            model_id = f"{model.get('model_id')}__sandbox"
            if pool_name:
                converted_model = f"uni_crawl:{pool_name}"
                converted_models.append(converted_model)
                logger.info(f"模型转换: pool_name={pool_name} -> {converted_model}")
            elif model_id:
                converted_model = f"uni_crawl:{model_id}"
                converted_models.append(converted_model)
                logger.info(f"模型转换: model_id={model_id} -> {converted_model}")
            else:
                raise ValueError(f"模型转换失败: {model}")
        logger.info(f"转换后的模型列表: {converted_models}")
        return converted_models

    def _get_max_workers_from_global_params(self):
        """从 global_params 获取最大并发数，优先从 global_params.models[0].concurrence 获取"""
        try:
            models = self.global_params.get("models", [])
            if isinstance(models, list) and len(models) > 0:
                first_model = models[0] or {}
                conc = first_model.get("concurrence")
                if conc is not None:
                    # 支持字符串/浮点数/整数
                    if isinstance(conc, str):
                        conc = conc.strip()
                        if conc.isdigit():
                            conc = int(conc)
                        else:
                            conc = None
                    elif isinstance(conc, float):
                        conc = int(conc)
                    # 验证
                    if isinstance(conc, int) and conc > 0:
                        return conc
            # 兼容旧字段 work_num
            work_num = self.global_params.get("work_num")
            if isinstance(work_num, str):
                work_num = work_num.strip()
                if work_num.isdigit():
                    work_num = int(work_num)
                else:
                    work_num = None
            if isinstance(work_num, int) and work_num > 0:
                return work_num
        except Exception as e:
            logger.warning(f"解析global_params并发数失败，将使用默认值: {e}")
        return 4  # 默认值

    def _get_check_model_from_global_params(self):
        """获取检查模型，目前评估系统没有提供检查模型参数，先写死"""
        return "uni_crawl:gpt-4.1-mini"

    def _parse_global_params(self):
        """解析 global_params 并转换为内部参数格式"""
        if not self.global_params:
            raise ValueError("自动评估模式下 global_params 不能为空")

        # 转换模型列表
        self.eval_model_list = self._convert_models_from_global_params()

        # 获取其他参数
        self.max_workers = self._get_max_workers_from_global_params()
        self.run_local = False  # 自动评估通常远程执行
        self.check_model = self._get_check_model_from_global_params()
        self.retry_failed_only = False  # 默认不启用失败重试

    def _parse_regular_params(self):
        """解析普通流程的参数（保持原有逻辑）"""
        # 从 self.params 中获取参数，如果没有则使用配置文件中的默认值
        models_param = self.params.get("models")
        if models_param:
            # models参数直接是数组格式，不需要分割
            if isinstance(models_param, list):
                self.eval_model_list = models_param
            elif isinstance(models_param, str):
                # 兼容字符串格式（逗号分隔）
                self.eval_model_list = [
                    model.strip() for model in models_param.split(",") if model.strip()
                ]
            else:
                logger.warning(
                    f"models参数格式不正确: {type(models_param)}, 使用配置文件默认值"
                )
                self.eval_model_list = config.get("eval_model_list", [])
        else:
            # 使用配置文件中的默认值
            self.eval_model_list = config.get("eval_model_list", [])

        # 从 self.params 中获取 max_workers 参数
        max_workers_param = self.params.get("max_workers")
        if max_workers_param is not None:
            try:
                self.max_workers = int(max_workers_param)
            except (ValueError, TypeError):
                logger.warning(f"max_workers参数无效: {max_workers_param}, 使用默认值")
                self.max_workers = config.get("max_workers", 4)
        else:
            self.max_workers = config.get("max_workers", 4)  # 并发执行数

        # 从 self.params 中获取运行模式参数
        self.run_local = self.params.get("run_local", False)
        if isinstance(self.run_local, str):
            self.run_local = self.run_local.lower() in ("true", "1", "yes")

        # 从 self.params 中获取 check_model 参数
        self.check_model = self.params.get("check_model")
        if self.check_model:
            logger.info(f"检查模型: {self.check_model}")
        else:
            logger.info("未配置检查模型，将跳过结果检查")

        # 从 self.params 中获取失败重试参数
        self.retry_failed_only = self.params.get("retry_failed_only", False)
        if isinstance(self.retry_failed_only, str):
            self.retry_failed_only = self.retry_failed_only.lower() in (
                "true",
                "1",
                "yes",
            )

    def _setup(self):
        """
        算子执行前准备工作
        """
        logger.info("setup executing...")
        logger.info("self.task_info: %s", self.task_info)
        logger.info("self.input_files: %s", self.input_files)
        logger.info("self.output_local_path: %s", self.output_local_path)

        # 判断运行模式并解析参数
        if self._is_auto_evaluation_mode():
            logger.info("运行在自动评估模式")
            logger.info("self.global_params: %s", self.global_params)
            self._parse_global_params()
        else:
            logger.info("运行在普通流程模式")
            logger.info("self.params: %s", self.params)
            self._parse_regular_params()

        logger.info(f"解析得到的模型列表: {self.eval_model_list}")
        logger.info(f"解析得到的最大并发数: {self.max_workers}")
        logger.info(f"运行模式: {'本地运行' if self.run_local else 'K8s运行'}")
        logger.info(
            f"失败重试模式: {'仅重试失败任务' if self.retry_failed_only else '全部重新执行'}"
        )

        # 验证输入
        if not self.eval_model_list:
            raise ValueError(
                "eval_model_list参数不能为空，请通过params.models参数或配置文件指定"
            )

        # 记录模型解析信息
        logger.info("模型列表解析:")
        for model_string in self.eval_model_list:
            provider, model = self._parse_model_string(model_string)
            logger.info(f"  {model_string} -> provider: {provider}, model: {model}")

        if not self.input_files:
            raise ValueError("需要提供输入文件")

        # 从输入文件中识别并分类所有文件
        self.custom_mcp_servers = {}
        self.jsonl_files = []
        self.check_script_path = None
        self.other_input_files = []
        self.environment_dependency = []

        for input_file in self.input_files:
            file_path = Path(input_file)
            if "mcpservers.json" in file_path.name.lower():
                # 这是自定义的MCP服务器配置文件
                try:
                    with open(input_file, "r", encoding="utf-8") as f:
                        config_data = json.load(f)

                    if "mcpservers" in config_data:
                        self.custom_mcp_servers = config_data["mcpservers"]
                        logger.info(
                            f"加载自定义MCP服务器配置（标准格式）: {input_file}"
                        )
                    elif "mcpServers" in config_data:
                        self.custom_mcp_servers = config_data["mcpServers"]
                        logger.info(
                            f"加载自定义MCP服务器配置（标准新格式）: {input_file}"
                        )
                    else:
                        self.custom_mcp_servers = config_data
                        logger.info(f"加载自定义MCP服务器配置（旧格式）: {input_file}")

                    logger.info(
                        f"配置包含服务器: {list(self.custom_mcp_servers.keys())}"
                    )
                except Exception as e:
                    logger.error(
                        f"加载自定义MCP服务器配置失败: {input_file}, 错误: {e}"
                    )
                    self.custom_mcp_servers = {}

            elif "environment_dependency.json" in file_path.name.lower():
                # 这是环境依赖配置文件
                try:
                    with open(input_file, "r", encoding="utf-8") as f:
                        env_data = json.load(f)

                    if "environment_dependency" in env_data and isinstance(
                        env_data["environment_dependency"], list
                    ):
                        self.environment_dependency = env_data["environment_dependency"]
                        logger.info(f"加载环境依赖配置: {input_file}")
                        logger.info(
                            f"环境依赖项数量: {len(self.environment_dependency)}"
                        )
                        for dep in self.environment_dependency:
                            logger.info(
                                f"  - {dep.get('type', 'unknown')}: {dep.get('path', 'unknown')}"
                            )
                    else:
                        logger.warning(f"环境依赖配置文件格式不正确: {input_file}")
                        self.environment_dependency = []
                except Exception as e:
                    logger.error(f"加载环境依赖配置失败: {input_file}, 错误: {e}")
                    self.environment_dependency = []

            elif "check.py" in file_path.name.lower():
                # 这是检查脚本
                self.check_script_path = input_file
                logger.info(f"识别到检查脚本: {input_file}")

            elif ".jsonl" in file_path.suffix:
                # 文件后缀是jsonl
                self.jsonl_files.append(input_file)
            else:
                # 其他输入文件
                self.other_input_files.append(input_file)
                logger.info(f"识别到其他输入文件: {input_file}")

        if not self.jsonl_files:
            raise ValueError("需要提供至少一个JSONL数据文件")

        logger.info(
            f"识别到 {len(self.jsonl_files)} 个JSONL文件, "
            f"{len(self.custom_mcp_servers)} 个自定义MCP服务器, "
            f"{len(self.environment_dependency)} 个环境依赖项, "
            f"{len(self.other_input_files)} 个其他文件, "
            f"检查脚本: {'是' if self.check_script_path else '否'}"
        )

        # 使用已加载的配置
        self.yaml_config = config

        # 创建工作目录 - 将临时文件放在最终输出目录的tmp子目录中
        self.final_output_path = Path(common_tool.get_output_path())
        self.local_output_path = self.final_output_path / "tmp"

        for dir_path in [self.final_output_path]:
            dir_path.mkdir(parents=True, exist_ok=True)

        logger.info(f"本地输出目录: {self.local_output_path}")
        logger.info(f"最终输出目录: {self.final_output_path}")

        # 存储最终输出文件列表
        self.output_files = []

    def _prepare_task_environment(self, task_id, model_string, jsonl_line):
        """
        准备任务执行环境，包括创建目录结构、生成配置文件、复制输入文件

        Args:
            task_id: 任务ID
            model_string: 模型字符串
            jsonl_line: JSONL数据行

        Returns:
            dict: 包含任务环境信息的字典
        """
        # 统一的目录结构，无论K8s还是本地执行都使用相同的层级
        task_input_dir = self.local_output_path / task_id / "input"
        task_output_dir = self.local_output_path / task_id / "output"
        task_work_dir = self.local_output_path / task_id / "work"
        task_log_dir = self.local_output_path / task_id / "logs"

        task_input_dir.mkdir(parents=True, exist_ok=True)
        task_output_dir.mkdir(parents=True, exist_ok=True)
        task_work_dir.mkdir(parents=True, exist_ok=True)
        task_log_dir.mkdir(parents=True, exist_ok=True)

        task_info = {
            "task_input_dir": task_input_dir,
            "task_output_dir": task_output_dir,
            "task_work_dir": task_work_dir,
            "task_log_dir": task_log_dir,
        }

        # 生成配置文件到任务输入目录
        server_names = jsonl_line.get("extra_info", {}).get("server_name", [])

        mcp_config_path = task_input_dir / "mcpservers.json"
        model_config_path = task_input_dir / "model_config.json"
        bench_config_path = task_input_dir / "bench.json"

        # 生成配置文件
        self._generate_mcp_config(server_names, mcp_config_path, jsonl_line)
        self._generate_model_config(model_string, model_config_path)
        self._generate_bench_config(jsonl_line, bench_config_path)

        if self.check_script_path:
            src_path = Path(self.check_script_path)
            dst_path = task_input_dir / src_path.name
            shutil.copy2(src_path, dst_path)
            logger.info(f"复制检查脚本到任务输入目录: {src_path.name}")

        for other_file in self.other_input_files:
            src_path = Path(other_file)
            dst_path = task_input_dir / src_path.name
            shutil.copy2(src_path, dst_path)
            logger.info(f"复制其他文件到任务输入目录: {src_path.name}")

        # 将配置文件路径添加到任务信息中
        task_info.update(
            {
                "mcp_config_path": mcp_config_path,
                "model_config_path": model_config_path,
                "bench_config_path": bench_config_path,
            }
        )

        logger.info(f"任务环境准备完成: {task_id}")
        logger.debug(f"输入目录: {task_input_dir}")
        logger.debug(f"输出目录: {task_output_dir}")
        logger.debug(f"工作目录: {task_work_dir}")
        logger.debug(f"日志目录: {task_log_dir}")

        return task_info

    def _parse_model_string(self, model_string):
        """解析模型字符串，支持 'provider:model' 格式"""
        if ":" in model_string:
            provider_name, model_name = model_string.split(":", 1)
            return provider_name.strip(), model_name.strip()
        else:
            # 兼容旧格式，只有模型名，需要在所有provider中查找
            return None, model_string.strip()

    def _get_model_config_by_name(self, model_string):
        """根据模型字符串获取对应的配置，支持 'provider:model' 格式"""
        target_provider, target_model = self._parse_model_string(model_string)

        model_providers = self.yaml_config.get("model_providers", {})

        if target_provider not in model_providers:
            raise ValueError(f"未找到provider: {target_provider}")

        provider_config = model_providers[target_provider]
        for model_info in provider_config.get("model_list", []):
            if model_info == target_model or model_info == "*":
                return self._build_model_config(
                    target_provider, target_model, provider_config
                )

        raise ValueError(f"在provider {target_provider} 中未找到模型 {target_model}")

    def _build_model_config(self, provider_name, model_name, provider_config):
        """构建模型配置"""
        # 合并provider配置和model配置
        config = {"model_name": model_name, "model_provider": provider_name}

        # 复制provider级别的配置
        for key, value in provider_config.items():
            if key not in ["model_list"]:
                config[key] = value

        # 根据provider类型设置特定字段
        if provider_name == "assistant_api":
            config.update(
                {
                    "backend": provider_config.get("backend"),
                    "authorization": provider_config.get("authorization"),
                    "assistant_server_ip": provider_config.get("assistant_server_ip"),
                    "assistant_server_port": provider_config.get(
                        "assistant_server_port"
                    ),
                    "app_id": provider_config.get("app_id"),
                }
            )
        elif (
            provider_name == "uni_crawl"
            or provider_name == "anquan"
            or provider_name == "qianfan"
        ):
            config.update(
                {
                    "openai_url": provider_config.get("openai_url"),
                    "openai_api_key": provider_config.get("openai_api_key"),
                }
            )
            # uni_crawl实际使用openai provider
            config["model_provider"] = "openai"
        elif provider_name == "openai":
            config.update(
                # 从环境变量中获取
                {
                    "openai_url": os.environ.get("OPENAI_API_BASE"),
                    "openai_api_key": os.environ.get("OPENAI_API_KEY"),
                }
            )

        return config

    def _generate_mcp_config(self, server_names, config_path, jsonl_line=None):
        """根据server_names生成MCP配置文件，支持三级优先级配置来源"""
        mcp_servers_config = {}

        # 检查jsonl_line是否已有执行结果
        has_existing_results = False
        if jsonl_line:
            has_existing_results = (
                jsonl_line.get("bench_result") is not None
                and jsonl_line.get("bench_env_path_url") is not None
            )

        # 三级优先级配置来源
        # 1. jsonl行中的extra_info.mcpservers (最高优先级)
        extra_info_servers = {}
        if jsonl_line and jsonl_line.get("extra_info", {}).get("mcpservers"):
            extra_info_servers = jsonl_line["extra_info"]["mcpservers"]
            logger.info(
                f"从JSONL extra_info中获取MCP服务器配置: {list(extra_info_servers.keys())}"
            )

        # 2. input_files中的mcpservers.json (中等优先级)
        custom_servers = self.custom_mcp_servers
        if custom_servers:
            logger.info(
                f"从自定义配置文件中获取MCP服务器配置: {list(custom_servers.keys())}"
            )

        # 3. yaml配置文件中的配置 (最低优先级)
        yaml_servers = self.yaml_config.get("mcp_servers", {})

        # 从配置文件获取映射规则
        server_name_mapping = self.yaml_config.get("mcp_server_mapping", {})

        for server_name in server_names:
            config_found = False

            # 优先级1: 从JSONL的extra_info中查找
            if server_name in extra_info_servers:
                mcp_servers_config[server_name] = extra_info_servers[server_name]
                logger.info(f"使用JSONL extra_info中的MCP服务器配置: {server_name}")
                config_found = True

            # 优先级2: 从自定义配置文件中查找
            elif server_name in custom_servers:
                mcp_servers_config[server_name] = custom_servers[server_name]
                logger.info(f"使用自定义配置文件中的MCP服务器配置: {server_name}")
                config_found = True

            # 优先级3: 从YAML配置文件中查找（使用映射）
            else:
                # 使用映射查找配置中的服务器名称
                config_server_name = server_name_mapping.get(server_name, server_name)

                if config_server_name in yaml_servers:
                    mcp_servers_config[server_name] = yaml_servers[config_server_name]
                    logger.info(
                        f"使用YAML配置中的MCP服务器配置: {server_name} -> {config_server_name}"
                    )
                    config_found = True

            # 处理配置缺失的情况
            if not config_found:
                if has_existing_results:
                    # 如果已有执行结果，则不校验配置，也不报警告
                    logger.debug(
                        f"跳过MCP服务器配置校验（已有执行结果）: {server_name}"
                    )
                else:
                    # 如果没有执行结果，直接抛出异常退出
                    raise ValueError(f"未找到MCP服务器配置: {server_name}")

        config = {"mcpServers": mcp_servers_config}

        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        logger.debug(f"生成MCP配置文件: {config_path}")
        logger.info(f"MCP配置包含服务器: {list(mcp_servers_config.keys())}")
        return config_path

    def _generate_model_config(self, model_string, config_path):
        """生成模型配置文件，包含检查模型配置"""
        model_config = self._get_model_config_by_name(model_string)

        # 如果有检查模型，添加检查模型配置
        if self.check_model:
            try:
                check_provider, check_model_name = self._parse_model_string(
                    self.check_model
                )
                check_model_config = self._get_model_config_by_name(self.check_model)

                # 添加检查模型相关配置
                model_config["check_model_name"] = "openai/" + check_model_name
                model_config["check_openai_url"] = check_model_config.get(
                    "openai_url", ""
                )
                model_config["check_openai_api_key"] = check_model_config.get(
                    "openai_api_key", ""
                )

                logger.info(f"添加检查模型配置: {self.check_model}")
            except Exception as e:
                logger.error(f"解析检查模型配置失败: {self.check_model}, 错误: {e}")

        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(model_config, f, indent=2, ensure_ascii=False)

        logger.debug(f"生成模型配置文件: {config_path} for {model_string}")
        return config_path

    def _create_safe_filename(self, model_string):
        """为模型字符串创建安全的文件名"""
        # 替换特殊字符为下划线，避免文件系统路径问题
        safe_name = model_string.replace("/", "_").replace("\\", "_").replace(":", "_")
        return safe_name

    def _generate_bench_config(self, jsonl_line, config_path):
        """生成基准测试配置文件"""
        # 复制原始jsonl_line数据
        bench_data = jsonl_line.copy()

        # 如果有环境依赖配置，将其添加到extra_info的environment_dependency数组的最前面
        if self.environment_dependency:
            # 确保extra_info字段存在
            if "extra_info" not in bench_data:
                bench_data["extra_info"] = {}

            # 获取现有的environment_dependency数组，如果不存在则创建空数组
            existing_env_deps = bench_data["extra_info"].get(
                "environment_dependency", []
            )

            # 将新的环境依赖添加到最前面
            new_env_deps = self.environment_dependency + existing_env_deps
            bench_data["extra_info"]["environment_dependency"] = new_env_deps

            logger.info(
                f"添加环境依赖到bench配置: {len(self.environment_dependency)} 个新依赖项 + {len(existing_env_deps)} 个现有依赖项"
            )

        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(bench_data, f, indent=2, ensure_ascii=False)

        logger.debug(f"生成基准配置文件: {config_path}")
        return config_path

    def _download_file_from_url(self, url, local_path):
        """从URL下载文件到本地路径"""
        try:
            response = requests.get(url, timeout=60)
            response.raise_for_status()

            with open(local_path, "wb") as f:
                f.write(response.content)

            logger.debug(f"成功下载文件: {url} -> {local_path}")
            return True
        except Exception as e:
            logger.error(f"下载文件失败: {url}, 错误: {e}")
            return False

    def _execute_single_task(self, task_info):
        """执行单个任务 - 根据配置选择本地或K8s执行，支持重试机制"""
        base_max_retries = 5
        retry_interval = 5  # 重试间隔默认5秒

        # 先执行一次以判断错误类型
        first_result = None
        try:
            if self.run_local:
                first_result = self._execute_single_task_local(task_info, 1)
            else:
                first_result = self._execute_single_task_k8s(task_info, 1)
        except Exception as e:
            first_result = {
                "task_id": f"failed_{task_info['model_string']}_{task_info['data_id']}",
                "model_string": task_info["model_string"],
                "data_id": task_info["data_id"],
                "success": False,
                "error": str(e),
                "result": {},
                "check_result": {},
            }

        # 判断是否为K8s资源超限错误，如果是则增加重试次数
        is_k8s_resource_limit = self._is_k8s_resource_limit_error(first_result)
        max_retries = (
            base_max_retries * 20 if is_k8s_resource_limit else base_max_retries
        )

        if is_k8s_resource_limit:
            logger.info(f"检测到K8s资源超限错误，将重试次数增加到: {max_retries}")

        # 如果第一次执行成功且不需要重试，直接返回
        if not self._should_retry_task(first_result) and first_result.get(
            "success", False
        ):
            return first_result

        # 否则进入重试循环
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(
                        f"第 {attempt + 1} 次尝试执行任务: {task_info['model_string']}"
                    )
                    # 添加重试间隔
                    time.sleep(retry_interval)

                # 使用第一次的结果或重新执行
                if attempt == 0:
                    result = first_result
                else:
                    if self.run_local:
                        result = self._execute_single_task_local(task_info, attempt + 1)
                    else:
                        result = self._execute_single_task_k8s(task_info, attempt + 1)

                # 检查是否需要重试
                if self._should_retry_task(result):
                    if attempt < max_retries - 1:  # 还有重试机会
                        logger.warning(
                            f"任务执行失败，准备重试: {result.get('task_id', 'unknown')}, "
                            f"失败原因: {self._get_failure_reason(result)}"
                        )
                        continue
                    else:
                        logger.error(
                            f"任务执行失败，已达到最大重试次数: {result.get('task_id', 'unknown')}"
                        )

                return result

            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(
                        f"任务执行异常，准备重试: {task_info['model_string']}, 错误: {e}"
                    )
                    continue
                else:
                    logger.error(
                        f"任务执行异常，已达到最大重试次数: {task_info['model_string']}, 错误: {e}"
                    )
                    # 返回失败结果
                    return {
                        "task_id": f"failed_{task_info['model_string']}_{task_info['data_id']}",
                        "model_string": task_info["model_string"],
                        "data_id": task_info["data_id"],
                        "success": False,
                        "error": f"执行异常，已达到最大重试次数: {str(e)}",
                        "result": {},
                        "check_result": {},
                    }

        return {
            "task_id": f"failed_{task_info['model_string']}_{task_info['data_id']}",
            "model_string": task_info["model_string"],
            "data_id": task_info["data_id"],
            "success": False,
            "error": "未知错误",
            "result": {},
            "check_result": {},
        }

    def _should_retry_task(self, result):
        """判断任务是否需要重试"""
        # 如果任务本身就失败了，需要重试
        if not result.get("success", False):
            return True

        # 检查result.json是否获取到
        result_data = result.get("result", {})
        if not result_data or result_data.get("error"):
            logger.info("result.json未获取到或包含错误，需要重试")
            return True

        # 检查response字段是否以错误开头
        response = result_data.get("response", "")
        if isinstance(response, str):
            response_lower = response.lower().strip()
            error_prefixes = ["错误：", "错误:", "error:"]
            for prefix in error_prefixes:
                if response_lower.startswith(prefix.lower()):
                    logger.info(
                        f"response字段以错误开头，需要重试: {response[:100]}..."
                    )
                    return True

        return False

    def _get_failure_reason(self, result):
        """获取任务失败的原因描述"""
        if not result.get("success", False):
            return result.get("error", "任务执行失败")

        result_data = result.get("result", {})
        if not result_data:
            return "result.json未获取到"

        if result_data.get("error"):
            return f"result.json包含错误: {result_data.get('error')}"

        response = result_data.get("response", "")
        if isinstance(response, str):
            response_lower = response.lower().strip()
            error_prefixes = ["错误：", "错误:", "error:"]
            for prefix in error_prefixes:
                if response_lower.startswith(prefix.lower()):
                    return f"response字段以错误开头: {response[:100]}..."

        return "未知失败原因"

    def _is_k8s_resource_limit_error(self, result):
        """判断是否为K8s资源超限错误"""
        if not result:
            return False

        # 检查错误信息中是否包含资源超限的关键词
        error_msg = result.get("error", "")
        if isinstance(error_msg, str):
            # 检查常见的资源超限错误信息
            resource_limit_keywords = [
                "资源超限",
                "调度失败",
                "resource limit",
                "scheduling failed",
                "insufficient resources",
                "资源不足",
            ]

            error_lower = error_msg.lower()
            for keyword in resource_limit_keywords:
                if keyword.lower() in error_lower:
                    return True

        # 如果是从K8s结果中解析出来的，检查特定的错误代码
        # 从日志中看到的错误：code: 10202, message: '资源超限,调度失败'
        if "10202" in error_msg and "资源超限" in error_msg:
            return True

        return False

    def _execute_single_task_local(self, task_info, attempt_num=1):
        """执行单个任务 - 本地执行"""
        model_string = task_info["model_string"]
        data_id = task_info["data_id"]
        jsonl_line = task_info["jsonl_line"]
        # 创建安全的文件名
        safe_model_name = self._create_safe_filename(model_string)
        task_id = f"{safe_model_name}_{data_id}_{int(time.time())}"
        if attempt_num > 1:
            task_id += f"_retry{attempt_num}"

        logger.info(
            f"开始执行本地任务: {task_id} (模型: {model_string}, 第{attempt_num}次尝试)"
        )

        try:
            # 使用通用方法准备任务环境
            task_env = self._prepare_task_environment(task_id, model_string, jsonl_line)
            task_input_dir = task_env["task_input_dir"]
            task_output_dir = task_env["task_output_dir"]
            task_work_dir = task_env["task_work_dir"]
            task_log_dir = task_env["task_log_dir"]
            log_path = task_log_dir / f"{task_id}.log"

            # 使用boost.sh脚本执行，与容器运行方式保持一致
            # boost.sh脚本在项目根目录的mcphost目录下
            current_file_dir = Path(__file__).parent  # operator目录
            project_root = current_file_dir.parent  # 项目根目录
            boost_script_path = project_root / "mcphost" / "boost.sh"
            mcphost_executable = project_root / "mcphost" / "mcphost"

            if not boost_script_path.exists():
                raise ValueError(f"boost.sh脚本不存在: {boost_script_path}")

            if not mcphost_executable.exists():
                raise ValueError(f"mcphost可执行文件不存在: {mcphost_executable}")

            logger.debug(f"使用boost.sh脚本: {boost_script_path}")
            logger.debug(f"mcphost可执行文件: {mcphost_executable}")

            # 设置环境变量，与容器运行方式保持一致
            env = os.environ.copy()
            env["DATAENG_INPUT_DATA_DIR"] = str(task_input_dir)
            env["DATAENG_OUTPUT_DIR"] = str(task_output_dir)
            # 传递mcphost可执行文件路径给boost.sh脚本
            env["MCPHOST_PATH"] = str(mcphost_executable)
            # 将mcphost目录加入PATH，这样boost.sh可以找到mcphost可执行文件
            mcphost_dir = project_root / "mcphost"
            env["PATH"] = str(mcphost_dir) + ":" + env.get("PATH", "")
            logger.debug(
                f"本地执行设置环境变量 DATAENG_INPUT_DATA_DIR={task_input_dir}"
            )
            logger.debug(f"本地执行设置环境变量 DATAENG_OUTPUT_DIR={task_output_dir}")
            logger.debug(f"本地执行设置环境变量 MCPHOST_PATH={mcphost_executable}")
            logger.debug(f"本地执行设置PATH包含mcphost目录: {mcphost_dir}")

            cmd = ["bash", str(boost_script_path)]
            logger.info(f"执行命令: {' '.join(cmd)}")

            with open(log_path, "w", encoding="utf-8") as log_file:
                process = subprocess.run(
                    cmd,
                    cwd=task_work_dir,  # 在工作目录执行
                    env=env,  # 传递环境变量
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    timeout=LOCAL_TIMEOUT,  # 15分钟超时
                    text=True,
                )

                if process.returncode != 0:
                    logger.error(
                        f"任务执行失败: {task_id}, 返回码: {process.returncode}"
                    )
                    return {
                        "task_id": task_id,
                        "model_string": model_string,
                        "data_id": data_id,
                        "success": False,
                        "error": f"执行失败，返回码: {process.returncode}",
                        "log_path": str(log_path.relative_to(self.final_output_path)),
                        "result": {},
                    }

            # 读取结果文件
            result_data = {}
            result_file_path = task_output_dir / "result.json"
            if result_file_path.exists():
                try:
                    with open(result_file_path, "r", encoding="utf-8") as f:
                        result_data = json.load(f)
                    logger.info(f"成功读取结果文件: {task_id}")
                except Exception as e:
                    logger.error(f"读取结果文件失败: {e}")
                    result_data = {"error": f"读取结果文件失败: {e}"}
            else:
                logger.error(f"结果文件不存在: {result_file_path}")
                result_data = {"error": "结果文件不存在"}

            # 将output目录中的所有.log文件移动到logs目录
            log_files_moved = []
            for log_file in task_output_dir.glob("*.log"):
                try:
                    dest_log_path = task_log_dir / log_file.name
                    shutil.move(str(log_file), str(dest_log_path))
                    log_files_moved.append(str(dest_log_path))
                    logger.info(f"移动日志文件: {log_file} -> {dest_log_path}")
                except Exception as e:
                    logger.warning(f"移动日志文件失败: {log_file}, 错误: {e}")

            # 更新log_urls列表，包含主日志和移动的日志
            all_log_urls = [str(log_path)]  # 主执行日志
            all_log_urls.extend(log_files_moved)  # 添加从output移动的日志

            # 检查脚本现在由boost.sh自动执行，读取检查结果
            check_result = {}
            check_result_path = task_output_dir / "check_result.json"
            if check_result_path.exists():
                try:
                    with open(check_result_path, "r", encoding="utf-8") as f:
                        check_result = json.load(f)
                    logger.info(f"成功读取检查结果文件: {task_id}")
                except Exception as e:
                    logger.error(f"读取检查结果文件失败: {e}")
                    check_result = {"error": f"读取检查结果文件失败: {e}"}
            else:
                if self.check_script_path and self.check_model:
                    logger.warning(f"检查结果文件不存在: {check_result_path}")
                    check_result = {"error": "检查结果文件不存在"}

            logger.info(f"本地任务执行完成: {task_id}, 第{attempt_num}次尝试")
            return {
                "task_id": task_id,
                "model_string": model_string,
                "data_id": data_id,
                "success": True,
                "log_urls": all_log_urls,
                "env_path_url": "",
                "result": result_data,
                "check_result": check_result,
            }

        except subprocess.TimeoutExpired:
            logger.error(f"任务执行超时: {task_id}")
            return {
                "task_id": task_id,
                "model_string": model_string,
                "data_id": data_id,
                "success": False,
                "error": "执行超时",
                "log_path": f"tmp/{task_id}/logs/{task_id}.log",
                "result": {},
                "check_result": {},
            }
        except Exception as e:
            logger.error(f"本地任务执行异常: {task_id}, 错误: {e}")
            return {
                "task_id": task_id,
                "model_string": model_string,
                "data_id": data_id,
                "success": False,
                "error": str(e),
                "log_path": f"tmp/{task_id}/logs/{task_id}.log",
                "result": {},
                "check_result": {},
            }

    def _execute_single_task_k8s(self, task_info, attempt_num=1):
        """执行单个任务 - 通过K8s提交"""
        model_string = task_info["model_string"]
        data_id = task_info["data_id"]
        jsonl_line = task_info["jsonl_line"]
        # 创建安全的文件名
        safe_model_name = self._create_safe_filename(model_string)
        task_id = f"{safe_model_name}_{data_id}_{int(time.time())}"
        if attempt_num > 1:
            task_id += f"_retry{attempt_num}"

        logger.info(
            f"开始执行K8s任务: {task_id} (模型: {model_string}, 第{attempt_num}次尝试)"
        )

        try:
            # 使用通用方法准备任务环境
            task_env = self._prepare_task_environment(task_id, model_string, jsonl_line)
            task_input_dir = task_env["task_input_dir"]
            task_output_dir = task_env["task_output_dir"]
            task_log_dir = task_env["task_log_dir"]
            mcp_config_path = task_env["mcp_config_path"]
            model_config_path = task_env["model_config_path"]
            bench_config_path = task_env["bench_config_path"]

            # 准备K8s输入数据
            k8s_input_data = [
                str(model_config_path),
                str(mcp_config_path),
                str(bench_config_path),
            ]

            # 将临时目录中的所有文件都传递到K8s容器
            for file_path in task_input_dir.rglob("*"):
                if file_path.is_file() and str(file_path) not in k8s_input_data:
                    k8s_input_data.append(str(file_path))
                    logger.debug(f"将文件传递到K8s容器: {file_path}")
            logger.info(f"K8s输入文件传递完成，共 {len(k8s_input_data)} 个文件")

            # 提交K8s作业
            k8s_result = submit_k8s_job(
                image_id=K8S_IMAGE_ID,
                command=K8S_COMMAND,
                input_data=k8s_input_data,
                base_mount_path=K8S_BASE_MOUNT_PATH,
                source_type=K8S_SOURCE_TYPE,
                timeout=K8S_TIMEOUT,
            )

            logger.info(f"K8s提交结果: {task_id}, 结果: {k8s_result}")

            # 检查K8s执行结果
            if k8s_result.get("code") != 0:
                error_msg = k8s_result.get("msg", "K8s执行失败")
                logger.error(f"K8s任务执行失败: {task_id}, 错误: {error_msg}")
                return {
                    "task_id": task_id,
                    "model_string": model_string,
                    "data_id": data_id,
                    "success": False,
                    "error": f"K8s执行失败: {error_msg}",
                    "result": {},
                    "check_result": {},
                }

            # 处理K8s返回的输出文件
            k8s_data = k8s_result.get("data", {})
            output_urls = k8s_data.get("output_url", [])
            result_data = {}
            check_result = {}
            log_urls = []
            env_path_url = ""
            result_file_found = False

            # 处理输出文件URL
            for url in output_urls:
                if (
                    "result.json" in url.lower()
                    and "check_result.json" not in url.lower()
                ):
                    # 这是结果文件，下载到本地output目录并解析
                    local_result_path = task_output_dir / "result.json"
                    logger.info(f"识别到结果文件{url}")
                    result_file_found = True

                    if self._download_file_from_url(url, local_result_path):
                        try:
                            with open(local_result_path, "r", encoding="utf-8") as f:
                                result_data = json.load(f)
                            logger.debug(
                                f"成功解析结果文件，数据大小: {len(str(result_data))} 字符"
                            )
                        except Exception as e:
                            logger.error(f"解析结果文件失败: {e}")
                            result_data = {"error": f"解析结果文件失败: {e}"}
                    else:
                        logger.error(f"下载结果文件失败: {url}")
                        result_data = {"error": "下载结果文件失败"}

                elif "check_result.json" in url.lower():
                    # 这是检查结果文件，下载到本地output目录并解析
                    local_check_result_path = task_output_dir / "check_result.json"
                    logger.info(f"识别到检查结果文件{url}")

                    if self._download_file_from_url(url, local_check_result_path):
                        try:
                            with open(
                                local_check_result_path, "r", encoding="utf-8"
                            ) as f:
                                check_result = json.load(f)
                            logger.debug(
                                f"成功解析检查结果文件，数据大小: {len(str(check_result))} 字符"
                            )
                        except Exception as e:
                            logger.error(f"解析检查结果文件失败: {e}")
                            check_result = {"error": f"解析检查结果文件失败: {e}"}
                    else:
                        logger.error(f"下载检查结果文件失败: {url}")
                        check_result = {"error": "下载检查结果文件失败"}

                elif "env.tar.gz" in url.lower():
                    # 这是环境目录tar文件，下载到本地用于排查问题，但结果中保留原始URL
                    local_env_path = task_output_dir / "env.tar.gz"
                    logger.info(f"识别到环境tar文件{url}")
                    if self._download_file_from_url(url, local_env_path):
                        logger.debug(f"环境tar文件下载成功: {local_env_path}")
                    else:
                        logger.warning(f"环境tar文件下载失败: {url}")

                    # 结果中保留原始URL（用于输出）
                    env_path_url = url
                    logger.debug(f"保存环境tar文件URL到结果: {env_path_url}")

                elif ".log" in url.lower():
                    # 这是日志文件，下载到本地用于排查问题，但结果中保留原始URL
                    log_filename = Path(url).name
                    local_log_path = task_log_dir / log_filename
                    logger.info(f"识别到日志文件{url}")
                    if self._download_file_from_url(url, local_log_path):
                        logger.debug(f"日志文件下载成功: {local_log_path}")
                    else:
                        logger.warning(f"日志文件下载失败: {url}")

                    # 结果中保留原始URL（用于输出）
                    log_urls.append(url)
                    logger.debug(f"保存日志URL到结果: {url}")
                else:
                    logger.info(f"忽略URL: {url}")

            # 检查执行状态：基于code、msg和是否找到结果文件来判断
            success = (
                k8s_result.get("code") == 0
                and k8s_result.get("msg") == "success"
                and result_file_found
            )

            if not success:
                code = k8s_result.get("code")
                msg = k8s_result.get("msg")
                logger.error(
                    f"K8s任务执行异常: {task_id}, code: {code}, msg: {msg}, 找到结果文件: {result_file_found}"
                )
            else:
                logger.info(f"K8s任务执行完成: {task_id}, 第{attempt_num}次尝试")

            return {
                "task_id": task_id,
                "model_string": model_string,
                "data_id": data_id,
                "success": success,
                "log_urls": log_urls,
                "env_path_url": env_path_url,
                "result": result_data,
                "check_result": check_result,
            }

        except Exception as e:
            logger.error(f"K8s任务执行异常: {task_id}, 错误: {e}")
            return {
                "task_id": task_id,
                "model_string": model_string,
                "data_id": data_id,
                "success": False,
                "error": str(e),
                "result": {},
                "check_result": {},
            }

    def _process_jsonl_file(self, input_file):
        """处理单个JSONL文件"""
        logger.info(f"处理JSONL文件: {input_file}")

        # 读取JSONL文件
        jsonl_lines = []
        with open(input_file, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        jsonl_data = json.loads(line)
                        jsonl_lines.append(jsonl_data)
                    except json.JSONDecodeError as e:
                        logger.error(f"解析JSONL第{line_num}行失败: {e}")

        logger.info(f"共读取 {len(jsonl_lines)} 行数据")

        # 如果启用失败重试模式，筛选失败的记录
        if self.retry_failed_only:
            original_count = len(jsonl_lines)
            failed_lines = []

            for jsonl_line in jsonl_lines:
                # 检查是否为失败的记录
                execution_result = jsonl_line.get("execution_result", {})
                if not execution_result.get("success", False):
                    # 清理原有的结果字段，准备重新执行
                    jsonl_line.pop("bench_result", None)
                    jsonl_line.pop("check_result", None)
                    jsonl_line.pop("execution_result", None)
                    jsonl_line.pop("bench_env_path_url", None)
                    failed_lines.append(jsonl_line)
                    logger.debug(
                        f"标记失败记录进行重试: data_id={jsonl_line.get('data_id', 'unknown')}"
                    )

            jsonl_lines = failed_lines
            logger.info(
                f"失败重试模式: 从 {original_count} 条记录中筛选出 {len(jsonl_lines)} 条失败记录进行重试"
            )

            if len(jsonl_lines) == 0:
                logger.info("没有找到失败的记录，跳过处理")
                return {model: [] for model in self.eval_model_list}

        # 准备所有任务
        all_tasks = []
        for jsonl_line in jsonl_lines:
            for model_string in self.eval_model_list:
                task_info = {
                    "model_string": model_string,
                    "data_id": jsonl_line.get("data_id", 0),
                    "jsonl_line": jsonl_line,
                }
                all_tasks.append(task_info)

        logger.info(f"总共需要执行 {len(all_tasks)} 个任务")

        # 并发执行任务
        results_by_model = {model: [] for model in self.eval_model_list}

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self._execute_single_task, task): task
                for task in all_tasks
            }

            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    model_string = result["model_string"]

                    # 将结果添加到原始JSONL数据中
                    original_data = task["jsonl_line"].copy()
                    execution_info = {}
                    original_data["bench_result"] = {}
                    for k, v in result.items():
                        if k == "result":
                            if v is not None:
                                original_data["bench_result"] = v
                        elif k == "check_result":
                            # 将check_result添加为与bench_result同级的字段
                            if v:  # 只有当check_result不为空时才添加
                                original_data["check_result"] = v
                        elif k == "env_path_url":
                            # 将env_path_url添加为与bench_result同级的字段
                            if v:  # 只有当env_path_url不为空时才添加
                                # 根据执行方式处理路径
                                if self.run_local:
                                    # 本地执行：转换为相对路径
                                    try:
                                        abs_path = Path(v)
                                        if abs_path.is_absolute() and abs_path.exists():
                                            rel_path = abs_path.relative_to(
                                                self.final_output_path
                                            )
                                            original_data["bench_env_path_url"] = (
                                                f"./{rel_path}"
                                            )
                                        else:
                                            original_data["bench_env_path_url"] = v
                                    except (ValueError, OSError):
                                        # 如果转换失败，保留原值
                                        original_data["bench_env_path_url"] = v
                                else:
                                    # K8s执行：保留原始URL
                                    original_data["bench_env_path_url"] = v
                        elif k == "log_urls":
                            # 处理日志URL列表
                            if v:
                                if self.run_local:
                                    # 本地执行：转换为相对路径
                                    processed_log_urls = []
                                    for log_url in v:
                                        try:
                                            abs_path = Path(log_url)
                                            if (
                                                abs_path.is_absolute()
                                                and abs_path.exists()
                                            ):
                                                rel_path = abs_path.relative_to(
                                                    self.final_output_path
                                                )
                                                processed_log_urls.append(
                                                    f"./{rel_path}"
                                                )
                                            else:
                                                processed_log_urls.append(log_url)
                                        except (ValueError, OSError):
                                            # 如果转换失败，保留原值
                                            processed_log_urls.append(log_url)
                                    execution_info[k] = processed_log_urls
                                else:
                                    # K8s执行：保留原始URL列表
                                    execution_info[k] = v
                        else:
                            execution_info[k] = v
                    original_data["execution_result"] = execution_info
                    results_by_model[model_string].append(original_data)
                    logger.info(
                        f"任务完成: {result['task_id']}, 成功: {result['success']}"
                    )

                except Exception as e:
                    logger.error(f"任务执行异常: {e}")

        return results_by_model

    def _run(self):
        """
        此方法是算子执行的核心业务逻辑
        """
        logger.info("run executing...")

        # 处理每个JSONL输入文件
        for input_file in self.jsonl_files:
            logger.info(f"开始处理JSONL文件: {input_file}")

            # 获取文件名（不含扩展名）
            file_stem = Path(input_file).stem

            # 处理JSONL文件
            results_by_model = self._process_jsonl_file(input_file)

            # 为每个模型生成输出文件
        for model_string, results in results_by_model.items():
            # 创建安全的文件名
            safe_model_name = self._create_safe_filename(model_string)

            if self.retry_failed_only:
                # 失败重试模式：需要与原文件合并
                output_filename = f"{file_stem}_{safe_model_name}_retry_results.jsonl"

                # 读取原始文件中的所有记录
                original_file_path = None
                for existing_file in self.jsonl_files:
                    if file_stem in Path(existing_file).stem:
                        # 尝试查找对应的原始结果文件
                        original_result_file = (
                            Path(existing_file).parent
                            / f"{file_stem}_{safe_model_name}_results.jsonl"
                        )
                        if original_result_file.exists():
                            original_file_path = original_result_file
                            break

                # 合并结果
                final_results = []
                original_records = {}

                # 读取原始文件中的记录
                if original_file_path and original_file_path.exists():
                    with open(original_file_path, "r", encoding="utf-8") as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                try:
                                    record = json.loads(line)
                                    data_id = record.get("data_id")
                                    original_records[data_id] = record
                                except json.JSONDecodeError:
                                    pass
                    logger.info(
                        f"从原始文件读取 {len(original_records)} 条记录: {original_file_path}"
                    )

                # 用新的执行结果更新原始记录
                updated_count = 0
                for result in results:
                    data_id = result.get("data_id")
                    if data_id in original_records:
                        # 更新原始记录
                        original_records[data_id] = result
                        updated_count += 1
                        logger.debug(f"更新记录: data_id={data_id}")
                    else:
                        # 新记录
                        original_records[data_id] = result

                # 生成最终结果列表（保持原始顺序）
                final_results = list(original_records.values())
                logger.info(
                    f"失败重试模式: 更新了 {updated_count} 条记录，最终输出 {len(final_results)} 条记录"
                )

            else:
                # 正常模式：直接使用新结果
                output_filename = f"{file_stem}_{safe_model_name}_results.jsonl"
                final_results = results

            # 输出文件放置到最终输出目录
            output_path = self.final_output_path / output_filename

            # 写入结果JSONL文件
            with open(output_path, "w", encoding="utf-8") as f:
                for result in final_results:
                    f.write(json.dumps(result, ensure_ascii=False) + "\n")

            self.output_files.append(str(output_path))
            logger.info(
                f"生成输出文件: {output_path}, 包含 {len(final_results)} 条记录"
            )

        # 生成汇总报告
        summary_report = self._generate_summary_report(self.output_files)
        summary_path = self.final_output_path / "summary_report.json"
        with open(summary_path, "w", encoding="utf-8") as f:
            json.dump(summary_report, f, indent=2, ensure_ascii=False)

        self.output_files.append(str(summary_path))
        logger.info(f"生成汇总报告: {summary_path}")

        return self.output_files

    def _generate_summary_report(self, output_files):
        """生成汇总报告"""
        summary = {
            "total_models": len(self.eval_model_list),
            "models": self.eval_model_list,
            "total_jsonl_files": len(self.jsonl_files),
            "total_custom_mcp_servers": len(self.custom_mcp_servers),
            "jsonl_files": self.jsonl_files,
            "custom_mcp_servers": (
                list(self.custom_mcp_servers.keys()) if self.custom_mcp_servers else []
            ),
            "total_output_files": len(output_files) - 1,  # 减去summary文件本身
            "output_files": output_files[:-1],  # 不包含summary文件
            "execution_summary": {},
            "check_summary": {},
        }

        # 统计每个模型的执行和检查情况
        for model_string in self.eval_model_list:
            # 创建安全的文件名进行文件匹配
            safe_model_name = self._create_safe_filename(model_string)
            model_files = [
                f for f in output_files if f"_{safe_model_name}_results.jsonl" in f
            ]

            # 初始化执行统计
            total_tasks = 0
            successful_tasks = 0

            # 初始化检查统计
            check_total_count = 0
            check_success_count = 0
            check_failure_count = 0
            check_error_count = 0
            check_other_count = 0

            for file_path in model_files:
                if Path(file_path).exists():
                    with open(file_path, "r", encoding="utf-8") as f:
                        for line in f:
                            if line.strip():
                                try:
                                    data = json.loads(line)
                                    total_tasks += 1
                                    # 统计执行结果
                                    exec_result = data.get("execution_result", {})
                                    if exec_result.get("success", False):
                                        successful_tasks += 1

                                    # 统计检查结果
                                    check_result = data.get("check_result", {})
                                    if (
                                        check_result
                                        and "overall_result" in check_result
                                    ):
                                        check_total_count += 1
                                        overall_result = check_result["overall_result"]
                                        if "succ" in overall_result.lower():
                                            check_success_count += 1
                                        elif "fail" in overall_result.lower():
                                            check_failure_count += 1
                                        elif "err" in overall_result.lower():
                                            check_error_count += 1
                                        else:
                                            check_other_count += 1
                                except:
                                    pass

            # 添加执行摘要
            summary["execution_summary"][model_string] = {
                "total_tasks": total_tasks,
                "successful_tasks": successful_tasks,
                "success_rate": (
                    successful_tasks / total_tasks if total_tasks > 0 else 0
                ),
                "output_files": model_files,
            }

            # 添加检查摘要
            if check_total_count > 0:
                success_rate = (check_success_count / check_total_count) * 100
                summary["check_summary"][model_string] = {
                    "total_count": check_total_count,
                    "success_count": check_success_count,
                    "failure_count": check_failure_count,
                    "error_count": check_error_count,
                    "other_count": check_other_count,
                    "success_rate": f"{success_rate:.2f}%",
                }

        return summary

    def _teardown(self):
        """
        算子核心业务逻辑执行完成后的收尾工作
        """
        logger.info("teardown executing...")

        # 将输出文件列表写入到common_tool (使用相对路径)
        if self.output_files:
            # 转换为相对路径，避免路径重复问题
            relative_output_files = []
            for output_file in self.output_files:
                # 获取相对于最终输出目录的相对路径
                rel_path = Path(output_file).relative_to(self.final_output_path)
                relative_output_files.append(str(rel_path))

            common_tool.set_output(relative_output_files)
            logger.info(f"设置输出文件列表: {len(relative_output_files)} 个文件")
            for i, (abs_path, rel_path) in enumerate(
                zip(self.output_files, relative_output_files)
            ):
                logger.info(f"  输出文件 {i+1}: {abs_path} -> {rel_path}")
        else:
            logger.warning("没有生成输出文件")

        # 清理临时配置文件
        # if self.input_dir.exists():
        #     shutil.rmtree(self.input_dir)
        #     logger.info("清理临时配置文件")


if __name__ == "__main__":
    user_op = Operator(sys.argv)
    user_op.execute()
