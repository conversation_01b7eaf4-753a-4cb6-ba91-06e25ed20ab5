model_providers:
  assistant_api:
    backend: yiyan
    authorization: testldx/1722333193666/9c2285103869105c43191fd48abb420eadf6286de45c7576e3bd3130168a606f
    assistant_server_ip: *************
    assistant_server_port: 8204
    app_id: "123456"
    model_list:
      - ERNIE-4.5-single-custom-yiyanweb
  uni_crawl:
    openai_url: http://*************:8201/api/real_time/v1
    openai_api_key: sk-123
    model_list:
      - qwen3-235b-a22b
      - deepseek-r1-0528
      - doubao-1-5-thinking-pro-250415
      - doubao-seed-1-6-thinking-250615
      - gpt-4.1
      - deepseek-v3
      - "*"
  qianfan:
    openai_url: http://*************:8205/v2
    openai_api_key: bce-v3/ALTAK-seG05tfjoB4V83dl1I60P/df902ea834c2c21559ac5feb3a5720dc175c9733
    model_list:
      - ernie-x1-turbo-32k
      - "*"

mcp_servers:
  filesystem:
    command: "npx"
    args: ["--no-install", "-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"]

  sqlite:
    command: "uvx"
    args: ["--offline", "mcp-server-sqlite==2025.4.25", "--db-path", "./demo.db"]

  playwright:
    command: "npx"
    args:
      - "-y"
      - "@playwright/mcp@0.0.27"
      - "--headless"
      - "--browser"
      - "chromium"
      - "--no-sandbox"
      - "--executable-path"
      - "/home/<USER>/ms-playwright/chromium-1179/chrome-linux/chrome"

  firecrawl-mcp:
    command: "npx"
    args: ["-y", "firecrawl-mcp@1.11.0"]
    env:
      FIRECRAWL_API_KEY: "fc-0f8bcd5338c7464a8509eff5e5972aac"

  amap-maps:
    type: "sse"
    url: "https://mcp.api-inference.modelscope.cn/sse/bcbecb781a084a"

# MCP服务器映射规则 - 支持多种写法映射到标准名称
mcp_server_mapping:
  # filesystem相关映射
  filesystem: filesystem
  file_system: filesystem
  fs: filesystem

  # sqlite相关映射
  sqlite: sqlite
  sqllite: sqlite
  sql_lite: sqlite
  sql: sqlite
  database: sqlite

  # playwright相关映射
  playwright: playwright
  browser: playwright
  web: playwright

  # firecrawl相关映射
  firecrawl: firecrawl-mcp
  firecrawl-mcp: firecrawl-mcp
  crawler: firecrawl-mcp

  # amap相关映射
  amap: amap-maps
  amap-maps: amap-maps
  map: amap-maps
  maps: amap-maps
