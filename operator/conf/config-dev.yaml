model_providers:
  assistant_api:
    backend: yiyan
    authorization: testldx/1722333193666/9c2285103869105c43191fd48abb420eadf6286de45c7576e3bd3130168a606f
    # assistant_server_ip: *************
    # assistant_server_port: 8361
    assistant_server_ip: *************
    assistant_server_port: 8204
    app_id: "123456"
    model_list:
      - ERNIE-4.5-single-custom-yiyanweb
  uni_crawl:
    # 测试环境
    # openai_url: http://************:8081/api/real_time/v1
    # openai_url: http://*************:8141/api/real_time/v1
    # 生产环境
    openai_url: http://*************:8601/api/real_time/v1
    # openai_url: http://*************:8201/api/real_time/v1
    openai_api_key: sk-123
    model_list:
      - qwen3-235b-a22b
      - deepseek-r1-0528
      - doubao-1-5-thinking-pro-250415
      - doubao-seed-1-6-thinking-250615
      - gpt-4.1
      - deepseek-v3
      - "*"
  qianfan:
    # openai_url: http://advanced-schedular-qianfan.dev.wenxinfactory-svc.appspace.baidu.com/v2
    openai_url: http://*************:8205/v2
    openai_api_key: bce-v3/ALTAK-seG05tfjoB4V83dl1I60P/df902ea834c2c21559ac5feb3a5720dc175c9733
    model_list:
      - ernie-x1-turbo-32k
      - "*"
  anquan:
    openai_url: http://yy.dbh.baidu-int.com
    openai_api_key: sk-cgozV3ZWXICnmsMLXH80elXQSbNxqgVAGvjUvpmCFONnmRhj
    model_list:
      - qwen3-235b-a22b
      - deepseek-r1-0528
      - doubao-1-5-thinking-pro-250415
      - gpt-4.1
      - deepseek-v3
      - "*"
  openai:
    openai_url: <OPENAI_API_BASE>
    openai_api_key: <OPENAI_API_KEY>
    model_list:
      - "*"

mcp_servers:
  filesystem:
    command: "npx"
    args: ["--no-install", "-y", "@modelcontextprotocol/server-filesystem@2025.3.28", "./"]

  sqlite:
    command: "uvx"
    args: ["--offline", "mcp-server-sqlite==2025.4.25", "--db-path", "./demo.db"]

  playwright:
    command: "npx"
    args:,

  firecrawl-mcp:
    command: "npx"
    args: ["-y", "firecrawl-mcp@1.11.0"]
    env:
      # FIRECRAWL_API_KEY: "fc-5567856f258c40de87a1f8734d418d12"
      FIRECRAWL_API_KEY: "fc-078d1927da3c4c4b88b3c01935a5e351"
      # FIRECRAWL_API_KEY: "fc-0f8bcd5338c7464a8509eff5e5972aac"
      # FIRECRAWL_API_KEY: "fc-41275342998d40c3b63b9e14177cc6da"
  amap-maps:
    type: "sse"
    url: "https://mcp.api-inference.modelscope.cn/sse/bcbecb781a084a"

# MCP服务器映射规则 - 支持多种写法映射到标准名称
mcp_server_mapping:
  # filesystem相关映射
  filesystem: filesystem
  file_system: filesystem
  fs: filesystem

  # sqlite相关映射
  sqlite: sqlite
  sqllite: sqlite
  sql_lite: sqlite
  sql: sqlite
  database: sqlite

  # playwright相关映射
  playwright: playwright

  # firecrawl相关映射
  firecrawl: firecrawl-mcp
  firecrawl-mcp: firecrawl-mcp
  crawler: firecrawl-mcp

  # amap相关映射
  amap: amap-maps
  amap-maps: amap-maps
