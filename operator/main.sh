#!/bin/sh
############################################################################################
## -----算子启动主脚本: 准备python环境, 安装依赖包, 运行算子主逻辑self_define_operator.py文件----- ##
# 如无特殊需求, 用户不用修改该文件
############################################################################################
echo 'start main.sh'
export LANG=en_US.UTF-8

DATAENG_CONFIG="dataeng.yml"
REQUIREMENTS_FILE="requirements.txt"

# 相关参数获取，这些业务不需要关心，直接复制即可
FLOW_ID=$1
DAG_NAME=$2
RUN_ID=$3
INPUT=$4

function parse_yaml() {
    local yaml_file=$1
    local prefix=$2
    local s
    local w
    local fs
    s='[[:space:]]*'
    w='[a-zA-Z0-9_.-]*'
    fs="$(echo @|tr @ '\034')"
    (
        sed -ne '/^--/s|--||g; s|\"|\\\"|g; s/\s*$//g;' \
            -e "/#.*[\"\']/!s| #.*||g; /^#/s|#.*||g;" \
            -e  "s|^\($s\)\($w\)$s:$s\"\(.*\)\"$s\$|\1$fs\2$fs\3|p" \
            -e "s|^\($s\)\($w\)$s[:-]$s\(.*\)$s\$|\1$fs\2$fs\3|p" |
        awk -F"$fs" '{
            indent = length($1)/2;
            if (length($2) == 0) { conj[indent]="+";} else {conj[indent]="";}
            vname[indent] = $2;
            for (i in vname) {if (i > indent) {delete vname[i]}}
                if (length($3) > 0) {
                    vn=""; for (i=0; i<indent; i++) {vn=(vn)(vname[i])("_")}
                    printf("%s%s%s%s=(\"%s\")\n", "'"$prefix"'",vn, $2, conj[indent-1],$3);
                }
            }' |
        sed -e 's/_=/+=/g' \
            -e '/\..*=/s|\.|_|' \
            -e '/\-.*=/s|\-|_|'
    ) < "$yaml_file"
}

# 准备python环境: (默认使用平台自带的python3.6版本), 用户不需要修改
# 根据算子包的dataeng.yml配置文件中的base_python_version配置, 安装对应版本python
# 根据requirements.txt安装依赖包
if [ -f "$DATAENG_CONFIG" ];then
  echo "dataeng.yml配置文件存在, 根据配置安装python环境"
  eval "$(parse_yaml $DATAENG_CONFIG)"
  echo "python_version: $base_python_version"
  if [[ "$base_python_version" =~ ^python.* ]];then
    cp -r ${HOME}/tools/${base_python_version}/ ./
    PYTHON="./${base_python_version}/bin/python3"
  elif [[ "$base_python_version" =~ ^http|ftp.* ]];then
    curl -s $base_python_version -o local.tar.gz
    mkdir python_pkg && tar zxf local.tar.gz -C ./python_pkg
    python_location=(`find python_pkg -depth -name "python"`)
    PYTHON="./${python_location[0]}"
  else
    echo "base_python_version配置错误, 请检查dataeng.yml文件"
    exit 1
  fi
else
  echo "dataeng.yml配置文件不存在, 使用默认的python3.6环境"
  cp -r ${HOME}/tools/python3.6/ ./
  PYTHON="./python3.6/bin/python3"
fi

if [ -f "$REQUIREMENTS_FILE" ];then
  echo "requirements.txt存在, 安装依赖包"
  $PYTHON -m pip install -r $REQUIREMENTS_FILE --no-cache-dir
fi

export DATAENG_K8S_SERVICE_HOST=************:8401
export DATAENG_BOS_AK=ALTAKwPzBBVWHFtDyVhB2xFLTj
export DATAENG_BOS_SK=2ae415575f1d4522beb6d1c5c269ae9b
export DATAENG_BOS_BUCKET=dataeng-bos-prod

# 执行算子主逻辑的命令，这里只需要根据实际情况修改self_define_operator.py这个文件名即可（注意这里需要根据实际情况填写相对路径）
$PYTHON self_define_operator.py $FLOW_ID $DAG_NAME $RUN_ID $INPUT