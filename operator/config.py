# !/usr/bin/env python3
"""
配置读取                                                                                                             
"""
# -*- coding: utf-8 -*-
from dataeng_sdk import common_tool
import yaml
import os
import os.path as osp

global config
logger = common_tool.get_logger(__name__)

env = os.getenv('AIRFLOW_PLATFORM_ENV', "dev")
if "config" not in dir():
    current_dir = osp.dirname(osp.abspath(__file__))
    if "dev" in env or "test" in env:
        with open(osp.join(current_dir, "conf/config-dev.yaml"), 'r', encoding='utf8') as file:
            config = yaml.safe_load(file)
    elif "sandbox" in env:
        with open(osp.join(current_dir, "conf/config-sandbox.yaml"), 'r', encoding='utf8') as file:
            config = yaml.safe_load(file)
    else:
        with open(osp.join(current_dir, "conf/config-prod.yaml"), 'r', encoding='utf8') as file:
            config = yaml.safe_load(file)
    logger.info(f"env now:{env}, finish loading config")