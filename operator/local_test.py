#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
local_test.py
本地测试MCP配置生成器和批量执行器
"""
import argparse
import sys
import os
import json
import tempfile
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from config import config
from self_define_operator import Operator

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="MCP批量评估工具")
    parser.add_argument("--input-file", help="输入的JSONL文件路径（可选，如果未指定会从--input-dir中自动查找）")
    parser.add_argument(
        "--models",
        required=True,
        help="要评估的模型列表，用逗号分隔，如: gpt41_pool,qwen3235ba22b_pool",
    )
    parser.add_argument(
        "--output-dir", default="output", help="输出目录路径（默认: output）"
    )
    parser.add_argument(
        "--max-workers",
        type=int,
        default=None,
        help="最大并发工作进程数（默认从配置文件读取）",
    )
    parser.add_argument(
        "--run-mode",
        choices=["local", "k8s"],
        default="local",
        help="运行模式: local=本地运行, k8s=K8s运行（默认: local）",
    )
    parser.add_argument(
        "--check-model",
        help="检查用的模型，格式如: uni_crawl:gpt-4, openai:gpt-3.5-turbo（可选）",
    )
    parser.add_argument(
        "--check-script",
        help="检查脚本文件路径（可以是绝对路径或相对路径，默认在当前目录查找check.py）",
    )
    parser.add_argument(
        "--mcpservers",
        help="自定义mcpservers.json文件路径（可选，可以是绝对路径或相对路径）",
    )
    parser.add_argument(
        "--environment-dependency",
        help="环境依赖配置文件路径（可选，固定名称为environment_dependency.json）",
    )
    parser.add_argument(
        "--retry-failed-only",
        action="store_true",
        help="仅重试失败任务（默认: false）",
    )
    parser.add_argument(
        "--input-dir",
        help="输入目录路径，该目录下的所有相关文件会被自动加载（可选）",
    )
    return parser.parse_args()


def parse_models_to_list(models_str):
    """将模型字符串解析为列表"""
    return [model.strip() for model in models_str.split(",") if model.strip()]


def main():
    """主函数"""
    args = parse_arguments()

    # 验证输入文件或目录
    input_file = None
    if args.input_file:
        input_file = Path(args.input_file)
        if not input_file.exists():
            print(f"错误: 输入文件不存在: {input_file}")
            sys.exit(1)
    elif args.input_dir:
        # 从输入目录中加载所有文件
        input_dir = Path(args.input_dir)
        if not input_dir.exists():
            print(f"错误: 输入目录不存在: {input_dir}")
            sys.exit(1)
        if not input_dir.is_dir():
            print(f"错误: 输入路径不是目录: {input_dir}")
            sys.exit(1)
        
        # 获取目录中的所有文件（不包括子目录）
        all_files = [f for f in input_dir.iterdir() if f.is_file()]
        if not all_files:
            print(f"错误: 输入目录 {input_dir} 中没有文件")
            sys.exit(1)
        
        # 查找JSONL文件作为主输入文件
        jsonl_files = [f for f in all_files if f.suffix.lower() == '.jsonl']
        if not jsonl_files:
            print(f"错误: 在输入目录 {input_dir} 中未找到JSONL文件")
            sys.exit(1)
        
        # 使用第一个找到的JSONL文件作为主输入文件
        input_file = jsonl_files[0]
        print(f"自动选择JSONL文件: {input_file}")
        
        if len(jsonl_files) > 1:
            print(f"注意: 找到多个JSONL文件，使用第一个: {input_file}")
            for other_file in jsonl_files[1:]:
                print(f"  其他JSONL文件: {other_file}")
        
        # 将目录中的所有文件添加到input_files中
        input_files = [str(f) for f in all_files]
        print(f"加载目录中的所有文件 ({len(input_files)} 个):")
        for file_path in sorted(input_files):
            file_type = "JSONL数据" if Path(file_path).suffix.lower() == '.jsonl' else \
                       "MCP服务器配置" if "mcpservers.json" in Path(file_path).name.lower() else \
                       "环境依赖配置" if "environment_dependency.json" in Path(file_path).name.lower() else \
                       "检查脚本" if "check.py" in Path(file_path).name.lower() else \
                       "MCP服务器脚本" if (Path(file_path).suffix == ".py" and 
                                      ("mock_mcp" in Path(file_path).name.lower() or 
                                       "mcp_server" in Path(file_path).name.lower())) else \
                       "其他文件"
            print(f"  - {Path(file_path).name} ({file_type})")
    else:
        print("错误: 请指定 --input-file 或 --input-dir 参数")
        sys.exit(1)

    # 为本地测试设置必要的环境变量
    # 这些环境变量是dataeng_sdk需要的，但在本地测试环境中通常不存在
    output_dir = Path(args.output_dir).absolute()
    if not os.environ.get("AIRFLOW_DATASTORE_DIR"):
        os.environ["AIRFLOW_DATASTORE_DIR"] = str(output_dir)
        print(f"设置本地测试环境变量 AIRFLOW_DATASTORE_DIR: {output_dir}")
    
    if not os.environ.get("AIRFLOW_TMP_DIR"):
        temp_dir = output_dir / "tmp"
        temp_dir.mkdir(parents=True, exist_ok=True)
        os.environ["AIRFLOW_TMP_DIR"] = str(temp_dir)
        print(f"设置本地测试环境变量 AIRFLOW_TMP_DIR: {temp_dir}")
    
    # 设置 AIRFLOW_DATASTORE_URL，用于 common_tool.set_output()
    if not os.environ.get("AIRFLOW_DATASTORE_URL"):
        # 使用 file:// 协议指向本地输出目录
        datastore_url = f"file://{output_dir}"
        os.environ["AIRFLOW_DATASTORE_URL"] = datastore_url
        print(f"设置本地测试环境变量 AIRFLOW_DATASTORE_URL: {datastore_url}")

    # 如果是本地运行模式，验证mcphost环境变量
    if args.run_mode == "local":
        mcphost_path = os.environ.get("MCPHOST_PATH") or os.environ.get(
            "MCPHOST_EXECUTABLE"
        )
        if not mcphost_path:
            print("错误: 未设置MCPHOST_PATH或MCPHOST_EXECUTABLE环境变量")
            print("请确保Makefile正确设置了mcphost可执行文件路径")
            sys.exit(1)

        mcphost_file = Path(mcphost_path)
        if not mcphost_file.exists():
            print(f"错误: mcphost可执行文件不存在: {mcphost_file}")
            sys.exit(1)

        print(f"使用mcphost: {mcphost_file}")

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    output_dir = output_dir.absolute()

    # 解析模型列表
    models_list = parse_models_to_list(args.models)

    # 准备算子参数
    if args.input_dir:
        # 使用input_dir时，input_files已经在前面设置了
        pass
    else:
        # 使用input_file时，设置input_files
        input_files = [str(input_file.absolute())]

    # 如果指定了自定义mcpservers.json，添加到输入文件列表
    if args.mcpservers:
        mcpservers_path = Path(args.mcpservers)
        # 如果是相对路径，相对于当前工作目录解析
        if not mcpservers_path.is_absolute():
            mcpservers_path = Path.cwd() / mcpservers_path
        
        if not mcpservers_path.exists():
            print(f"错误: 自定义mcpservers.json文件不存在: {mcpservers_path}")
            sys.exit(1)
        input_files.append(str(mcpservers_path.absolute()))
        print(f"使用自定义mcpservers.json: {mcpservers_path}")

    # 如果指定了环境依赖配置文件，添加到输入文件列表
    if args.environment_dependency:
        env_dep_path = Path(args.environment_dependency)
        # 如果是相对路径，相对于当前工作目录解析
        if not env_dep_path.is_absolute():
            env_dep_path = Path.cwd() / env_dep_path
        
        if not env_dep_path.exists():
            print(f"错误: 环境依赖配置文件不存在: {env_dep_path}")
            sys.exit(1)
        input_files.append(str(env_dep_path.absolute()))
        print(f"使用环境依赖配置文件: {env_dep_path}")

    # 如果指定了检查脚本，添加到输入文件列表
    if args.check_script:
        check_script_path = Path(args.check_script)
        # 如果是相对路径，相对于当前工作目录解析
        if not check_script_path.is_absolute():
            check_script_path = Path.cwd() / check_script_path
        
        if not check_script_path.exists():
            print(f"错误: 检查脚本文件不存在: {check_script_path}")
            sys.exit(1)
        input_files.append(str(check_script_path.absolute()))
        print(f"使用检查脚本: {check_script_path}")
    elif args.check_model:
        # 如果指定了检查模型但没有指定脚本，尝试查找默认的check.py
        possible_check_scripts = [
            Path(current_dir) / "check.py",  # operator目录下
            Path(current_dir).parent / "check.py",  # 项目根目录下
            Path.cwd() / "check.py",  # 当前工作目录下
        ]
        
        check_script_found = False
        for check_script_path in possible_check_scripts:
            if check_script_path.exists():
                input_files.append(str(check_script_path.absolute()))
                print(f"使用默认检查脚本: {check_script_path}")
                check_script_found = True
                break
        
        if not check_script_found:
            print(f"警告: 指定了检查模型但未找到检查脚本")
            print(f"尝试的路径:")
            for path in possible_check_scripts:
                print(f"  - {path}")
            print(f"请使用--check-script指定脚本路径")

    # 构建params参数，传递给算子
    params = {
        "models": models_list,  # 直接传递数组格式
        "run_local": args.run_mode == "local",  # 根据命令行参数设置运行模式
        "retry_failed_only": args.retry_failed_only,  # 根据命令行参数设置失败重试模式
    }

    if args.max_workers is not None:
        params["max_workers"] = args.max_workers
    
    if args.check_model:
        params["check_model"] = args.check_model

    print(f"输入文件: {input_files}")
    print(f"输入目录: {args.input_dir or '未配置'}")
    print(f"输出目录: {output_dir}")
    print(f"评估模型: {models_list}")
    print(f"检查模型: {args.check_model or '未配置'}")
    print(f"自定义MCP服务器: {args.mcpservers or '未配置'}")
    print(f"环境依赖配置: {args.environment_dependency or '未配置'}")
    print(f"失败重试模式: {'启用' if args.retry_failed_only else '禁用'}")
    print(f"运行模式: {args.run_mode}")
    print(f"最大并发数: {params.get('max_workers')}")
    print("开始执行MCP批量评估...")

    try:
        # 直接创建算子实例，不通过dataeng_sdk框架
        operator = Operator.__new__(Operator)

        # 手动设置必要的属性
        operator.input_files = input_files
        operator.output_local_path = str(output_dir)
        operator.params = params  # 传递解析后的参数
        operator.task_info = {
            "flow_id": "mock_flow_id",
            "dag_name": "mock_dag_name",
            "run_id": "mock_run_id",
        }

        # 手动执行算子的各个阶段
        print("执行setup阶段...")
        operator._setup()

        print("执行主要逻辑...")
        result_files = operator._run()

        print("执行teardown阶段...")
        operator._teardown()

        print("\n=== 执行完成 ===")
        print(f"结果文件:")
        for file_path in result_files or []:
            if Path(file_path).exists():
                print(f"  ✓ {file_path}")
            else:
                print(f"  ✗ {file_path} (不存在)")

        # 显示汇总信息
        summary_file = output_dir / "summary_report.json"
        if summary_file.exists():
            with open(summary_file, "r", encoding="utf-8") as f:
                summary = json.load(f)
            print(f"\n=== 执行汇总 ===")
            print(f"评估模型数: {summary.get('total_models', 0)}")
            print(f"输入文件数: {summary.get('total_input_files', 0)}")
            print(f"输出文件数: {summary.get('total_output_files', 0)}")

            for model, stats in summary.get("execution_summary", {}).items():
                success_rate = stats.get("success_rate", 0) * 100
                print(
                    f"{model}: {stats.get('successful_tasks', 0)}/"
                    f"{stats.get('total_tasks', 0)} 成功 ({success_rate:.1f}%)"
                )

    except Exception as e:
        print(f"执行失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
