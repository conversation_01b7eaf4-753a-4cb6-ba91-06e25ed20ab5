# ----------算子配置文件: 生成算子包的配置文件, 可配置依赖的python环境等----------

# [配置项]: base_python_version: 必填, 依赖的python版本
#   1. 可从下面平台提供的python版本中选择
#   2. 如果没有适合的版本或者依赖自身已有的python环境:
#      1). 可联系平台同学将增加基础python版本
#      2). 可填写可以下载python包(格式xx.tar.gz)的http或者ftp地址, 算子包打包会自动安装
#   3. 如果依赖第三方package, 可填写在requirements.txt中
#      1). 依赖安装的第三方包, 格式: {包名}=={version}, 版本号可不填, 直接写{包名}
#      2). 示例: 带版本号: numpy==1.19.5, 不带版本号: urllib3
# [平台提供的基础python版本]:
#   1. python3.6
#   2. python3.6-grpc (带grpc包的python环境, 不依赖grpc不要用这个)
#   3. python3.9-paddle (带paddle的python环境, 不依赖paddle不要用这个)
#   4. python3.10.12
# [默认值]: python3.6
# [配置示例]: python3.6
base_python_version: python3.10.12