# 自定义算子测试用例

本目录包含了 `self_define_operator.py` 的完整测试用例，使用 pytest 框架实现。

## 测试覆盖范围

### 单元测试
- **TestOperatorSetup**: 测试算子初始化和文件识别逻辑
- **TestModelStringParsing**: 测试模型字符串解析功能
- **TestModelConfig**: 测试各种模型配置生成功能
- **TestMCPConfigPriority**: 测试MCP配置三级优先级机制（核心功能）
- **TestFileOperations**: 测试文件操作相关功能

### 集成测试
- **TestIntegration**: 测试完整的工作流程集成

## 核心测试功能

### MCP配置三级优先级机制
测试用例验证以下优先级顺序：
1. **JSONL extra_info.mcpservers** (最高优先级)
2. **input_files中的mcpservers.json** (中等优先级)  
3. **YAML配置文件映射** (最低优先级)

### 测试场景包括：
- 单一优先级场景
- 混合优先级场景
- 服务器名称映射测试
- 配置文件解析测试

## 运行测试

### 安装依赖
```bash
cd operator
pip install pytest pytest-mock
```

### 运行所有测试
```bash
# 方法1: 使用运行脚本
python run_tests.py

# 方法2: 直接使用pytest
python -m pytest tests/ -v
```

### 运行特定测试
```bash
# 运行MCP优先级测试
python run_tests.py mcp

# 运行集成测试
python run_tests.py integration

# 运行特定测试类
python run_tests.py TestOperatorSetup

# 运行单个测试方法
python -m pytest tests/test_self_define_operator.py::TestMCPConfigPriority::test_mcp_config_priority_jsonl_first -v
```

## 测试数据

测试使用的样例数据：

### 示例JSONL数据
```json
{
  "data_id": 1,
  "query": "测试查询1",
  "extra_info": {
    "server_name": ["sqlite", "filesystem"],
    "mcpservers": {
      "sqlite": {
        "command": "python",
        "args": ["-m", "mcp_server_sqlite", "/test/db.sqlite"],
        "env": {"TEST": "1"}
      }
    }
  }
}
```

### 示例mcpservers.json
```json
{
  "filesystem": {
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
    "env": {"NODE_ENV": "test"}
  }
}
```

## 测试覆盖要点

### 1. 文件识别测试
- 验证算子能正确区分JSONL文件和mcpservers.json
- 测试多文件输入场景
- 验证文件解析错误处理

### 2. 优先级机制测试
- 验证三级优先级的正确执行顺序
- 测试配置覆盖行为
- 验证混合优先级场景

### 3. 模型配置测试
- 测试不同provider的配置生成
- 验证环境变量处理
- 测试错误模型名称处理

### 4. 集成测试
- 验证端到端工作流程
- 测试配置文件生成的完整性
- 验证Mock外部依赖的正确性

## Mock策略

测试中使用了以下Mock策略：
- **common_tool**: Mock日志和路径获取功能
- **subprocess.run**: Mock外部命令执行
- **环境变量**: Mock API密钥等敏感信息
- **文件系统**: 使用临时目录进行测试

## 注意事项

1. 测试使用临时目录，执行完自动清理
2. 所有外部依赖都被Mock，不会产生实际的网络请求或文件操作
3. 环境变量会被适当设置和清理
4. 测试数据都是预定义的示例，不依赖外部资源

## 故障排除

### 常见问题
1. **导入错误**: 确保在operator目录下运行测试
2. **路径问题**: 测试脚本会自动处理Python路径
3. **依赖缺失**: 确保安装了pytest和相关依赖

### 调试技巧
```bash
# 运行单个失败的测试
python -m pytest tests/test_self_define_operator.py::TestClass::test_method -v -s

# 查看详细错误信息
python -m pytest tests/ -v --tb=long

# 不捕获输出（查看print语句）
python -m pytest tests/ -v -s
``` 