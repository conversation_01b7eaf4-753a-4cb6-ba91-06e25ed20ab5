#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_self_define_operator.py
自定义算子的测试用例
"""
import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from self_define_operator import Operator


class TestOperatorSetup:
    """测试Operator的setup方法"""
    
    def test_setup_with_mcpservers_file(self, mock_operator, create_test_files, sample_yaml_config):
        """测试包含mcpservers.json文件的setup"""
        # 创建测试文件
        test_files = create_test_files(include_mcpservers=True)
        
        with patch('self_define_operator.config', sample_yaml_config):
            with patch('self_define_operator.common_tool') as mock_common_tool:
                mock_common_tool.get_output_path.return_value = "/tmp/test"
                mock_common_tool.get_logger.return_value = Mock()
                
                # 设置输入文件
                mock_operator.input_files = [test_files['jsonl'], test_files['mcpservers']]
                
                # 执行setup
                mock_operator._setup()
                
                # 验证文件分类
                assert len(mock_operator.jsonl_files) == 1
                assert test_files['jsonl'] in mock_operator.jsonl_files
                assert len(mock_operator.custom_mcp_servers) > 0
                assert 'filesystem' in mock_operator.custom_mcp_servers
                assert 'playwright' in mock_operator.custom_mcp_servers
    
    def test_setup_without_mcpservers_file(self, mock_operator, create_test_files, sample_yaml_config):
        """测试不包含mcpservers.json文件的setup"""
        # 创建测试文件（不包含mcpservers.json）
        test_files = create_test_files(include_mcpservers=False)
        
        with patch('self_define_operator.config', sample_yaml_config):
            with patch('self_define_operator.common_tool') as mock_common_tool:
                mock_common_tool.get_output_path.return_value = "/tmp/test"
                mock_common_tool.get_logger.return_value = Mock()
                
                # 设置输入文件
                mock_operator.input_files = [test_files['jsonl']]
                
                # 执行setup
                mock_operator._setup()
                
                # 验证文件分类
                assert len(mock_operator.jsonl_files) == 1
                assert test_files['jsonl'] in mock_operator.jsonl_files
                assert len(mock_operator.custom_mcp_servers) == 0
    
    def test_setup_model_parsing(self, mock_operator, create_test_files, sample_yaml_config):
        """测试模型参数解析"""
        test_files = create_test_files(include_mcpservers=False)
        
        with patch('self_define_operator.config', sample_yaml_config):
            with patch('self_define_operator.common_tool') as mock_common_tool:
                mock_common_tool.get_output_path.return_value = "/tmp/test"
                mock_common_tool.get_logger.return_value = Mock()
                
                # 设置输入文件和参数
                mock_operator.input_files = [test_files['jsonl']]
                mock_operator.params = {
                    "models": ["openai:gpt-4", "assistant_api:ernie-bot"],
                    "max_workers": 3,
                    "run_local": "true"
                }
                
                # 执行setup
                mock_operator._setup()
                
                # 验证模型解析
                assert mock_operator.eval_model_list == ["openai:gpt-4", "assistant_api:ernie-bot"]
                assert mock_operator.max_workers == 3
                assert mock_operator.run_local == True


class TestModelStringParsing:
    """测试模型字符串解析"""
    
    def test_parse_model_string_with_provider(self, mock_operator):
        """测试带provider的模型字符串解析"""
        provider, model = mock_operator._parse_model_string("openai:gpt-4")
        assert provider == "openai"
        assert model == "gpt-4"
    
    def test_parse_model_string_without_provider(self, mock_operator):
        """测试不带provider的模型字符串解析"""
        provider, model = mock_operator._parse_model_string("gpt-4")
        assert provider is None
        assert model == "gpt-4"
    
    def test_parse_model_string_with_complex_name(self, mock_operator):
        """测试复杂模型名称解析"""
        provider, model = mock_operator._parse_model_string("assistant_api:ernie-bot-turbo")
        assert provider == "assistant_api"
        assert model == "ernie-bot-turbo"


class TestModelConfig:
    """测试模型配置生成"""
    
    def test_get_model_config_openai(self, mock_operator, sample_yaml_config):
        """测试OpenAI模型配置获取"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            
            # 模拟环境变量
            with patch.dict('os.environ', {
                'OPENAI_API_BASE': 'https://api.openai.com/v1',
                'OPENAI_API_KEY': 'test_key'
            }):
                config = mock_operator._get_model_config_by_name("openai:gpt-4")
                
                assert config['model_name'] == 'gpt-4'
                assert config['model_provider'] == 'openai'
                assert config['openai_url'] == 'https://api.openai.com/v1'
                assert config['openai_api_key'] == 'test_key'
    
    def test_get_model_config_assistant_api(self, mock_operator, sample_yaml_config):
        """测试Assistant API模型配置获取"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            
            config = mock_operator._get_model_config_by_name("assistant_api:ernie-bot")
            
            assert config['model_name'] == 'ernie-bot'
            assert config['model_provider'] == 'assistant_api'
            assert config['backend'] == 'yiyan'
            assert config['authorization'] == 'test_auth'
            assert config['app_id'] == 'test_app_id'
    
    def test_get_model_config_not_found(self, mock_operator, sample_yaml_config):
        """测试未找到模型配置的情况"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            
            with pytest.raises(ValueError, match="未找到provider"):
                mock_operator._get_model_config_by_name("unknown:model")


class TestMCPConfigPriority:
    """测试MCP配置三级优先级机制"""
    
    def test_mcp_config_priority_jsonl_first(self, mock_operator, temp_test_dir, sample_yaml_config):
        """测试JSONL extra_info优先级最高"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            mock_operator.custom_mcp_servers = {
                "sqlite": {
                    "command": "custom_sqlite",
                    "args": ["custom_arg"]
                }
            }
            
            # JSONL行数据（优先级最高）
            jsonl_line = {
                "data_id": 1,
                "extra_info": {
                    "server_name": ["sqlite"],
                    "mcpservers": {
                        "sqlite": {
                            "command": "jsonl_sqlite",
                            "args": ["jsonl_arg"],
                            "priority": "highest"
                        }
                    }
                }
            }
            
            config_path = temp_test_dir / "test_config.json"
            mock_operator._generate_mcp_config(["sqlite"], config_path, jsonl_line)
            
            # 验证使用了JSONL中的配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            assert config["mcpServers"]["sqlite"]["command"] == "jsonl_sqlite"
            assert config["mcpServers"]["sqlite"]["priority"] == "highest"
    
    def test_mcp_config_priority_custom_file_second(self, mock_operator, temp_test_dir, sample_yaml_config):
        """测试自定义文件优先级第二"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            mock_operator.custom_mcp_servers = {
                "sqlite": {
                    "command": "custom_sqlite",
                    "args": ["custom_arg"]
                }
            }
            
            # JSONL行数据（没有mcpservers配置）
            jsonl_line = {
                "data_id": 1,
                "extra_info": {
                    "server_name": ["sqlite"]
                }
            }
            
            config_path = temp_test_dir / "test_config.json"
            mock_operator._generate_mcp_config(["sqlite"], config_path, jsonl_line)
            
            # 验证使用了自定义文件中的配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            assert config["mcpServers"]["sqlite"]["command"] == "custom_sqlite"
            assert config["mcpServers"]["sqlite"]["args"] == ["custom_arg"]
    
    def test_mcp_config_priority_yaml_third(self, mock_operator, temp_test_dir, sample_yaml_config):
        """测试YAML配置优先级最低"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            mock_operator.custom_mcp_servers = {}  # 没有自定义配置
            
            # JSONL行数据（没有mcpservers配置）
            jsonl_line = {
                "data_id": 1,
                "extra_info": {
                    "server_name": ["sqlite"]
                }
            }
            
            config_path = temp_test_dir / "test_config.json"
            mock_operator._generate_mcp_config(["sqlite"], config_path, jsonl_line)
            
            # 验证使用了YAML中的配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            assert config["mcpServers"]["sqlite"]["command"] == "python"
            assert "-m" in config["mcpServers"]["sqlite"]["args"]
    
    def test_mcp_config_server_mapping(self, mock_operator, temp_test_dir, sample_yaml_config):
        """测试服务器名称映射"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            mock_operator.custom_mcp_servers = {}
            
            # 使用需要映射的服务器名称
            jsonl_line = {
                "data_id": 1,
                "extra_info": {
                    "server_name": ["file_system"]  # 映射到filesystem
                }
            }
            
            config_path = temp_test_dir / "test_config.json"
            mock_operator._generate_mcp_config(["file_system"], config_path, jsonl_line)
            
            # 验证映射后找到了配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            assert "file_system" in config["mcpServers"]
            assert config["mcpServers"]["file_system"]["command"] == "npx"
    
    def test_mcp_config_mixed_priority(self, mock_operator, temp_test_dir, sample_yaml_config):
        """测试混合优先级场景"""
        with patch('self_define_operator.config', sample_yaml_config):
            mock_operator.yaml_config = sample_yaml_config
            mock_operator.custom_mcp_servers = {
                "filesystem": {
                    "command": "custom_filesystem",
                    "args": ["custom_fs_arg"]
                }
            }
            
            # JSONL只配置了sqlite，filesystem从自定义文件获取
            jsonl_line = {
                "data_id": 1,
                "extra_info": {
                    "server_name": ["sqlite", "filesystem"],
                    "mcpservers": {
                        "sqlite": {
                            "command": "jsonl_sqlite",
                            "args": ["jsonl_arg"]
                        }
                    }
                }
            }
            
            config_path = temp_test_dir / "test_config.json"
            mock_operator._generate_mcp_config(["sqlite", "filesystem"], config_path, jsonl_line)
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # sqlite使用JSONL配置（优先级1）
            assert config["mcpServers"]["sqlite"]["command"] == "jsonl_sqlite"
            # filesystem使用自定义文件配置（优先级2）
            assert config["mcpServers"]["filesystem"]["command"] == "custom_filesystem"


class TestFileOperations:
    """测试文件操作相关功能"""
    
    def test_create_safe_filename(self, mock_operator):
        """测试安全文件名创建"""
        assert mock_operator._create_safe_filename("openai:gpt-4") == "openai_gpt-4"
        assert mock_operator._create_safe_filename("path/to/model") == "path_to_model"
        assert mock_operator._create_safe_filename("model\\name") == "model_name"
    
    def test_generate_bench_config(self, mock_operator, temp_test_dir):
        """测试基准配置文件生成"""
        jsonl_line = {
            "data_id": 1,
            "query": "测试查询",
            "extra_info": {"server_name": ["sqlite"]}
        }
        
        config_path = temp_test_dir / "bench.json"
        result_path = mock_operator._generate_bench_config(jsonl_line, config_path)
        
        assert config_path.exists()
        assert str(config_path) == str(result_path)  # 转换为字符串比较
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        assert config == jsonl_line


class TestIntegration:
    """集成测试"""
    
    def test_mcp_priority_integration(self, mock_operator, create_test_files, sample_yaml_config):
        """MCP优先级机制集成测试"""
        # 创建测试文件
        test_files = create_test_files(include_mcpservers=True)
        
        with patch('self_define_operator.config', sample_yaml_config):
            with patch('self_define_operator.common_tool') as mock_common_tool:
                mock_common_tool.get_output_path.return_value = "/tmp/test"
                mock_common_tool.get_logger.return_value = Mock()
                
                # 设置输入文件
                mock_operator.input_files = [test_files['jsonl'], test_files['mcpservers']]
                
                # 执行setup加载配置
                mock_operator._setup()
                
                # 测试第一条JSONL数据（有extra_info.mcpservers）
                with open(test_files['jsonl'], 'r', encoding='utf-8') as f:
                    first_line = json.loads(f.readline())
                
                config_path = Path("/tmp/test_mcp_config.json")
                server_names = first_line["extra_info"]["server_name"]
                
                mock_operator._generate_mcp_config(server_names, config_path, first_line)
                
                # 验证配置文件生成
                assert config_path.exists()
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # sqlite应该使用JSONL中的配置（优先级1）
                assert config["mcpServers"]["sqlite"]["command"] == "python"
                assert config["mcpServers"]["sqlite"]["env"]["TEST"] == "1"
                
                # filesystem应该使用自定义文件配置（优先级2）
                assert config["mcpServers"]["filesystem"]["command"] == "npx"
                assert config["mcpServers"]["filesystem"]["env"]["NODE_ENV"] == "test"
                
                # 清理
                if config_path.exists():
                    config_path.unlink()
    
    @patch('subprocess.run')
    @patch('uuid.uuid4')
    @patch('pathlib.Path.exists')
    def test_execute_single_task_local_mock(self, mock_path_exists, mock_uuid, mock_subprocess, mock_operator, 
                                           create_test_files, sample_yaml_config, temp_test_dir):
        """测试本地执行单个任务（Mock版本）"""
        # 准备测试数据
        test_files = create_test_files(include_mcpservers=False)
        mock_uuid.return_value.hex = "test-uuid-123"
        
        # Mock Path.exists() 返回True，模拟mcphost文件存在
        mock_path_exists.return_value = True
        
        # Mock subprocess.run返回成功
        mock_process = Mock()
        mock_process.returncode = 0
        mock_subprocess.return_value = mock_process
        
        with patch('self_define_operator.config', sample_yaml_config):
            with patch('self_define_operator.common_tool') as mock_common_tool:
                mock_common_tool.get_output_path.return_value = str(temp_test_dir)
                mock_common_tool.get_logger.return_value = Mock()
                
                # 设置operator
                mock_operator.input_files = [test_files['jsonl']]
                mock_operator._setup()
                
                # 准备任务信息
                with open(test_files['jsonl'], 'r', encoding='utf-8') as f:
                    jsonl_line = json.loads(f.readline())
                
                task_info = {
                    "model_string": "openai:gpt-4",
                    "data_id": 1,
                    "jsonl_line": jsonl_line
                }
                
                # 创建模拟的结果文件
                task_id_base = "openai_gpt-4_1"
                # 创建配置目录结构
                config_base_dir = mock_operator.config_dir
                
                # 遍历可能的任务ID，找到实际创建的目录
                import time
                current_time = int(time.time())
                possible_task_ids = [f"{task_id_base}_{current_time + i}" for i in range(-2, 3)]
                
                for task_id in possible_task_ids:
                    task_dir = config_base_dir / task_id
                    if task_dir.exists() or True:  # 总是创建，因为可能在执行过程中创建
                        task_dir.mkdir(parents=True, exist_ok=True)
                        result_file = task_dir / "result.json"
                        result_data = {"success": True, "result": "test_result"}
                        with open(result_file, 'w', encoding='utf-8') as f:
                            json.dump(result_data, f)
                        break
                
                # 执行任务
                result = mock_operator._execute_single_task_local(task_info)
                
                # 验证结果
                assert result["success"] == True
                assert result["model_string"] == "openai:gpt-4"
                assert result["data_id"] == 1
                
                # 验证subprocess被调用
                assert mock_subprocess.called


if __name__ == "__main__":
    pytest.main([__file__, "-v"]) 