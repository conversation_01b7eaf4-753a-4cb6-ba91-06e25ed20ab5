#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
conftest.py
pytest配置文件，定义测试fixtures和通用配置
"""
import os
import sys
import json
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch

# 添加operator目录到Python路径
operator_dir = Path(__file__).parent.parent
sys.path.insert(0, str(operator_dir))

from self_define_operator import Operator
from config import config


@pytest.fixture
def mock_task_info():
    """模拟任务信息"""
    return {
        "task_id": "test_task_123",
        "input_files": [],
        "output_path": "/tmp/test_output"
    }


@pytest.fixture
def mock_params():
    """模拟参数"""
    return {
        "models": ["openai:gpt-4", "assistant_api:ernie-bot"],
        "max_workers": 2,
        "run_local": True
    }


@pytest.fixture
def sample_jsonl_data():
    """示例JSONL数据"""
    return [
        {
            "data_id": 1,
            "query": "测试查询1",
            "extra_info": {
                "server_name": ["sqlite", "filesystem"],
                "mcpservers": {
                    "sqlite": {
                        "command": "python",
                        "args": ["-m", "mcp_server_sqlite", "/test/db.sqlite"],
                        "env": {"TEST": "1"}
                    }
                }
            }
        },
        {
            "data_id": 2,
            "query": "测试查询2",
            "extra_info": {
                "server_name": ["playwright", "firecrawl-mcp"]
            }
        }
    ]


@pytest.fixture
def sample_mcpservers_config():
    """示例MCP服务器配置"""
    return {
        "filesystem": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
            "env": {"NODE_ENV": "test"}
        },
        "playwright": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-playwright"],
            "env": {"HEADLESS": "true"}
        }
    }


@pytest.fixture
def temp_test_dir():
    """创建临时测试目录"""
    with tempfile.TemporaryDirectory() as tmpdir:
        test_dir = Path(tmpdir)
        yield test_dir


@pytest.fixture
def sample_yaml_config():
    """示例YAML配置"""
    return {
        "eval_model_list": ["openai:gpt-4"],
        "max_workers": 4,
        "model_providers": {
            "openai": {
                "model_list": ["gpt-4", "gpt-3.5-turbo"],
                "global_timeout": 300
            },
            "assistant_api": {
                "model_list": ["ernie-bot"],
                "backend": "yiyan",
                "authorization": "test_auth",
                "assistant_server_ip": "127.0.0.1",
                "assistant_server_port": 8080,
                "app_id": "test_app_id",
                "global_timeout": 300
            }
        },
        "mcp_servers": {
            "sqlite": {
                "command": "python",
                "args": ["-m", "mcp_server_sqlite", "/default/db.sqlite"]
            },
            "filesystem": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", "/default"]
            }
        },
        "mcp_server_mapping": {
            "file_system": "filesystem",
            "filesystem": "filesystem",
            "sqlite": "sqlite",
            "sqllite": "sqllite"
        }
    }


@pytest.fixture
def mock_operator(temp_test_dir, mock_task_info, mock_params, sample_yaml_config):
    """创建模拟的Operator实例"""
    with patch('self_define_operator.config', sample_yaml_config):
        with patch('self_define_operator.common_tool') as mock_common_tool:
            mock_common_tool.get_output_path.return_value = str(temp_test_dir)
            mock_common_tool.get_logger.return_value = Mock()
            
            # 模拟sys.argv
            with patch('sys.argv', ['operator.py']):
                operator = Operator.__new__(Operator)
                operator.task_info = mock_task_info
                operator.params = mock_params
                operator.input_files = []
                operator.output_local_path = str(temp_test_dir)
                
                return operator


@pytest.fixture
def create_test_files(temp_test_dir, sample_jsonl_data, sample_mcpservers_config):
    """创建测试文件"""
    def _create_files(include_mcpservers=True, include_extra_jsonl=False):
        files = {}
        
        # 创建JSONL测试文件
        jsonl_file = temp_test_dir / "test_data.jsonl"
        with open(jsonl_file, 'w', encoding='utf-8') as f:
            for line_data in sample_jsonl_data:
                f.write(json.dumps(line_data, ensure_ascii=False) + '\n')
        files['jsonl'] = str(jsonl_file)
        
        # 创建mcpservers.json文件（如果需要）
        if include_mcpservers:
            mcpservers_file = temp_test_dir / "mcpservers.json"
            with open(mcpservers_file, 'w', encoding='utf-8') as f:
                json.dump(sample_mcpservers_config, f, indent=2, ensure_ascii=False)
            files['mcpservers'] = str(mcpservers_file)
        
        # 创建额外的JSONL文件（如果需要） 
        if include_extra_jsonl:
            extra_jsonl_file = temp_test_dir / "extra_data.jsonl"
            with open(extra_jsonl_file, 'w', encoding='utf-8') as f:
                extra_data = {
                    "data_id": 3,
                    "query": "额外测试查询",
                    "extra_info": {"server_name": ["sqlite"]}
                }
                f.write(json.dumps(extra_data, ensure_ascii=False) + '\n')
            files['extra_jsonl'] = str(extra_jsonl_file)
            
        return files
    
    return _create_files


@pytest.fixture(autouse=True)
def setup_test_env():
    """自动设置测试环境"""
    # 设置测试环境变量
    os.environ['MCPHOST_PATH'] = '/usr/local/bin/mcphost'  # 模拟路径
    yield
    # 清理环境变量
    if 'MCPHOST_PATH' in os.environ:
        del os.environ['MCPHOST_PATH'] 