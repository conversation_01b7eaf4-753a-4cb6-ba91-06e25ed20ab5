# MCP Bench - 开发工具命令
.PHONY: help install install-dev test test-cov lint format clean build upload docker-build docker-run docker-push go-build go-build-linux go-test go-clean run-mcp-eval

# 项目配置
IMAGE_NAME := mcp/mcp-env
TAG := latest
REGISTRY := ccr-2y3xupwh-pub.cnc.bj.baidubce.com
DATAENG_INPUT_DATA_DIR = 

# 默认目标
help:
	@echo "MCP Bench 开发工具命令:"
	@echo ""
	@echo "MCP批量评估:"
	@echo "  run-mcp-eval      - 运行MCP批量评估（自动设置mcphost环境变量）"
	@echo "    用法: [INPUT_FILE=/path/to/file.jsonl] [INPUT_DIR=/path/to/input/dir] MODELS='provider:model1,provider:model2' MAX_WORKERS=4 [RUN_MODE=local] [CHECK_MODEL=provider:model] [CHECK_SCRIPT=/path/to/check.py] [MCP_SERVERS=/path/to/mcpservers.json] [ENV_DEPENDENCY=/path/to/environment_dependency.json] [RETRY_FAILED_ONLY=true] make run-mcp-eval"
	@echo "    注意: INPUT_FILE和INPUT_DIR至少指定一个，优先使用INPUT_FILE，若未指定则从INPUT_DIR中自动查找JSONL文件"
	@echo "    本地运行示例: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool,uni_crawl:qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    带检查的示例: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool' CHECK_MODEL='uni_crawl:gpt-4' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    失败重试示例: INPUT_FILE=/path/old_results.jsonl MODELS='uni_crawl:gpt41_pool' MAX_WORKERS=4 RETRY_FAILED_ONLY=true make run-mcp-eval"
	@echo "    自定义检查脚本: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool' CHECK_MODEL='uni_crawl:gpt-4' CHECK_SCRIPT='/path/to/my_check.py' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    自定义MCP服务器: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool' MCP_SERVERS='operator/my_mcpservers.json' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    环境依赖配置: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool' ENV_DEPENDENCY='operator/environment_dependency.json' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    输入目录示例: INPUT_FILE=/path/demo.jsonl MODELS='uni_crawl:gpt41_pool' INPUT_DIR='/path/to/mcp/scripts' MAX_WORKERS=4 make run-mcp-eval"
	@echo "    K8s运行示例: INPUT_FILE=/path/demo.jsonl MODELS='assistant_api:ERNIE-4.5-single-custom-yiyanweb' MAX_WORKERS=4 RUN_MODE=k8s make run-mcp-eval"
	@echo ""
	@echo "Go 相关:"
	@echo "  go-build       - 构建Go应用程序（本地环境）"
	@echo "  go-build-linux - 构建Go应用程序（Linux环境，用于Docker）"
	@echo "  go-test        - 运行Go测试"
	@echo "  go-clean       - 清理Go构建文件"
	@echo ""
	@echo "Docker 相关:"
	@echo "  docker-build   - 完整Docker构建（Go编译+镜像构建）"
	@echo "  docker-run     - 运行Docker容器"
	@echo "  docker-push    - 推送Docker镜像"
	@echo ""


# Go 相关命令
go-build:
	@echo "构建Go应用程序（本地环境）..."
	cd mcphost && go build -o mcphost ./main.go
	@echo "✓ Go应用程序构建完成: mcphost/mcphost"

go-build-linux:
	@echo "构建Go应用程序（Linux环境，用于Docker）..."
	cd mcphost && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o mcphost ./main.go
	@echo "✓ Linux Go应用程序构建完成: mcphost/mcphost"

go-test:
	@echo "运行Go测试..."
	cd mcphost && go test ./...

go-clean:
	@echo "清理Go构建文件..."
	cd mcphost && rm -rf output/ mcphost
	cd mcphost && go clean

go-mod:
	@echo "整理Go模块依赖..."
	cd mcphost && go mod tidy

# Python安装依赖
install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt
	pip install -e .

# 运行测试
test:
	python -m pytest tests/ -v

test-cov:
	python -m pytest tests/ -v --cov=src --cov-report=html --cov-report=term-missing

# 代码质量检查
lint:
	python -m flake8 src/ tests/ evaluation_runner.py
	python -m mypy src/ evaluation_runner.py

# 代码格式化
format:
	python -m black src/ tests/ evaluation_runner.py *.py
	python -m isort src/ tests/ evaluation_runner.py *.py

test-mcphost: go-build
	cd mcphost && DATAENG_INPUT_DATA_DIR=${PWD}/mcphost/conf/${CONF_SUFFIX} DATAENG_OUTPUT_DIR=${PWD}/mcphost/output ./boost.sh

# MCP批量评估目标
run-mcp-eval: go-build
	@echo "运行MCP批量评估..."
	@if [ -z "$(INPUT_FILE)" ] && [ -z "$(INPUT_DIR)" ]; then \
		echo "错误: 请指定INPUT_FILE或INPUT_DIR参数"; \
		echo "示例1: INPUT_FILE=operator/test_data/demo.jsonl MODELS='gpt41_pool,qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval"; \
		echo "示例2: INPUT_DIR='/path/to/mcp/scripts' MODELS='gpt41_pool,qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval"; \
		exit 1; \
	fi
	@if [ -z "$(MODELS)" ]; then \
		echo "错误: 请指定MODELS参数"; \
		echo "示例: INPUT_DIR='/path/to/mcp/scripts' MODELS='gpt41_pool,qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval"; \
		exit 1; \
	fi
	@if [ -n "$(INPUT_FILE)" ] && [ ! -f "$(INPUT_FILE)" ]; then \
		echo "错误: 输入文件 $(INPUT_FILE) 不存在"; \
		exit 1; \
	fi
	@if [ -n "$(INPUT_DIR)" ] && [ ! -d "$(INPUT_DIR)" ]; then \
		echo "错误: 输入目录 $(INPUT_DIR) 不存在"; \
		exit 1; \
	fi
	@if [ -z "$(MAX_WORKERS)" ]; then \
		echo "错误: 请指定MAX_WORKERS参数"; \
		echo "示例: INPUT_DIR='/path/to/mcp/scripts' MODELS='gpt41_pool,qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval"; \
		exit 1; \
	fi
	$(eval RUN_MODE ?= local)
	$(eval RETRY_FAILED_ONLY ?= false)
	@echo "输入文件: $(INPUT_FILE)"
	@echo "评估模型: $(MODELS)"
	@echo "检查模型: $(if $(CHECK_MODEL),$(CHECK_MODEL),未配置)"
	@echo "检查脚本: $(if $(CHECK_SCRIPT),$(CHECK_SCRIPT),自动查找)"
	@echo "自定义MCP服务器: $(if $(MCP_SERVERS),$(MCP_SERVERS),未配置)"
	@echo "环境依赖配置: $(if $(ENV_DEPENDENCY),$(ENV_DEPENDENCY),未配置)"
	@echo "输入目录: $(if $(INPUT_DIR),$(INPUT_DIR),未配置)"
	@echo "运行模式: $(RUN_MODE)"
	@echo "最大并发数: $(MAX_WORKERS)"
	@echo "失败重试模式: $(if $(filter true,$(RETRY_FAILED_ONLY)),启用 - 仅重试失败任务,禁用 - 全部重新执行)"
	@if [ "$(RUN_MODE)" = "local" ]; then \
		echo "mcphost可执行文件路径: $${PWD}/mcphost/mcphost"; \
	fi
	@cd operator && MCPHOST_PATH="$${PWD}/../mcphost/mcphost" python local_test.py \
	$(if $(INPUT_FILE),--input-file "$(INPUT_FILE)",) \
	$(if $(filter true,$(RETRY_FAILED_ONLY)),--retry-failed-only,) \
	--models "$(MODELS)" \
	--output-dir "output_$(if $(INPUT_FILE),$(basename $(notdir $(INPUT_FILE))),$(notdir $(INPUT_DIR)))_$(shell date +%Y%m%d_%H%M%S)" \
	--max-workers $(MAX_WORKERS) \
	--run-mode $(RUN_MODE) \
	$(if $(CHECK_MODEL),--check-model "$(CHECK_MODEL)",) \
	$(if $(CHECK_SCRIPT),--check-script "$(CHECK_SCRIPT)",) \
	$(if $(MCP_SERVERS),--mcpservers "$(MCP_SERVERS)",) \
	$(if $(ENV_DEPENDENCY),--environment-dependency "$(ENV_DEPENDENCY)",) \
	$(if $(INPUT_DIR),--input-dir "$(INPUT_DIR)",)
	@echo "✓ MCP批量评估完成，结果保存在 operator/output/ 目录"

# Docker 相关命令
docker-build: go-build-linux
	@echo "构建Docker镜像..."
	docker build --platform linux/amd64 -t $(IMAGE_NAME):$(TAG) .
	@echo "✓ Docker镜像构建完成: $(IMAGE_NAME):$(TAG)"

docker-run:
	@echo "运行Docker容器..."
	docker run --platform linux/amd64 \
		-e DATAENG_INPUT_DATA_DIR=/home/<USER>/input \
		-e DATAENG_OUTPUT_DIR=/home/<USER>/output \
		-v $(PWD)/mcphost/conf:/home/<USER>/input \
		-v $(PWD)/mcphost/output:/home/<USER>/output \
		-v $(PWD)/mcphost/file_system_mock_data:/home/<USER>/file_system_mock_data \
		--rm -it $(IMAGE_NAME):$(TAG) 
		

docker-push:
	@echo "推送Docker镜像..."
	@if [ -z "$(REGISTRY)" ]; then \
		echo "错误: 请设置REGISTRY变量"; \
		exit 1; \
	fi
	docker tag $(IMAGE_NAME):$(TAG) $(REGISTRY)/$(IMAGE_NAME):$(TAG)
	docker push $(REGISTRY)/$(IMAGE_NAME):$(TAG)

# 清理文件
clean:
	@echo "清理Python临时文件..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete
	@echo "清理Go构建文件..."
	cd mcphost && rm -rf output/ mcphost && go clean
	@echo "清理Docker资源..."
	docker system prune -f
	@echo "✓ 清理完成"
