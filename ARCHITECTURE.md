# 项目架构说明文档

## 1. 综合概述

本项目旨在构建一个自动化、可扩展的基准测试平台，专门用于评估大语言模型（LLM）在复杂场景下使用外部工具（Tools）和函数（Functions）的能力。平台通过将宏观的任务调度与微观的原子化测试执行相分离，实现了高度的灵活性和可伸缩性，能够支持从本地开发调试到大规模云端并行测试的多种需求。

项目的核心由两个关键组件构成：
- **`operator` (调度层)**: 一个基于 Python 的高级任务调度与评估框架。
- **`mcphost` (执行层)**: 一个基于 Go 的原子化测试执行引擎。

## 2. 架构分层

本平台采用经典的两层架构设计：调度层与执行层。这种分层设计使得每一层都可以专注于自身的核心职责，从而提高了整个系统的模块化程度、可维护性和可扩展性。

### 调度层 (`operator`)

`operator` 作为整个测试流程的“大脑”和“控制塔”，负责宏观层面的任务管理。

- **主要职责**:
    - **测试编排**: 读取批量的测试用例（Test Cases）和待测模型列表。
    - **动态配置**: 为每一个独立的测试任务动态生成 `mcphost` 所需的配置文件。
    - **任务分发**: 调用 `mcphost` 执行具体的测试任务，支持本地进程和 Kubernetes Job 两种模式。
    - **结果聚合**: 收集所有 `mcphost` 实例执行后生成的 `result.json` 文件。
    - **评估与报告**: 验证并汇总所有测试结果，生成最终的、易于分析的摘要报告。

### 执行层 (`mcphost`)

`mcphost` 作为“执行者”或“工人”，负责完成一次具体的、独立的测试流程。它被设计成一个无状态的、可移植的命令行工具。

- **主要职责**:
    - **环境准备**: 根据配置初始化测试环境，如创建临时目录、启动依赖服务等。
    - **连接建立**: 负责与 LLM API 和 MCP（Model Context Protocol）服务器建立连接。
    - **原子化执行**: 严格按照测试用例的步骤，编排 LLM 与工具之间的调用与交互。
    - **详细记录**: 捕获并记录测试过程中的所有关键信息，包括时间戳、LLM 请求与响应、工具调用参数与返回等。
    - **结果输出**: 在测试完成后，将所有记录的数据序列化为一个标准的 `result.json` 文件。

### 分层优势

- **关注点分离 (Separation of Concerns)**: `operator` 关注“测什么”和“怎么测”，而 `mcphost` 只关注“如何执行单次测试”。这使得两者的逻辑都更加清晰，易于开发和维护。
- **技术栈优势互补 (Complementary Tech Stack)**:
    - **Python (`operator`)**: 非常适合用于数据处理、配置管理和与 Kubernetes 等系统进行交互的脚本编写。
    - **Go (`mcphost`)**: 能够编译成高效、无依赖的静态二进制文件，非常适合在各种环境中（包括容器）分发和执行，保证了执行环境的一致性和可靠性。
- **可扩展性与灵活性 (Scalability & Flexibility)**: `operator` 可以轻松地在本地或 Kubernetes 集群上并行启动成百上千个 `mcphost` 实例，从而实现大规模并行测试。同时，新增测试用例或模型只需修改 `operator` 的输入，无需改动 `mcphost` 的核心逻辑。

## 3. 核心工作流

一个典型的端到端测试工作流程如下：

```mermaid
sequenceDiagram
    participant User as 用户
    participant Operator as 调度层 (operator)
    participant Mcphost as 执行层 (mcphost)
    participant LLM_API as LLM API
    participant MCP_Server as MCP 工具服务器

    User->>Operator: 运行测试指令 (含测试用例集)
    Operator->>Operator: 读取测试用例和模型列表
    loop 为每个测试任务
        Operator->>Operator: 动态生成 mcphost 配置文件 (config.json)
        Operator->>Mcphost: 启动执行 (本地/K8s)，传入配置
    end
    Mcphost->>Mcphost: 解析配置，准备环境
    Mcphost->>LLM_API: 建立连接
    Mcphost->>MCP_Server: 建立连接
    Mcphost->>LLM_API: 发送初始 Prompt
    LLM_API-->>Mcphost: 返回响应 (含工具调用请求)
    Mcphost->>MCP_Server: 执行工具调用
    MCP_Server-->>Mcphost: 返回工具执行结果
    Mcphost->>LLM_API: 将工具结果发回
    LLM_API-->>Mcphost: 返回最终响应
    Mcphost->>Mcphost: 测试完成，生成 result.json
    Mcphost-->>Operator: 通知任务完成
    Operator->>Operator: 收集并验证 result.json
    Operator->>User: 所有任务完成后，生成最终摘要报告
```

## 4. 关键组件

### `operator` (调度层)
- **设计思想**: 作为一个灵活的测试调度器，它通过插件化的方式支持不同的执行后端（本地、Kubernetes），并通过读取结构化的数据文件（如 YAML）来管理复杂的测试矩阵。
- **核心功能**:
    - **测试用例管理**: 解析定义了测试场景、步骤和断言的用例文件。
    - **动态配置生成**: 将测试用例与模型参数结合，生成 `mcphost` 可直接消费的 JSON 配置。
    - **进程/作业管理**: 抽象了任务的启动和监控，无论是本地子进程还是 Kubernetes Job。
    - **结果聚合与报告**: 负责将分散的、详细的 JSON 结果，转化为集中的、高层次的评估报告。

### `mcphost` (执行层)
- **设计思想**: 作为一个健壮、可移植的“测试执行器”，它保证了每一次测试都在一个隔离且一致的环境中进行。它的输入（配置）和输出（结果）都是标准化的 JSON，使其易于被任何上层系统集成。
- **核心功能**:
    - **原子化执行**: 完整地执行单次测试，不依赖外部状态。
    - **环境管理器**: 负责测试生命周期内的环境设置与清理。
    - **LLM/MCP 客户端**: 内置了与大语言模型和 MCP 服务器通信的逻辑。
    - **标准化输出**: 确保无论测试成功或失败，都能产出一个结构一致、信息详尽的 `result.json` 文件。

## 5. 部署与执行

平台支持两种主要的部署和执行模式，以适应不同的使用场景。

### 本地模式 (Local Mode)
- **描述**: `operator` 在本地机器上直接通过命令行调用 `mcphost` 二进制文件来执行测试任务。所有测试任务作为本地子进程运行。
- **适用场景**:
    - **开发与调试**: 快速验证新的 `mcphost` 功能或调试单个测试用例。
    - **小规模测试**: 在本地机器资源足够的情况下，运行小批量的测试。

### Kubernetes 模式 (Kubernetes Mode)
- **描述**: `operator` 与 Kubernetes API 服务器交互，为每个测试任务动态创建一个 Kubernetes Job。`mcphost` 的二进制文件被打包在 Docker 镜像中，作为 Job 的一部分在集群的 Pod 中运行。
- **适用场景**:
    - **大规模并行测试**: 利用 Kubernetes 集群的弹性伸缩能力，同时运行成百上千个测试任务。
    - **自动化 CI/CD**: 集成到持续集成流程中，在代码变更后自动运行完整的基准测试。
    - **资源隔离与管理**: 每个测试都在独立的容器环境中运行，保证了资源隔离和环境一致性。