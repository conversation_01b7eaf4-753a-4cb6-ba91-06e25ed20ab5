# Git 和版本控制
.git
.gitignore
.gitattributes

# IDE 和编辑器
.vscode
.idea
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.tmp/

# 构建输出
dist/
build/
*.egg-info/

# 测试和覆盖率
.coverage
.nyc_output
coverage/

# 文档
docs/_build/
*.md
!README.md

# 其他
*.backup
*.bak 