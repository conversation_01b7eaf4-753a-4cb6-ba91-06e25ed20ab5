# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an automated evaluation platform for Large Language Models (LLMs) based on the Model Context Protocol (MCP). The system assesses LLM agent capabilities through standardized testing environments, initializing test scenarios, recording model behaviors, executing tool calls, and performing automated result validation.

The platform uses a two-layer architecture:
1. **Operator Layer (Python)**: Scheduling and evaluation framework
2. **MCPHost Layer (Go)**: Atomic test execution engine

## Common Commands

### Building and Running

```bash
# Build the Go application for local environment
make go-build

# Build the Go application for Linux/Docker environment
make go-build-linux

# Complete Docker build (Go compilation + image build)
make docker-build

# Run Docker container
make docker-run

# Push Docker image
make docker-push
```

### Testing

```bash
# Run Go tests
make go-test

# Run Python tests
make test

# Run mcphost test cases locally
make test-mcphost

# Run MCP batch evaluation locally
make run-mcp-eval
```

Example for running MCP batch evaluation:
```bash
INPUT_FILE=operator/test_data/demo.jsonl MODELS='uni_crawl:gpt41_pool,uni_crawl:qwen3235ba22b_pool' MAX_WORKERS=4 make run-mcp-eval
```

### Development

```bash
# Install Python dependencies
make install

# Install development dependencies
make install-dev

# Code linting
make lint

# Format code
make format

# Clean build files
make clean
```

## Architecture Structure

### Operator Layer (Python)
- Located in `operator/` directory
- Main file: `operator/self_define_operator.py`
- Responsible for:
  - Test orchestration and scheduling
  - Dynamic configuration generation for MCPHost
  - Task distribution (local/K8s modes)
  - Result aggregation and reporting
  - Supporting both local execution and Kubernetes deployment

### MCPHost Layer (Go)
- Located in `mcphost/` directory
- Main entry point: `mcphost/main.go`
- Core logic: `mcphost/cmd/root.go`
- Responsibilities:
  - Environment preparation
  - Connecting to LLM APIs and MCP servers
  - Executing test steps atomically
  - Detailed logging of all actions
  - Standardized result output (result.json)

### Configuration Files

1. **Model Configuration** (`model_config.json`): Defines LLM provider details, API keys, endpoints
2. **MCP Server Configuration** (`mcp_config.json`): Defines available MCP tools/servers
3. **Benchmark Configuration** (`bench.json`): Contains test scenarios, queries, environment dependencies, and validation rules

### Workflow

1. User initiates test command
2. Operator reads test cases and model list
3. Operator generates MCPHost configurations and starts execution
4. MCPHost initializes environment, connects to LLM and MCP servers
5. MCPHost executes tests and generates result files
6. Operator collects results and generates final report

### Deployment Modes

- **Local Mode**: Direct execution of mcphost binary
- **Kubernetes Mode**: Execution through K8s Jobs with Docker containers