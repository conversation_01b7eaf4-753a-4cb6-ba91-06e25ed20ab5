#!/bin/bash

chmod +x ./github-mcp-server/github-mcp-server

# GitHub MCP 评估仓库创建脚本
# 用于创建包含各种GitHub工作流的测试仓库

set -e  # 遇到错误立即退出

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_info() {
    echo "ℹ️  $1"
}

# 硬编码配置
REPO_OWNER="TikyFirstggg"
BASE_REPO_NAME="mcp-github-evaluation"

# 最高优先级：读取github_name.txt文件内容作为仓库名称
if [ -f "./github_name.txt" ]; then
    GITHUB_NAME_FROM_FILE=$(cat ./github_name.txt | tr -d '
\r' | xargs)
    if [ ! -z "$GITHUB_NAME_FROM_FILE" ]; then
        REPO_NAME="$GITHUB_NAME_FROM_FILE"
        log_info "✅ 使用github_name.txt中的仓库名称: $REPO_NAME"
    else
        log_warning "github_name.txt文件为空，使用默认命名逻辑"
        # 如果提供了dataid参数，将其添加到仓库名称中
        if [ ! -z "$1" ]; then
            REPO_NAME="${BASE_REPO_NAME}_$1"
            log_info "Using dataid '$1' to create unique repo: $REPO_NAME"
        else
            REPO_NAME="$BASE_REPO_NAME"
        fi
    fi
else
    log_warning "未找到github_name.txt文件，使用默认命名逻辑"
    # 如果提供了dataid参数，将其添加到仓库名称中
    if [ ! -z "$1" ]; then
        REPO_NAME="${BASE_REPO_NAME}_$1"
        log_info "Using dataid '$1' to create unique repo: $REPO_NAME"
    else
        REPO_NAME="$BASE_REPO_NAME"
    fi
fi

REPO_URL="https://github.com/$REPO_OWNER/$REPO_NAME"
GITHUB_TOKEN="****************************************"

# 更新 mcpservers.json 配置
update_mcpservers_config() {
    local mcpservers_file="./mcpservers.json"
    
    if [ -f "$mcpservers_file" ]; then
        log_info "更新 $mcpservers_file 配置..."
        
        # 使用 sed 替换环境变量占位符
        sed -i.bak "s/\${REPO_NAME}/$REPO_NAME/g" "$mcpservers_file"
        
        # 删除备份文件
        rm -f "$mcpservers_file.bak"
        
        log_success "✅ $mcpservers_file 已更新为仓库: $REPO_NAME"
    else
        log_warning "未找到 $mcpservers_file 文件"
    fi
}

# 导出环境变量供其他程序使用
export REPO_NAME
log_info "✅ 已导出环境变量 REPO_NAME=$REPO_NAME"

# 更新配置文件
update_mcpservers_config

# 设置 MCP 服务器执行权限
setup_mcp_server_permissions() {
    local mcp_server_path="./github-mcp-server/github-mcp-server"
    
    if [ -f "$mcp_server_path" ]; then
        log_info "设置 MCP 服务器执行权限..."
        chmod +x "$mcp_server_path"
        log_success "✅ 已设置 $mcp_server_path 执行权限"
    else
        log_warning "未找到 MCP 服务器文件: $mcp_server_path"
    fi
}

# 设置权限
setup_mcp_server_permissions

log_info "Using hardcoded config: $REPO_OWNER/$REPO_NAME"
REPO_DESCRIPTION="GitHub MCP Server 功能评估测试仓库"

# GitHub集成配置
GITHUB_REPO_VISIBILITY="private"  # 创建私有仓库
GITHUB_ORG=""  # 可选: 组织名称，留空则创建在个人账户下
PUSH_TO_GITHUB="true"  # 默认推送到GitHub
AUTO_CREATE_REPO="true"  # 默认自动创建GitHub仓库
AUTO_MODE="true"  # 自动模式

log_info "将创建私有仓库（私有仓库可能对Push Protection有不同的策略）"

# 自动安装依赖函数
auto_install_dependencies() {
    log_info "检查并自动安装所需依赖..."
    
    # 检测操作系统
    OS=""
    if [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    elif [[ "$OSTYPE" == "linux-gnu"* ]] || [[ "$OSTYPE" == "linux"* ]]; then
        if command -v apt-get &> /dev/null; then
            OS="ubuntu"
        elif command -v yum &> /dev/null; then
            OS="centos"
        elif command -v dnf &> /dev/null; then
            OS="fedora"
        elif command -v pacman &> /dev/null; then
            OS="arch"
        elif command -v zypper &> /dev/null; then
            OS="opensuse"
        elif command -v apk &> /dev/null; then
            OS="alpine"
        else
            OS="linux"
        fi
    else
        OS="unknown"
    fi
    
    log_info "检测到操作系统: $OS"
    
    # 安装 GitHub CLI
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI 未安装，正在自动安装..."
        
        case $OS in
            "macos")
                if ! command -v brew &> /dev/null; then
                    log_info "安装 Homebrew..."
                    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
                fi
                brew install gh
                ;;
            "ubuntu")
                sudo apt update
                sudo apt install -y gh
                ;;
            "centos")
                sudo yum install -y gh
                ;;
            "fedora")
                sudo dnf install -y gh
                ;;
            "arch")
                sudo pacman -S --noconfirm github-cli
                ;;
            "opensuse")
                sudo zypper install -y gh
                ;;
            "alpine")
                sudo apk add --no-cache github-cli
                ;;
            "linux")
                log_warning "检测到通用Linux系统，尝试使用官方安装脚本..."
                curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg \
                && sudo chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg \
                && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
                && sudo apt update \
                && sudo apt install gh -y 2>/dev/null || {
                    log_warning "使用官方脚本安装GitHub CLI..."
                    curl -fsSL https://cli.github.com/packages/install.sh | sudo bash
                }
                ;;
            *)
                log_error "无法在当前系统上自动安装 GitHub CLI，请手动安装"
                log_info "访问: https://cli.github.com/ 获取安装说明"
                exit 1
                ;;
        esac
        
        if command -v gh &> /dev/null; then
            log_success "GitHub CLI 安装成功"
        else
            log_error "GitHub CLI 安装失败"
            exit 1
        fi
    else
        log_success "GitHub CLI 已安装"
    fi
    
    # 安装 Git（通常系统已有，但检查一下）
    if ! command -v git &> /dev/null; then
        log_warning "Git 未安装，正在自动安装..."
        
        case $OS in
            "macos")
                brew install git
                ;;
            "ubuntu"|"linux")
                sudo apt update && sudo apt install -y git
                ;;
            "centos")
                sudo yum install -y git
                ;;
            "fedora")
                sudo dnf install -y git
                ;;
            "arch")
                sudo pacman -S --noconfirm git
                ;;
            "opensuse")
                sudo zypper install -y git
                ;;
            "alpine")
                sudo apk add --no-cache git
                ;;
            *)
                log_error "无法在当前系统上自动安装 Git，请手动安装"
                exit 1
                ;;
        esac
        
        if command -v git &> /dev/null; then
            log_success "Git 安装成功"
        else
            log_error "Git 安装失败"
            exit 1
        fi
    else
        log_success "Git 已安装"
    fi
    
    # 安装 Node.js 和 npm（可选，但推荐）
    if ! command -v node &> /dev/null; then
        log_warning "Node.js 未安装，正在自动安装..."
        
        case $OS in
            "macos")
                brew install node
                ;;
            "ubuntu"|"linux")
                curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - 2>/dev/null && sudo apt-get install -y nodejs || {
                    log_warning "使用官方NodeSource安装失败，尝试系统包管理器..."
                    sudo apt update && sudo apt install -y nodejs npm
                }
                ;;
            "centos")
                curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash - 2>/dev/null && sudo yum install -y nodejs npm || {
                    sudo yum install -y nodejs npm
                }
                ;;
            "fedora")
                sudo dnf install -y nodejs npm
                ;;
            "arch")
                sudo pacman -S --noconfirm nodejs npm
                ;;
            "opensuse")
                sudo zypper install -y nodejs npm
                ;;
            "alpine")
                sudo apk add --no-cache nodejs npm
                ;;
            *)
                log_warning "无法在当前系统上自动安装 Node.js，将跳过相关功能"
                ;;
        esac
        
        if command -v node &> /dev/null; then
            log_success "Node.js 安装成功"
        else
            log_warning "Node.js 安装失败，将跳过npm相关功能"
        fi
    else
        log_success "Node.js 已安装"
    fi
    
    # 安装 curl（通常系统已有）
    if ! command -v curl &> /dev/null; then
        log_warning "curl 未安装，正在自动安装..."
        
        case $OS in
            "macos")
                brew install curl
                ;;
            "ubuntu"|"linux")
                sudo apt update && sudo apt install -y curl
                ;;
            "centos")
                sudo yum install -y curl
                ;;
            "fedora")
                sudo dnf install -y curl
                ;;
            "arch")
                sudo pacman -S --noconfirm curl
                ;;
            "opensuse")
                sudo zypper install -y curl
                ;;
            "alpine")
                sudo apk add --no-cache curl
                ;;
            *)
                log_error "无法在当前系统上自动安装 curl"
                exit 1
                ;;
        esac
        
        if command -v curl &> /dev/null; then
            log_success "curl 安装成功"
        else
            log_error "curl 安装失败"
            exit 1
        fi
    else
        log_success "curl 已安装"
    fi
    
    log_success "所有依赖检查完成！"
}

# 主函数
main() {
    log_info "开始创建 GitHub MCP 评估仓库..."
    
    # 首先自动安装所有依赖
    auto_install_dependencies
    
    # 先自动清理可能存在的仓库
    auto_cleanup_existing_repo
    
    # 创建GitHub仓库并推送内容
    create_temp_repo_and_push
    
    log_success "GitHub仓库创建完成！"
}

# 创建临时仓库并推送到GitHub
create_temp_repo_and_push() {
    log_info "创建临时仓库并推送内容到GitHub..."
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local original_dir=$(pwd)
    
    log_info "使用临时目录: $temp_dir"
    cd "$temp_dir"
    
    # 在临时目录中创建仓库内容
    create_project_structure
    create_test_files
    create_additional_files
    init_git_repo
    
    # 创建GitHub远程仓库
    create_github_repository_only
    
    # 先推送基础代码（不含敏感信息）
    log_info "第一步：推送基础代码到GitHub..."
    git remote add origin "https://github.com/$REPO_OWNER/$REPO_NAME.git"
    git push -u origin main
    git push -u origin develop  
    git push -u origin feature/security-improvements
    
    # 启用所有GitHub功能
    log_info "启用GitHub所有可用功能..."
    enable_all_github_features
    
    # 第二步：添加包含密钥的文件
    log_info "第二步：添加安全测试用的敏感信息..."
    add_security_test_secrets
    
    # 创建示例Issue
    log_info "创建示例Issue..."
    create_sample_issue
    
    # 创建产生通知的活动
    log_info "创建产生通知的活动..."
    create_notification_activities
    
    # 返回原目录并清理临时目录
    cd "$original_dir"
    rm -rf "$temp_dir"
    
    log_success "✅ GitHub仓库创建完成（包含所有内容和分支）"
}

# 自动清理已存在的仓库
auto_cleanup_existing_repo() {
    log_info "检查是否存在已有仓库..."
    
    # 检查GitHub CLI是否可用
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI未安装，跳过清理检查"
        return 0
    fi
    
    if ! gh auth status &> /dev/null; then
        log_warning "GitHub CLI未认证，跳过清理检查"
        return 0
    fi
    
    # 检查GitHub仓库是否存在
    if gh repo view "$REPO_OWNER/$REPO_NAME" &> /dev/null; then
        log_warning "发现已存在的GitHub仓库: $REPO_URL"
        log_info "自动删除已存在的仓库..."
        
        if gh repo delete "$REPO_OWNER/$REPO_NAME" --yes; then
            log_success "✅ 已存在的GitHub仓库已删除"
        else
            log_error "❌ 删除已存在仓库失败"
            log_info "请手动删除仓库或检查权限"
            return 1
        fi
    else
        log_info "未发现已存在的仓库，继续创建"
    fi
    
    # 检查本地目录（如果存在）
    if [ -d "$REPO_NAME" ]; then
        log_info "发现本地目录，自动删除..."
        rm -rf "$REPO_NAME"
        log_success "✅ 本地目录已删除"
    fi
}

# 只创建GitHub仓库
create_github_repository_only() {
    log_info "检查GitHub CLI环境..."
    
    # 检查gh CLI是否安装
    if ! command -v gh &> /dev/null; then
        log_error "GitHub CLI (gh) 未安装"
        log_info "请先安装GitHub CLI: https://cli.github.com/"
        log_info "或使用 brew install gh (macOS)"
        return 1
    fi
    
    # 检查gh认证状态，如果未认证则使用token自动认证
    if ! gh auth status &> /dev/null; then
        if [ -n "$GITHUB_TOKEN" ]; then
            log_info "使用token进行自动认证..."
            echo "$GITHUB_TOKEN" | gh auth login --with-token
            if gh auth status &> /dev/null; then
                log_success "GitHub CLI自动认证成功"
            else
                log_error "GitHub CLI自动认证失败"
                return 1
            fi
        else
            log_error "GitHub CLI未认证且未找到token"
            log_info "请先运行: gh auth login"
            return 1
        fi
    fi
    
    log_success "GitHub CLI环境检查通过"
    
    # 创建GitHub仓库
    log_info "创建GitHub仓库: $REPO_OWNER/$REPO_NAME"
    log_info "仓库描述: $REPO_DESCRIPTION"
    log_info "仓库可见性: $GITHUB_REPO_VISIBILITY"
    
    # 构建创建命令
    local create_cmd="gh repo create $REPO_OWNER/$REPO_NAME"
    create_cmd="$create_cmd --description \"$REPO_DESCRIPTION\""
    create_cmd="$create_cmd --$GITHUB_REPO_VISIBILITY"
    
    # 执行创建
    if eval "$create_cmd" 2>/dev/null; then
        log_success "✅ GitHub仓库创建成功"
        log_info "🌐 仓库URL: $REPO_URL"
        log_info "📝 可通过以下命令查看:"
        log_info "   gh repo view $REPO_OWNER/$REPO_NAME"
        log_info "   gh repo view --web $REPO_OWNER/$REPO_NAME"
        
        # 立即启用安全功能
        log_info "启用GitHub安全功能..."
        sleep 2  # 等待仓库完全创建
        
        # 启用高级安全功能和代码扫描
        gh api -X PATCH "repos/$REPO_OWNER/$REPO_NAME" \
            --field 'security_and_analysis.advanced_security.status=enabled' \
            --field 'security_and_analysis.secret_scanning.status=enabled' \
            --field 'security_and_analysis.secret_scanning_push_protection.status=disabled' \
            || log_warning "启用安全功能失败，可能需要手动操作"
        
        # 尝试启用代码扫描（CodeQL）
        log_info "尝试启用代码扫描功能..."
        gh api -X PUT "repos/$REPO_OWNER/$REPO_NAME/code-scanning/default-setup" \
            --field 'state=configured' \
            --field 'languages[]=javascript' \
            2>/dev/null || log_info "代码扫描将在首次工作流运行后自动启用"
        
        log_success "安全功能已启用，Push Protection已禁用"
    else
        log_warning "GitHub CLI创建失败，尝试使用REST API作为后备方案..."
        
        # 获取认证token
        local auth_token=$(gh auth token 2>/dev/null)
        if [ -z "$auth_token" ]; then
            log_error "无法获取认证token"
            return 1
        fi
        
        # 使用REST API作为后备方案
        log_info "使用REST API创建仓库..."
        local api_response=$(curl -s -w "
%{http_code}" -X POST \
            -H "Authorization: token $auth_token" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/user/repos \
            -d "{\"name\":\"$REPO_NAME\",\"description\":\"$REPO_DESCRIPTION\",\"public\":$([ "$GITHUB_REPO_VISIBILITY" = "public" ] && echo "true" || echo "false")}")
        
        local http_code=$(echo "$api_response" | tail -n1)
        local response_body=$(echo "$api_response" | sed '$d')
        
        if [ "$http_code" = "201" ]; then
            log_success "✅ GitHub仓库通过REST API创建成功"
            log_info "🌐 仓库URL: $REPO_URL"
            
            # 启用所有安全功能
            log_info "启用所有安全功能..."
            sleep 2
            curl -s -X PATCH \
                -H "Authorization: token $auth_token" \
                -H "Accept: application/vnd.github.v3+json" \
                "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME" \
                -d '{
                    "security_and_analysis": {
                        "advanced_security": {"status": "enabled"},
                        "secret_scanning": {"status": "enabled"},
                        "secret_scanning_push_protection": {"status": "disabled"},
                        "dependency_graph": {"status": "enabled"},
                        "dependabot_security_updates": {"status": "enabled"}
                    },
                    "has_issues": true,
                    "has_projects": true,
                    "has_wiki": true,
                    "has_downloads": true,
                    "allow_squash_merge": true,
                    "allow_merge_commit": true,
                    "allow_rebase_merge": true,
                    "allow_auto_merge": true,
                    "delete_branch_on_merge": true,
                    "allow_update_branch": true
                }' > /dev/null || log_warning "启用功能失败"
            
            log_success "所有可用功能已启用"
        else
            log_error "❌ REST API创建失败 (HTTP $http_code)"
            if [ "$http_code" = "422" ]; then
                log_info "仓库可能已存在，请检查"
            elif [ "$http_code" = "401" ]; then
                log_info "认证失败，请检查token权限"
            else
                log_info "响应: $(echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body" | head -3)"
            fi
            return 1
        fi
    fi
}

# 创建本地仓库目录
create_local_repo() {
    log_info "创建本地仓库目录..."
    
    # 如果目录已存在，询问是否删除（自动模式下直接删除）
    if [ -d "$REPO_NAME" ]; then
        log_warning "目录 $REPO_NAME 已存在"
        
        if [ "$AUTO_MODE" = "true" ]; then
            log_info "自动模式：删除旧目录"
            rm -rf "$REPO_NAME"
            log_info "已删除旧目录"
        else
            read -p "是否删除重建? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rm -rf "$REPO_NAME"
                log_info "已删除旧目录"
            else
                log_error "取消操作"
                exit 1
            fi
        fi
    fi
    
    mkdir -p "$REPO_NAME"
    cd "$REPO_NAME"
    log_success "创建目录: $REPO_NAME"
}

# 创建项目结构
create_project_structure() {
    log_info "创建轻量化项目目录结构..."
    
    # 创建轻量化的项目目录结构 - 优化后只保留核心目录
    mkdir -p src/{main,lib,utils,middleware,routes,models,services,validators}
    mkdir -p src/tests/{unit,integration,e2e,performance,security}
    mkdir -p src/config/{environments,database,security}
    mkdir -p docs/api
    mkdir -p scripts
    mkdir -p tools/{linting,formatting,git-hooks}
    mkdir -p public/{assets/{css,js,images},docs,downloads}
    mkdir -p private/{keys,internal-docs}
    mkdir -p .github/{workflows,ISSUE_TEMPLATE,PULL_REQUEST_TEMPLATE,actions,dependabot}
    mkdir -p .vscode/{settings,extensions,tasks,launch,snippets}
    mkdir -p infrastructure/terraform
    mkdir -p data
    
    # 立即为空目录添加 .gitkeep 文件，确保目录结构被正确跟踪
    create_gitkeep_files
    
    log_success "轻量化项目目录结构创建完成 (52个目录，支持25个测试场景)"
}

# 为空目录创建 .gitkeep 文件
create_gitkeep_files() {
    log_info "为空目录添加 .gitkeep 文件，确保在GitHub上显示..."
    
    # 使用find命令查找所有空目录并创建.gitkeep文件
    # 这比手动列举更可靠，能确保所有空目录都被处理
    find . -type d -empty -not -path "./.git*" -print0 | while IFS= read -r -d '' dir; do
        if [ "$dir" != "." ]; then
            echo "# This file ensures this directory is tracked by Git" > "$dir/.gitkeep"
            log_info "✓ 已添加 $dir/.gitkeep"
        fi
    done
    
    # 特别处理关键目录，确保它们有.gitkeep文件
    local critical_dirs=(
        "data"
        "scripts" 
        "docs/api"
        "infrastructure/terraform"
        "src/lib"
        "src/utils"
        "src/middleware"
        "src/models"
        "src/services"
        "src/validators"
        "private/keys"
        "private/internal-docs"
        ".vscode/snippets"
    )
    
    for dir in "${critical_dirs[@]}"; do
        if [ -d "$dir" ] && [ ! -f "$dir/.gitkeep" ]; then
            echo "# This file ensures this directory is tracked by Git" > "$dir/.gitkeep"
            log_info "✓ 特别处理：已添加 $dir/.gitkeep"
        fi
    done
    
    # 验证关键分层目录结构
    if [ -d "docs" ] && [ -d "docs/api" ] && [ -f "docs/api/.gitkeep" ]; then
        log_success "✓ docs/api 分层目录结构正确"
    else
        log_error "✗ docs/api 分层目录结构有问题"
    fi
    
    if [ -d "infrastructure" ] && [ -d "infrastructure/terraform" ] && [ -f "infrastructure/terraform/.gitkeep" ]; then
        log_success "✓ infrastructure/terraform 分层目录结构正确"
    else
        log_error "✗ infrastructure/terraform 分层目录结构有问题"
    fi
    
    log_success "所有空目录已添加 .gitkeep 文件，GitHub将显示正确的分层目录结构"
}

# 创建测试文件
create_test_files() {
    log_info "创建测试文件..."
    
    # 创建README.md
    cat > README.md << 'EOF'
# MCP GitHub 评估测试仓库

这是一个用于测试GitHub MCP Server功能的评估仓库。

## 🎯 测试场景

### 1. 代码开发流程
- 新功能开发流程
- Bug修复流程  
- 代码重构流程

### 2. 代码评审流程
- 标准评审流程
- Copilot辅助评审

### 3. Issue管理
- Bug报告处理
- 需求跟踪

### 4. CI/CD运维
- 构建失败处理
- 部署流程管理

### 5. 安全监控
- 安全扫描处理
- 漏洞修复流程

## 🔒 安全测试

此仓库包含故意设置的安全漏洞用于测试：
- 认证绕过问题
- 敏感信息泄露
- 弱密码策略
- 日志信息泄露

**注意**: 仅用于测试目的，请勿在生产环境使用。
EOF

    # 创建带有安全问题的示例代码
    cat > src/main/user-controller.js << 'EOF'
const express = require('express');
const router = express.Router();

// 用户登录接口
router.post('/login', async (req, res) => {
    const { username, password } = req.body;
    
    // 正常的登录逻辑
    const user = await User.findByCredentials(username, password);
    if (user) {
        const token = generateJWT(user);
        res.json({ success: true, token });
    } else {
        res.status(401).json({ error: 'Invalid credentials' });
    }
});

// 获取用户profile - 故意的安全漏洞 (CVE-PENDING-001)
router.get('/profile/:id', async (req, res) => {
    const userId = req.params.id;
    
    // 缺少认证验证 - 这是故意的安全漏洞
    const user = await User.findById(userId);
    res.json(user);
});

module.exports = router;
EOF

    # 创建基础的安全测试文件（暂不包含真实密钥）
    cat > src/main/vulnerable-code.js << 'EOF'
// 这个文件包含安全漏洞，用于测试安全扫描功能
const express = require('express');
const app = express();

// 占位符 - 敏感信息将在后续步骤添加
const PLACEHOLDER_KEY = "PLACEHOLDER_FOR_SECURITY_TESTING";
const DB_PASSWORD = "admin123";  // 硬编码密码

// SQL注入漏洞
function getUserData(userId) {
    const query = `SELECT * FROM users WHERE id = ${userId}`;  // SQL注入
    return database.query(query);
}

// XSS漏洞
app.get('/search', (req, res) => {
    const query = req.query.q;
    res.send(`<h1>Search results for: ${query}</h1>`);  // XSS
});

// 不安全的随机数生成
function generateToken() {
    return Math.random().toString(36);  // 不安全的随机数
}

module.exports = { getUserData, generateToken };
EOF

    # 创建示例测试文件
    cat > src/tests/sample-test.js << 'EOF'
// 示例测试文件，用于测试代码搜索功能
describe('Sample Tests', () => {
    test('should pass basic test', () => {
        expect(1 + 1).toBe(2);
    });

    test('should test string operations', () => {
        const result = 'hello world';
        expect(result).toContain('hello');
    });
});

// TODO: 添加更多测试用例
// FIXME: 修复getUserData函数的安全问题
EOF

    # 创建Jest配置文件
    cat > jest.config.js << 'EOF'
module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/src/tests/**/*.js'],
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};
EOF

    # 创建基础的index.js文件
    cat > src/main/index.js << 'EOF'
// 主应用程序入口点
console.log('MCP GitHub Evaluation Server Starting...');

// 简单的HTTP服务器示例
const http = require('http');

const server = http.createServer((req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/plain' });
    res.end('MCP GitHub Evaluation Server is running!');
});

const port = process.env.PORT || 3000;
server.listen(port, () => {
    console.log(`Server running on port ${port}`);
});

module.exports = server;
EOF

    # 创建.gitignore文件
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production

# VS Code settings (keep some, ignore others)
.vscode/settings.json

# macOS
.DS_Store

# Windows
Thumbs.db

# JetBrains IDEs
.idea/

# Logs
logs
*.log

# Runtime cache
.cache/

# Build output
dist/
build/
EOF

    # 创建ESLint配置文件
    cat > .eslintrc.js << 'EOF'
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    'no-console': 'off',
    'no-unused-vars': 'warn',
    'no-undef': 'error'
  }
};
EOF

    # 创建基础配置文件（暂不包含真实密钥）
    cat > src/main/config.js << 'EOF'
// 配置文件 - 故意的安全漏洞 (CVE-PENDING-002)
module.exports = {
    database: {
        host: 'localhost',
        user: 'admin',
        password: 'admin123',  // 硬编码密码 - 故意的安全漏洞
        database: 'mcp_test'
    },
    jwt: {
        secret: 'secret123'  // 弱密钥 - 故意的安全漏洞
    },
    api: {
        placeholder: 'PLACEHOLDER_FOR_SECURITY_TESTING'  // 占位符，真实密钥将后续添加
    }
};
EOF

    # 创建GitHub工作流
    cat > .github/workflows/ci.yml << 'EOF'
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # 允许手动触发工作流
    inputs:
      environment:
        description: 'Environment to run tests against'
        required: false
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci || npm install
      
    - name: Run tests
      run: npm test || echo "Tests not implemented yet"
      
    - name: Run linting
      run: npm run lint || echo "Linting configuration needed"
      
    - name: Display environment
      run: echo "Running tests in ${{ github.event.inputs.environment || 'development' }} environment"
      
    - name: Check package files
      run: |
        echo "=== Package files ==="
        ls -la package*.json || echo "No package files found"
        echo "=== Node version ==="
        node --version
        echo "=== NPM version ==="
        npm --version
EOF

    # 创建安全扫描工作流
    cat > .github/workflows/security-scan.yml << 'EOF'
name: Security Scan

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # 允许手动触发安全扫描
    inputs:
      scan_type:
        description: 'Type of security scan to run'
        required: false
        default: 'basic'
        type: choice
        options:
          - basic
          - codeql
          - dependencies

jobs:
  security:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci || npm install
    
    - name: Run basic security checks
      if: github.event.inputs.scan_type == 'basic' || github.event.inputs.scan_type == null
      run: |
        echo "=== Running basic security checks ==="
        echo "Checking for common vulnerabilities..."
        # 检查package.json中的已知漏洞
        npm audit || echo "npm audit completed with warnings"
        
        # 简单的代码安全检查
        echo "Checking for hardcoded secrets..."
        grep -r "password\|secret\|key\|token" src/ || echo "No obvious secrets found in src/"
        
        echo "Basic security scan completed"
    
    - name: Initialize CodeQL
      if: github.event.inputs.scan_type == 'codeql'
      uses: github/codeql-action/init@v3
      with:
        languages: javascript
        queries: security-extended
    
    - name: Autobuild
      if: github.event.inputs.scan_type == 'codeql'
      uses: github/codeql-action/autobuild@v3
    
    - name: Run CodeQL Analysis
      if: github.event.inputs.scan_type == 'codeql'
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:javascript"
        upload: false  # 不上传结果，避免权限问题
      
    - name: Run dependency security check
      if: github.event.inputs.scan_type == 'dependencies'
      run: |
        echo "=== Dependency Security Check ==="
        npm audit --audit-level=moderate || echo "Dependency check completed"
        echo "For comprehensive dependency scanning, enable Dependabot in repository settings"
      
    - name: Display scan summary
      run: |
        echo "=== Security Scan Summary ==="
        echo "Scan type: ${{ github.event.inputs.scan_type || 'basic' }}"
        echo "Repository: ${{ github.repository }}"
        echo "Workflow completed successfully"
EOF

    # 跳过Dependabot配置（避免自动创建PR）
    # cat > .github/dependabot.yml << 'EOF'
    # version: 2
    # updates:
    #   - package-ecosystem: "npm"
    #     directory: "/"
    #     schedule:
    #       interval: "daily"
    #     open-pull-requests-limit: 10
    #     reviewers:
    #       - "TikyFirstggg"
    #     assignees:
    #       - "TikyFirstggg"
    # EOF
    
    log_info "跳过Dependabot配置，避免自动创建PR"

    # 创建package.json
    cat > package.json << 'EOF'
{
  "name": "mcp-github-evaluation",
  "version": "1.0.0",
  "description": "GitHub MCP Server 功能评估测试仓库 - 轻量化版本",
  "main": "src/main/index.js",
  "scripts": {
    "start": "node src/main/index.js",
    "test": "jest",
    "lint": "eslint src/",
    "dev": "nodemon src/main/index.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "jest": "^29.0.0",
    "eslint": "^8.0.0",
    "nodemon": "^3.0.0"
  }
}
EOF

    # 创建一个.npmrc配置文件确保兼容性
    cat > .npmrc << 'EOF'
# 确保npm兼容性
legacy-peer-deps=true
save-exact=false
EOF

    # 创建Issue模板
    cat > .github/ISSUE_TEMPLATE/bug_report.md << 'EOF'
---
name: Bug Report
about: 创建bug报告
title: '[BUG] '
labels: bug
assignees: ''
---

**Bug描述**
简单明了地描述bug是什么。

**重现步骤**
重现该行为的步骤：
1. 转到 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

**预期行为**
清楚简洁地描述您期望发生的事情。

**屏幕截图**
如果适用，请添加屏幕截图以帮助解释您的问题。

**环境信息:**
 - OS: [e.g. iOS]
 - Browser [e.g. chrome, safari]
 - Version [e.g. 22]
EOF

    # 创建缺失的重要文件
    create_additional_files
    
    # 安装npm依赖并生成package-lock.json
    install_npm_dependencies
    
    log_success "所有测试文件创建完成"
}

# 安装npm依赖并生成package-lock.json
install_npm_dependencies() {
    log_info "安装npm依赖并生成package-lock.json..."
    
    # 检查npm是否可用
    if ! command -v npm &> /dev/null; then
        log_warning "npm未安装，跳过依赖安装"
        log_info "GitHub Actions可能会失败，因为缺少package-lock.json"
        return 0
    fi
    
    # 安装依赖（使用--package-lock-only只生成锁文件，不下载node_modules）
    log_info "生成package-lock.json文件..."
    npm install --package-lock-only 2>/dev/null || {
        log_warning "package-lock-only失败，尝试完整安装..."
        npm install 2>/dev/null || {
            log_warning "npm install失败，将手动创建基础的package-lock.json"
            create_basic_package_lock
        }
    }
    
    if [ -f "package-lock.json" ]; then
        log_success "✅ package-lock.json已生成"
    else
        log_warning "package-lock.json生成失败"
    fi
}

# 创建基础的package-lock.json（作为后备方案）
create_basic_package_lock() {
    cat > package-lock.json << 'EOF'
{
  "name": "mcp-github-evaluation",
  "version": "1.0.0",
  "lockfileVersion": 3,
  "requires": true,
  "packages": {
    "": {
      "name": "mcp-github-evaluation",
      "version": "1.0.0",
      "dependencies": {
        "express": "^4.18.2",
        "lodash": "^4.17.21"
      },
      "devDependencies": {
        "eslint": "^8.0.0",
        "jest": "^29.0.0",
        "nodemon": "^3.0.0"
      }
    }
  }
}
EOF
    log_info "已创建基础的package-lock.json文件"
}

# 创建额外的重要文件
create_additional_files() {
    log_info "创建额外的重要文件..."
    
    # 创建 VS Code 配置
    create_vscode_config
    
    # 创建开发工具文件
    create_tools_files
    
    # 创建公共资源文件
    create_public_files
    
    # 创建私有资源文件
    create_private_files
    
    # 创建基础设施文件
    create_infrastructure_files
    
    log_success "额外文件创建完成"
}

# 创建 VS Code 配置
create_vscode_config() {
    # 工作区设置
    cat > .vscode/settings.json << 'EOF'
{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/coverage": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/tmp": true
  },
  "eslint.workingDirectories": ["src"],
  "javascript.preferences.importModuleSpecifier": "relative",
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  }
}
EOF

    # 推荐扩展
    cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "github.copilot",
    "github.copilot-chat"
  ]
}
EOF

    # 任务配置
    cat > .vscode/tasks.json << 'EOF'
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "npm: start",
      "type": "npm",
      "script": "start",
      "group": {
        "kind": "build",
        "isDefault": true
      }
    },
    {
      "label": "npm: test",
      "type": "npm",
      "script": "test",
      "group": "test"
    },
    {
      "label": "npm: lint",
      "type": "npm",
      "script": "lint"
    }
  ]
}
EOF

    # 调试配置
    cat > .vscode/launch.json << 'EOF'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Program",
      "program": "${workspaceFolder}/src/main/index.js",
      "request": "launch",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node"
    },
    {
      "name": "Attach to Process",
      "request": "attach",
      "port": 9229,
      "type": "node"
    }
  ]
}
EOF
}

# 创建开发工具文件
create_tools_files() {
    # ESLint配置
    cat > tools/linting/.eslintrc.js << 'EOF'
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    jest: true
  },
  extends: [
    'eslint:recommended',
    'plugin:security/recommended'
  ],
  plugins: ['security'],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    'no-console': 'warn',
    'no-debugger': 'error',
    'security/detect-hardcoded-credentials': 'error',
    'security/detect-sql-injection': 'error'
  }
};
EOF

    # Prettier配置
    cat > tools/formatting/.prettierrc << 'EOF'
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
EOF


    # Git hooks
    cat > tools/git-hooks/pre-commit << 'EOF'
#!/bin/bash
echo "🔍 Running pre-commit checks..."
npm run lint && npm test
EOF
    chmod +x tools/git-hooks/pre-commit
}

# 创建公共资源文件
create_public_files() {
    # CSS样式文件
    cat > public/assets/css/main.css << 'EOF'
:root {
  --primary-color: #0366d6;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
EOF

    # JavaScript工具文件
    cat > public/assets/js/utils.js << 'EOF'
class Utils {
  static formatDate(date) {
    return new Intl.DateTimeFormat('zh-CN').format(date);
  }

  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

window.Utils = Utils;
EOF

    # API示例文档
    cat > public/docs/api-examples.md << 'EOF'
# API 使用示例

## 认证相关

### 用户登录
```javascript
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'password' })
});
```
EOF

    # 示例数据文件
    cat > public/downloads/sample-data.json << 'EOF'
{
  "users": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "administrator"
    }
  ]
}
EOF
}

# 创建私有资源文件
create_private_files() {
    # 内部文档
    cat > private/internal-docs/security-guidelines.md << 'EOF'
# 内部安全指南

## 密钥管理
- 所有密钥必须使用环境变量
- 定期轮换API密钥

## 代码安全
- 禁止硬编码凭据
- 使用安全的加密算法
EOF

    # 私钥占位文件
    cat > private/keys/README.md << 'EOF'
# 私钥存储目录

**警告**: 此目录用于存储私钥文件，不应提交到版本控制系统。

请将私钥文件添加到 .gitignore 中。
EOF
}

# 创建基础设施文件
create_infrastructure_files() {
    # Terraform配置
    cat > infrastructure/terraform/main.tf << 'EOF'
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}
EOF
}

# 初始化git仓库
init_git_repo() {
    log_info "初始化git仓库..."
    
    git init
    git config user.name "MCP Evaluation" 
    git config user.email "<EMAIL>"
    
    # 验证目录结构是否正确
    log_info "验证目录结构..."
    if [ -d "docs/api" ] && [ -d "infrastructure/terraform" ]; then
        log_success "✓ 分层目录结构正确"
    else
        log_error "✗ 分层目录结构不正确"
        return 1
    fi
    
    # 显示将要提交的文件结构
    log_info "将要提交的目录结构："
    find . -type d -name ".git" -prune -o -type d -print | head -20
    
    git add .
    git commit -m "Initial commit: MCP GitHub evaluation environment

📁 Project structure created with:
- 24 core files for testing
- 8 main testing scenarios
- 25 sub-scenarios for comprehensive evaluation
- Lightweight Git history for MCP testing

🎯 Ready for GitHub MCP Server capability evaluation"

    # 创建develop分支
    git checkout -b develop
    echo "# Development Branch" >> README.md
    git add README.md
    git commit -m "docs: add development branch note"
    
    # 创建feature分支
    git checkout -b feature/security-improvements
    echo "# Security Improvements" >> README.md
    git add README.md
    git commit -m "docs: add security improvements section"
    
    git checkout main
    
    log_success "Git仓库初始化完成"
}

# 显示测试场景
show_test_scenarios() {
    echo ""
    echo "📋 MCP评估测试场景 (8个主场景，25个子场景):"
    echo "   🔧 代码开发流程 (4个子场景) - 分支管理、文件操作、PR管理"
    echo "   🔍 代码评审流程 (3个子场景) - PR审查、评论、差异查看"
    echo "   📋 Issue管理 (3个子场景) - 创建更新、评论、搜索"
    echo "   ⚙️ CI/CD运维 (3个子场景) - 工作流、构建状态、部署"
    echo "   🛡️ 安全监控 (3个子场景) - 代码扫描、依赖检测、密钥扫描"
    echo "   🔎 代码搜索 (3个子场景) - 代码搜索、仓库搜索、用户搜索"
    echo "   🔔 通知管理 (3个子场景) - 获取通知、处理、订阅管理"
    echo "   🏷️ 版本管理 (3个子场景) - 标签管理、提交历史、发布管理"
    echo ""
    echo "🚀 下一步操作:"
    echo "   cd $REPO_NAME"
    echo "   git status"
    echo "   git log --oneline --all"
    echo "   git branch -a"
    echo ""
    echo "📚 轻量化项目特点:"
    echo "   🎯 支持所有25个子场景测试"
    echo "   📁 52个目录，24个核心文件"
    echo "   🐛 包含安全漏洞供测试 (4种类型)"
    echo "   🔄 3个Git分支 (main/develop/feature)"
    echo "   🧪 完整的测试环境"
    echo "   🔧 VS Code完整配置"
    echo "   🏗️ 基础设施代码 (Terraform)"
    echo "   ⚡ 轻量化但功能完整"
    echo ""
}

# 启用GitHub所有可用功能
enable_all_github_features() {
    log_info "启用GitHub仓库所有可用功能..."
    
    # 检查GitHub CLI是否可用
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI未安装，跳过功能配置"
        return 0
    fi
    
    # 1. 启用基础安全功能
    log_info "🔒 启用基础安全功能..."
    gh api -X PUT "repos/$REPO_OWNER/$REPO_NAME/vulnerability-alerts" || log_warning "启用漏洞告警失败"
    gh api -X PUT "repos/$REPO_OWNER/$REPO_NAME/automated-security-fixes" || log_warning "启用自动安全修复失败"
    
    # 2. 启用高级安全功能（包括CodeQL代码扫描）
    log_info "🔍 启用高级安全功能..."
    gh api -X PATCH "repos/$REPO_OWNER/$REPO_NAME" \
        --field 'security_and_analysis.advanced_security.status=enabled' \
        --field 'security_and_analysis.secret_scanning.status=enabled' \
        --field 'security_and_analysis.secret_scanning_push_protection.status=disabled' \
        --field 'security_and_analysis.dependency_graph.status=enabled' \
        --field 'security_and_analysis.dependabot_security_updates.status=enabled' \
        || log_warning "配置高级安全功能失败"
    
    # 尝试启用代码扫描默认设置
    log_info "🔍 启用代码扫描默认设置..."
    gh api -X PUT "repos/$REPO_OWNER/$REPO_NAME/code-scanning/default-setup" \
        --field 'state=configured' \
        --field 'languages[]=javascript' \
        2>/dev/null || log_info "代码扫描将通过工作流自动配置"
    
    # 3. 启用仓库功能
    log_info "📚 启用仓库功能..."
    gh api -X PATCH "repos/$REPO_OWNER/$REPO_NAME" \
        --field 'has_issues=true' \
        --field 'has_projects=true' \
        --field 'has_wiki=true' \
        --field 'has_pages=false' \
        --field 'has_downloads=true' \
        --field 'allow_squash_merge=true' \
        --field 'allow_merge_commit=true' \
        --field 'allow_rebase_merge=true' \
        --field 'allow_auto_merge=true' \
        --field 'delete_branch_on_merge=true' \
        --field 'allow_update_branch=true' \
        || log_warning "启用仓库功能失败"
    
    # 4. 配置分支保护（针对main分支）
    log_info "🛡️ 配置分支保护策略..."
    gh api -X PUT "repos/$REPO_OWNER/$REPO_NAME/branches/main/protection" \
        --field 'required_status_checks.strict=false' \
        --field 'required_status_checks.contexts=[]' \
        --field 'enforce_admins=false' \
        --field 'required_pull_request_reviews.required_approving_review_count=0' \
        --field 'required_pull_request_reviews.dismiss_stale_reviews=false' \
        --field 'required_pull_request_reviews.require_code_owner_reviews=false' \
        --field 'restrictions=null' \
        --field 'allow_force_pushes=true' \
        --field 'allow_deletions=false' \
        2>/dev/null || log_warning "分支保护配置失败（可能是仅有一个提交的新仓库）"
    
    # 5. 启用GitHub Pages（如果需要）
    log_info "📄 检查GitHub Pages配置..."
    gh api -X POST "repos/$REPO_OWNER/$REPO_NAME/pages" \
        --field 'source.branch=main' \
        --field 'source.path=/' \
        2>/dev/null || log_info "GitHub Pages未启用或已存在"
    
    # 6. 启用项目功能
    log_info "📊 启用项目管理功能..."
    # Projects v2 需要单独的API调用
    gh api graphql -f query='
        mutation {
          createProjectV2(input: {
            ownerId: "'$(gh api user --jq .node_id)'"
            title: "MCP Evaluation Project"
          }) {
            projectV2 {
              id
              number
            }
          }
        }' 2>/dev/null || log_info "项目创建跳过（可能已存在）"
    
    # 7. 启用Discussions（如果支持）
    log_info "💬 启用Discussions功能..."
    gh api -X PATCH "repos/$REPO_OWNER/$REPO_NAME" \
        --field 'has_discussions=true' \
        2>/dev/null || log_warning "Discussions功能启用失败"
    
    # 8. 配置默认标签
    log_info "🏷️ 创建默认标签..."
    create_default_labels
    
    # 9. 等待设置生效
    log_info "⏳ 等待所有功能生效..."
    sleep 8
    
    # 10. 验证功能状态
    verify_enabled_features
    
    log_success "✅ 所有可用功能已启用"
}

# 创建默认标签
create_default_labels() {
    local labels=(
        "bug:d73a4a:Something isn't working"
        "enhancement:a2eeef:New feature or request"
        "documentation:0075ca:Improvements or additions to documentation"
        "good first issue:7057ff:Good for newcomers"
        "help wanted:008672:Extra attention is needed"
        "invalid:e4e669:This doesn't seem right"
        "question:d876e3:Further information is requested"
        "wontfix:ffffff:This will not be worked on"
        "duplicate:cfd3d7:This issue or pull request already exists"
        "security:d73a4a:Security related issues"
        "performance:ffb700:Performance improvements"
        "testing:1d76db:Related to testing"
        "ci/cd:28a745:Continuous Integration/Deployment"
        "dependencies:0366d6:Pull requests that update a dependency file"
        "javascript:f1e05a:JavaScript related"
        "typescript:3178c6:TypeScript related"
        "high priority:b60205:High priority issue"
        "medium priority:fbca04:Medium priority issue"
        "low priority:0e8a16:Low priority issue"
    )
    
    for label_info in "${labels[@]}"; do
        IFS=':' read -r name color description <<< "$label_info"
        gh api -X POST "repos/$REPO_OWNER/$REPO_NAME/labels" \
            --field "name=$name" \
            --field "color=$color" \
            --field "description=$description" \
            2>/dev/null || true
    done
}

# 验证启用的功能
verify_enabled_features() {
    log_info "📋 验证启用的功能..."
    
    # 检查安全功能
    if gh api "repos/$REPO_OWNER/$REPO_NAME" --jq '.security_and_analysis.advanced_security.status' 2>/dev/null | grep -q "enabled"; then
        log_success "✅ 高级安全功能已启用"
    else
        log_warning "⚠️  高级安全功能状态未确认"
    fi
    
    # 检查代码扫描状态
    if gh api "repos/$REPO_OWNER/$REPO_NAME/code-scanning/default-setup" 2>/dev/null | jq -r '.state' | grep -q "configured"; then
        log_success "✅ 代码扫描默认设置已配置"
    else
        log_info "💡 代码扫描将在首次工作流运行后启用"
    fi
    
    # 检查基础功能
    local repo_info=$(gh api "repos/$REPO_OWNER/$REPO_NAME" 2>/dev/null)
    if echo "$repo_info" | jq -r '.has_issues' | grep -q "true"; then
        log_success "✅ Issues功能已启用"
    fi
    if echo "$repo_info" | jq -r '.has_projects' | grep -q "true"; then
        log_success "✅ Projects功能已启用"
    fi
    if echo "$repo_info" | jq -r '.has_wiki' | grep -q "true"; then
        log_success "✅ Wiki功能已启用"
    fi
    
    log_info "📊 完整功能状态已记录"
}

# 添加安全测试密钥
add_security_test_secrets() {
    log_info "添加安全测试用的敏感信息..."
    
    # Push Protection已在创建仓库时禁用，无需再次操作
    
    # 更新vulnerable-code.js添加真实的测试密钥
    cat > src/main/vulnerable-code.js << 'EOF'
// 这个文件包含安全漏洞，用于测试安全扫描功能
const express = require('express');
const app = express();

// 硬编码敏感信息 - 安全漏洞
const OPENAI_API_KEY = "***************************************************";  // OpenAI API密钥泄露
const AWS_ACCESS_KEY = "AKIAIOSFODNN7EXAMPLE";  // AWS访问密钥泄露
const AWS_SECRET_KEY = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";  // AWS密钥泄露
const GITHUB_TOKEN = "ghp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";  // GitHub Token泄露
const DB_PASSWORD = "admin123";  // 硬编码密码

// SQL注入漏洞
function getUserData(userId) {
    const query = `SELECT * FROM users WHERE id = ${userId}`;  // SQL注入
    return database.query(query);
}

// XSS漏洞
app.get('/search', (req, res) => {
    const query = req.query.q;
    res.send(`<h1>Search results for: ${query}</h1>`);  // XSS
});

// 不安全的随机数生成
function generateToken() {
    return Math.random().toString(36);  // 不安全的随机数
}

module.exports = { getUserData, generateToken };
EOF

    # 更新config.js添加真实的测试密钥
    cat > src/main/config.js << 'EOF'
// 配置文件 - 故意的安全漏洞 (CVE-PENDING-002)
module.exports = {
    database: {
        host: 'localhost',
        user: 'admin',
        password: 'admin123',  // 硬编码密码 - 故意的安全漏洞
        database: 'mcp_test'
    },
    jwt: {
        secret: 'secret123'  // 弱密钥 - 故意的安全漏洞
    },
    api: {
        openai_key: '***************************************************',  // OpenAI API密钥泄露
        aws_access_key: 'AKIAIOSFODNN7EXAMPLE',  // AWS访问密钥泄露
        aws_secret_key: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'  // AWS密钥泄露
    }
};
EOF

    # 提交并推送包含密钥的更新
    git add src/main/vulnerable-code.js src/main/config.js
    git commit -m "feat: add security test secrets for scanning validation

🔒 Added real API keys for security scanning tests:
- OpenAI API key in vulnerable-code.js and config.js  
- AWS access/secret keys for comprehensive testing
- GitHub token placeholder

These are intentional security vulnerabilities for testing GitHub's
secret scanning and alert generation capabilities."

    # 再次确保Push Protection被禁用
    log_info "再次禁用Push Protection..."
    gh api -X PATCH "repos/$REPO_OWNER/$REPO_NAME" \
        --field 'security_and_analysis.secret_scanning_push_protection.status=disabled' \
        2>/dev/null || true
    
    # 等待设置生效
    sleep 3
    
    # 尝试推送
    log_info "推送包含测试密钥的代码..."
    push_output=$(git push origin main 2>&1 || true)
    
    # 检查是否被Push Protection阻止
    if echo "$push_output" | grep -q "GITHUB PUSH PROTECTION"; then
        log_warning "⚠️ Push Protection检测到秘密并阻止推送"
        
        # 提取unblock URL
        unblock_url=$(echo "$push_output" | grep -o 'https://github.com/[^/]*/[^/]*/security/secret-scanning/unblock-secret/[^[:space:]]*' | head -1)
        
        if [ -n "$unblock_url" ]; then
            log_info "📝 找到unblock URL: $unblock_url"
            log_info "🔓 通过GitHub CLI自动授权允许该秘密..."
            
            # 使用GitHub CLI访问unblock URL进行授权
            if gh api --method POST "${unblock_url#https://github.com/}" 2>/dev/null; then
                log_success "✅ 秘密已授权允许"
                
                # 重新推送
                log_info "🔄 重新推送代码..."
                if git push origin main; then
                    log_success "✅ 推送成功！测试秘密已包含在代码中"
                else
                    log_error "❌ 重新推送仍然失败"
                fi
            else
                log_warning "⚠️ 自动授权失败，需要手动访问: $unblock_url"
                open "$unblock_url" 2>/dev/null || true
                log_info "💡 请手动访问上述URL授权后，运行以下命令重新推送:"
                log_info "    cd $REPO_NAME && git push origin main"
            fi
        else
            log_error "❌ 未找到unblock URL，无法自动授权"
            log_info "推送输出："
            echo "$push_output"
        fi
    elif echo "$push_output" | grep -q "Everything up-to-date\|To https://github.com"; then
        log_success "✅ 推送成功！测试秘密已包含在代码中"
    else
        log_error "❌ 推送失败，原因未知"
        log_info "推送输出："
        echo "$push_output"
    fi
    
    
    log_success "安全测试密钥添加完成"
}

# 创建必要的标签
create_labels() {
    log_info "创建必要的标签..."
    
    # 检查GitHub CLI是否可用
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI未安装，跳过标签创建"
        return 0
    fi
    
    # 创建标签（如果已存在会被忽略）
    gh label create security --color "d73a4a" --description "Security related issues" 2>/dev/null || true
    gh label create bug --color "d73a4a" --description "Something isn't working" 2>/dev/null || true
    gh label create "good first issue" --color "7057ff" --description "Good for newcomers" 2>/dev/null || true
    gh label create dependencies --color "0366d6" --description "Pull requests that update a dependency file" 2>/dev/null || true
    gh label create javascript --color "f1e05a" --description "JavaScript related" 2>/dev/null || true
    
    log_success "标签创建完成"
}

# 创建示例Issue
create_sample_issue() {
    log_info "创建示例Issue用于MCP工具测试..."
    
    # 检查GitHub CLI是否可用
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI未安装，跳过Issue创建"
        return 0
    fi
    
    # 先创建必要的标签
    create_labels
    
    # 创建安全漏洞修复Issue
    log_info "创建安全漏洞修复Issue..."
    gh issue create \
        --title "Fix security vulnerabilities in codebase" \
        --body "## 描述
发现代码库中存在多个安全漏洞需要修复：

### 🔍 发现的安全问题
1. **SQL注入漏洞** - src/main/vulnerable-code.js:11
   - 直接拼接SQL查询，存在注入风险
   
2. **XSS漏洞** - src/main/vulnerable-code.js:18  
   - 直接输出用户输入，未进行转义
   
3. **硬编码密码** - src/main/config.js:6,10,13
   - 配置文件包含硬编码的敏感信息
   
4. **认证绕过** - src/main/user-controller.js
   - /profile/:id 端点缺少认证验证

### 📋 修复计划
- [ ] 使用参数化查询修复SQL注入问题
- [ ] 添加输出转义防止XSS攻击  
- [ ] 使用环境变量管理敏感配置
- [ ] 为所有API端点添加认证验证

### 🎯 优先级
**高优先级** - 这些是故意设置的测试漏洞，用于验证GitHub安全扫描功能。

**注意**: 此Issue用于MCP工具功能测试，实际修复时请保留测试用途的漏洞代码。" \
        --label "security,bug,good first issue" \
        --assignee "$REPO_OWNER" || log_warning "创建Issue失败"
    
    log_success "示例Issue创建完成"
}

# 创建产生通知的活动
create_notification_activities() {
    log_info "创建产生通知的活动用于测试notification工具..."
    
    # 检查GitHub CLI是否可用
    if ! command -v gh &> /dev/null; then
        log_warning "GitHub CLI未安装，跳过通知活动创建"
        return 0
    fi
    
    # 获取刚创建的Issue编号
    local issue_number
    issue_number=$(gh issue list --limit 1 --json number --jq '.[0].number' 2>/dev/null)
    
    if [ -z "$issue_number" ] || [ "$issue_number" = "null" ]; then
        log_warning "未找到Issue，创建新Issue用于通知测试..."
        gh issue create \
            --title "Notification Test Issue" \
            --body "This issue is created to generate notifications for MCP tool testing." \
            --label "test" || {
            log_warning "创建测试Issue失败，跳过通知活动"
            return 0
        }
        issue_number=$(gh issue list --limit 1 --json number --jq '.[0].number' 2>/dev/null)
    fi
    
    if [ -n "$issue_number" ] && [ "$issue_number" != "null" ]; then
        log_info "为Issue #$issue_number 添加评论和@提及来产生通知..."
        
        # 添加包含@提及的评论（这会产生通知）
        gh issue comment "$issue_number" \
            --body "👋 @$REPO_OWNER 这是一个测试评论，用于生成GitHub通知。

📝 **通知测试说明**:
- 此评论包含@提及，会为被提及用户产生通知
- 用于测试MCP notification相关工具的功能
- 包括 list_notifications, dismiss_notification 等

🔧 **MCP工具测试用途**:
- list_notifications: 获取通知列表
- dismiss_notification: 标记通知为已读
- get_notification_details: 获取通知详情

✅ 通知已成功生成，可以使用MCP工具进行测试了！" || log_warning "添加评论失败"
        
        log_success "✅ 通知活动创建完成，已为 @$REPO_OWNER 生成通知"
    else
        log_warning "无法获取Issue编号，跳过通知活动创建"
    fi
}

# 错误处理
handle_error() {
    log_error "脚本执行失败: $1"
    echo "请检查错误信息并重新运行脚本"
    exit 1
}

# 设置错误处理
trap 'handle_error "在第 $LINENO 行发生错误"' ERR

# 执行主函数
main "$@"