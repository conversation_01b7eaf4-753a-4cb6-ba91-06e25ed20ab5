/media/psf/Home
任务执行失败，准备重试

INPUT_FILE=/Users/<USER>/code/query/firecrawl_amaps_test.jsonl MODELS='assistant_api:ERNIE-4.5-single-custom-yiyanweb' MAX_WORKERS=2 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_personal_fininal.jsonl MODELS='anquan:doubao-1-5-thinking-pro-250415' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_learn_manager.jsonl MODELS='anquan:doubao-1-5-thinking-pro-250415' MAX_WORKERS=8 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_smart_client.jsonl MODELS='anquan:doubao-1-5-thinking-pro-250415' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='assistant_api:ERNIE-4.5-single-custom-yiyanweb' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='uni_crawl:qwen3235ba22b_pool,anquan:deepseek-r1-0528,anquan:doubao-1-5-thinking-pro-250415,anquan:gpt-4.1' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked.jsonl MODELS='assistant_api:ERNIE-4.5-single-custom-yiyanweb,anquan:qwen3-235b-a22b,anquan:deepseek-r1-0528,anquan:doubao-1-5-thinking-pro-250415,anquan:gpt-4.1' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/firecrawl_train.jsonl MODELS='anquan:deepseek-r1-0528' MAX_WORKERS=8 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='anquan:qwen3-235b-a22b' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked.jsonl MODELS='anquan:qwen3-235b-a22b' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked_4.jsonl MODELS='anquan:qwen3-235b-a22b' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked_4.jsonl MODELS='assistant_api:ERNIE-4.5-single-custom-yiyanweb,uni_crawl:deepseekreasoner_0528_pool' MAX_WORKERS=1 RUN_MODE=k8s make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part2.jsonl MODELS='anquan:doubao-1-5-thinking-pro-250415' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part3.jsonl MODELS='anquan:doubao-1-5-thinking-pro-250415' MAX_WORKERS=4 make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part3.jsonl MODELS='openai:gemini-2.5-pro-preview-06-05' MAX_WORKERS=8 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part2.jsonl MODELS='openai:gemini-2.5-flash-preview-05-20' MAX_WORKERS=8 make run-mcp-eval


INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part2.jsonl MODELS='anquan:qwen3-235b-a22b' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part3.jsonl MODELS='anquan:deepseek-r1-250528' MAX_WORKERS=4 make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part2.jsonl MODELS='openai:claude-sonnet-4-20250514' MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/firecrawl_train_part3.jsonl MODELS='anquan:gpt-4.1' MAX_WORKERS=4 make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='uni_crawl:deepseek-r1-250528' MAX_WORKERS=4 RUN_MODE=k8s make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='openai:deepseek-reasoner' MAX_WORKERS=20 RUN_MODE=k8s make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked.jsonl MODELS='uni_crawl:deepseek-r1-250528' MAX_WORKERS=4 make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl MODELS='openai:deepseek/deepseek-chat-v3-0324:free' MAX_WORKERS=20 RUN_MODE=k8s make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/sql_0604_checked.jsonl MODELS='openai:deepseek/deepseek-chat-v3-0324:free' MAX_WORKERS=4 make run-mcp-eval


INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked_4.jsonl CHECK_SCRIPT=/Users/<USER>/code/mcp-env/check_py/check.py MODELS='uni_crawl:qwen3-235b-a22b' CHECK_MODEL='uni_crawl:qwen3-235b-a22b' RUN_MODE=k8s MAX_WORKERS=4 make run-mcp-eval
INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked_4.jsonl CHECK_SCRIPT=/Users/<USER>/code/mcp-env/check_py/check.py MODELS='uni_crawl:qwen3-235b-a22b' CHECK_MODEL='uni_crawl:qwen3-235b-a22b' RUN_MODE=local MAX_WORKERS=4 make run-mcp-eval

INPUT_FILE=/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl CHECK_SCRIPT MODELS='uni_crawl:qwen3-235b-a22b' CHECK_MODEL='uni_crawl:qwen3-235b-a22b' RUN_MODE=k8s MAX_WORKERS=4 make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/tmp1/file_system_100_20250528_checked_4.jsonl' \
MODELS='uni_crawl:deepseek-v3' \
MCP_SERVERS='/Users/<USER>/code/tmp1/mcpservers.json' \
MAX_WORKERS=8 \
RUN_MODE=k8s \
make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/query/file_system_100_20250528_checked_4.jsonl' \
MODELS='qianfan:ernie-x1-turbo-32k' \
MAX_WORKERS=4 \
RUN_MODE=local \
make run-mcp-eval

INPUT_FILE='/Users/<USER>/code/query/file_system_100_20250528_checked_4.jsonl' \
MODELS='qianfan:ernie-x1-turbo-32k' \
MAX_WORKERS=4 \
RUN_MODE=local \
make run-mcp-eval

INPUT_FILE='/Users/<USER>/code/query/file_system_100_20250528_checked_4.jsonl' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=30 \
RUN_MODE=k8s \
make run-mcp-eval

INPUT_FILE='/Users/<USER>/code/query/file_system_100_20250528_checked.jsonl' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=30 \
RUN_MODE=local \
make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/query/sql_0604_checked.jsonl' \
MODELS='uni_crawl:deepseek-r1-0528' \
MAX_WORKERS=16 \
RUN_MODE=k8s \
make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/query/sql_0604_checked.jsonl' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=16 \
RUN_MODE=local \
make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/mcp-env/operator/output_file_system_100_20250528_checked_20250616_213741/file_system_100_20250528_checked.jsonl' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=10 \
RETRY_FAILED_ONLY=true \
RUN_MODE=k8s \
make run-mcp-eval


INPUT_FILE='/Users/<USER>/code/query/sql_0617_checked.jsonl' \
MODELS='uni_crawl:qwen3-235b-a22b' \
MAX_WORKERS=10 \
RUN_MODE=k8s \
make run-mcp-eval

INPUT_DIR='/Users/<USER>/code/query/hotel_06181900' \
MODELS='anquan:deepseek-v3' \
MAX_WORKERS=4 \
RETRY_FAILED_ONLY=false \
RUN_MODE=local \
make run-mcp-eval


INPUT_DIR='/Users/<USER>/code/query/hotel_06182100' \
MODELS='uni_crawl:deepseek-v3' \
MAX_WORKERS=8 \
RETRY_FAILED_ONLY=false \
RUN_MODE=k8s \
make run-mcp-eval

INPUT_FILE='/Users/<USER>/code/query/filesystem_100_20250620_checked.jsonl' \
MODELS='uni_crawl:deepseek-r1-0528,uni_crawl:doubao-seed-1-6-thinking-250615' \
MAX_WORKERS=2 \
RUN_MODE=local \
make run-mcp-eval

INPUT_FILE='/Users/<USER>/code/query/filesystem_100_20250620_checked.jsonl' \
MODELS='uni_crawl:deepseek-v3,uni_crawl:gpt-4.1-mini,uni_crawl:eval_crawl_pool_EB_SPV4_32K_DNI_VOC_test_0608v2_dpo0616v1p0618v1_ema_plath_ddf0b76fe235b3f0026065a9ca6b4239ec258384d8a3282779ee25d93dbfb397' \
MAX_WORKERS=3 \
RUN_MODE=local \
make run-mcp-eval


export DATAENG_INPUT_DATA_DIR=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/input
export DATAENG_OUTPUT_DIR=/Users/<USER>/code/baidu/dataeng/mcp-env/eggs/example/output